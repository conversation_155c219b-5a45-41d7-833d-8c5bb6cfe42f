<template>
  <div class="container">
    <img
      class="h-full w-full"
      :src="currentImage"
      @click="show = true" />
    <ImagePreview
      v-model:show="show"
      :images="[currentImage]"
      :close-on-click-image="false"
      :double-scale="true">
    </ImagePreview>
  </div>
</template>

<script setup>
  import { ImagePreview } from 'vant'
  import { computed, inject, ref } from 'vue'
  const images = import.meta.glob('./images/investment-map/*.png', {
    eager: true,
  })

  const currentType = inject('currentType')

  const currentImage = computed(() => {
    return images[`./images/investment-map/${currentType.value}.png`].default
  })
  const show = ref(false)
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 10px;
    background-color: #f7f8fa;
  }
</style>
