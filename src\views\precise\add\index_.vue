<template>
  <div
    ref="pageRef"
    class="h-full overflow-y-auto bg-[#f7f8fa] px-[16px]">
    <div class="form_title">企业基本信息</div>
    <div class="form_box">
      <RenderForm
        :options="renderFormOptions"
        :readonly="addStore.pageType === 'view'" />
    </div>
    <div class="record_list">
      <RecordList />
    </div>
    <div class="bottom_button">
      <Button
        v-if="addStore.pageType !== 'add'"
        block
        type="primary"
        @click="router.push(`/precise/history/${addStore.cardBid}`)">
        操作记录
      </Button>
      <Button
        block
        type="primary"
        @click="onButtonClick">
        {{ addStore.pageType === 'view' ? '编辑' : '保存' }}
      </Button>
    </div>
  </div>
</template>

<script setup>
  import {
    addFavInfo,
    getCompanyDetailByFav,
    getEntTypeList,
    getRecords,
    updateFavInfo,
  } from '@/apis/precise'
  import { getRecommendAllList } from '@/apis/recommend'
  import { useAddStore } from '@/stores/add'
  import { usePreciseStore } from '@/stores/percise'
  import { INDUSTRY_OPTIONS } from '@/views/cygk/options'
  import { useInfiniteScroll, useScroll } from '@vueuse/core'
  import { useRequest } from 'alova/client'
  import { Button, showToast } from 'vant'
  import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue'
  import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router'
  import RecordList from './components/RecordList.vue'
  import RenderForm from './components/RenderForm.vue'

  const preciseStore = usePreciseStore()
  const addStore = useAddStore()
  const route = useRoute()
  const router = useRouter()

  const uid = route.query.uid || preciseStore.uid
  const name = route.query.name || ''
  const key = route.query.key || ''
  const bid = route.query.bid || ''
  const cardBid = route.query.cardBid || ''

  const pageRef = useTemplateRef('pageRef')
  const { y } = useScroll(pageRef)

  const formData = ref({
    /** 企业名称 */
    companyName: '',
    /** 行业类别 */
    industryChain: '',
    /** 企业类型 */
    companyTypes: [],
    /** 企业联系人 */
    contactPersonName: '',
    /** 职务 */
    contactPersonJob: '',
    /** 联系方式 */
    contactPersonPhone: '',
    /** 信息来源 */
    label: '',
    /** 楚商 */
    jingchuMerchant: undefined,
    /** 楚商信息 */
    jingchuInfo: '',
  })

  const pageTypeMap = {
    查看企业: 'view',
    编辑企业: 'edit',
    添加企业: 'add',
  }

  const { send: fetchRecords } = useRequest(getRecords, {
    immediate: false,
  })
    .onSuccess(({ data }) => {
      addStore.records = data.list
      addStore.recordPaginate = {
        pageNum: data.page,
        hasNext: data.hasNext,
        pageSize: 10,
      }
      nextTick(() => {
        pageRef.value.scrollTo({
          top: addStore.scroll,
        })
      })
    })
    .onError(() => {
      addStore.recordPaginate.hasNext = false
    })

  const { send: fetchDetail } = useRequest(getCompanyDetailByFav, {
    immediate: false,
  }).onSuccess(({ data }) => {
    delete data.bid
    delete data.cardBid
    delete data.companyId
    formData.value = {
      ...data,
      companyTypes:
        data.companyTypes === '0'
          ? []
          : companyTypeOptions.value
              .filter(
                (item) =>
                  (parseInt(item.value) & parseInt(data.companyTypes)) ===
                  parseInt(item.value),
              )
              .map((i) => i.value.toString()),
    }
  })

  /** 初始化数据 */
  onMounted(() => {
    if (addStore.isFirst) {
      addStore.isFirst = false
      addStore.pageType = pageTypeMap[key]
      formData.value.companyName = name
      addStore.bid = bid
      addStore.cardBid = cardBid
      if (addStore.pageType !== 'add') {
        // 初始化跟进记录
        fetchRecords({
          page: {
            pageNum: 1,
            pageSize: 10,
          },
          params: {
            cardBid: cardBid,
          },
        })
        fetchDetail(bid)
      }
    } else {
      formData.value = addStore.formData
      if (addStore.pageType !== 'add') {
        addStore.records.splice(-10, 10)
        fetchRecords({
          page: {
            pageNum: addStore.recordPaginate.pageNum,
            pageSize: 10,
          },
          params: {
            cardBid: cardBid,
          },
        })
      }
    }
  })

  useInfiniteScroll(
    pageRef,
    () => {
      fetchRecords({
        page: {
          pageNum: addStore.recordPaginate.pageNum + 1,
          pageSize: 10,
        },
        params: {
          cardBid,
        },
      })
    },
    {
      distance: 30,
      canLoadMore() {
        return addStore.recordPaginate.hasNext && addStore.pageType !== 'add'
      },
    },
  )

  const companyTypeOptions = ref([])
  onMounted(async () => {
    try {
      const {
        data: { data: res },
      } = await getEntTypeList()
      companyTypeOptions.value = res
    } catch (error) {
      console.log(error)
    }
  })

  async function fetchCompanyList(keyword) {
    if (!keyword) return []
    try {
      const {
        data: { data: res },
      } = await getRecommendAllList({
        page: {
          pageNum: 1,
          pageSize: 100,
        },
        params: {
          companyName: keyword,
        },
      })
      return res.list
    } catch (error) {
      console.log(error)
      return []
    }
  }

  const renderFormOptions = computed(() => {
    return [
      {
        type: 'auto-complete',
        props: {
          label: '企业名称',
          fetchSuggestions: fetchCompanyList,
          valueKey: 'companyName',
          required: true,
          rules: [
            {
              required: true,
              message: '请填写企业名称',
            },
          ],
        },
        placeholder: '请填写企业名称',
        value: formData.value.companyName,
        events: {
          change(val) {
            formData.value.companyName = val
          },
        },
      },
      {
        type: 'action-sheet',
        name: 'industryChain',
        value: formData.value.industryChain,
        label: '行业类别',
        placeholder: '请选择行业类别',
        actions: INDUSTRY_OPTIONS.map((item) => ({
          name: item.text,
        })),
        events: {
          select(val) {
            formData.value.industryChain = val.name
          },
        },
      },
      {
        type: 'multiple',
        name: 'companyTypes',
        value: formData.value.companyTypes,
        label: '企业类别',
        placeholder: '请选择企业类别',
        options: companyTypeOptions.value,
        events: {
          confirm(val) {
            formData.value.companyTypes = val
          },
        },
      },
      {
        type: 'field',
        value: formData.value.contactPersonName,
        label: '联系人',
        placeholder: '请填写企业联系人',
        events: {
          change(val) {
            formData.value.contactPersonName = val
          },
        },
      },
      {
        type: 'field',
        value: formData.value.contactPersonJob,
        label: '职务',
        placeholder: '请填写职务',
        events: {
          change(val) {
            formData.value.contactPersonJob = val
          },
        },
      },
      {
        type: 'field',
        value: formData.value.contactPersonPhone,
        label: '联系方式',
        placeholder: '请填写企业联系方式',
        events: {
          change(val) {
            formData.value.contactPersonPhone = val
          },
        },
      },
      {
        type: 'action-sheet',
        value: formData.value.label,
        name: 'label',
        label: '信息来源',
        placeholder: '请选择信息来源',
        'cancel-text': '取消',
        'close-on-click-action': true,
        actions: [
          {
            name: '领导交办',
            value: '领导交办',
          },
          {
            name: '部门推荐',
            value: '部门推荐',
          },
          {
            name: '自我对接',
            value: '自我对接',
          },
          {
            name: '其他来源',
            value: '其他来源',
          },
        ],
        events: {
          select(val) {
            formData.value.label = val.value
          },
        },
      },
      {
        type: 'action-sheet',
        name: 'jingchuMerchant',
        value: formData.value.jingchuMerchant,
        label: '是否楚商',
        placeholder: '请选择',
        'cancel-text': '取消',
        'close-on-click-action': true,
        actions: [
          {
            name: '是',
            value: true,
          },
          {
            name: '否',
            value: false,
          },
        ],
        events: {
          select(val) {
            formData.value.jingchuMerchant = val.value
          },
        },
      },
      {
        type: 'textarea',
        name: 'jingchuInfo',
        value: formData.value.jingchuInfo,
        label: '楚商信息',
        placeholder: '请填写楚商信息',
        render: !!formData.value.jingchuMerchant,
        events: {
          change(val) {
            formData.value.jingchuInfo = val
          },
        },
      },
    ]
  })

  const { send: fetchUpdateItem } = useRequest(updateFavInfo, {
    immediate: false,
  }).onSuccess(() => {
    showToast('保存成功')
  })

  const { send: fetchAdd } = useRequest(addFavInfo, {
    immediate: false,
  }).onSuccess(() => {
    showToast('添加成功')
    router.back()
  })

  function onButtonClick() {
    if (addStore.pageType === 'view') {
      addStore.pageType = 'edit'
      document.title = '编辑企业'
    } else if (addStore.pageType === 'edit') {
      if (formData.value.companyName === '') {
        showToast('请填写企业名称')
        return
      }
      // 提交
      fetchUpdateItem({
        bid: cardBid,
        userBid: uid,
        companyName: formData.value.companyName,
        industryChain: formData.value.industryChain || '',
        companyTypes: formData.value.companyTypes.reduce(
          (total, current) => total + parseInt(current),
          0,
        ),
        contactPersonName: formData.value.contactPersonName || '',
        contactPersonJob: formData.value.contactPersonJob || '',
        contactPersonPhone: formData.value.contactPersonPhone || '',
        label: formData.value.label || '',
        jingchuMerchant: formData.value.jingchuMerchant || false,
        jingchuInfo: formData.value.jingchuInfo || '',
      })
    } else if (addStore.pageType === 'add') {
      fetchAdd({
        userBid: uid,
        companyName: formData.value.companyName,
        industryChain: formData.value.industryChain || '',
        companyTypes: formData.value.companyTypes.reduce(
          (total, current) => total + parseInt(current),
          0,
        ),
        contactPersonName: formData.value.contactPersonName || '',
        contactPersonJob: formData.value.contactPersonJob || '',
        contactPersonPhone: formData.value.contactPersonPhone || '',
        label: formData.value.label || '',
        jingchuMerchant: formData.value.jingchuMerchant || false,
        jingchuInfo: formData.value.jingchuInfo || '',
        records: addStore.records,
      })
    }
  }

  /** 根据路由跳转判断是否需要缓存数据 */
  onBeforeRouteLeave((to) => {
    if (to.path === '/precise/record' || to.path === '/precise/history') {
      addStore.formData = formData.value
      addStore.scroll = y.value
    } else {
      addStore.isFirst = true
      addStore.formData = {}
      addStore.records = []
      addStore.pageType = 'view'
      addStore.bid = ''
      addStore.cardBid = ''
      addStore.scroll = 0
      addStore.recordPaginate = {
        pageNum: 1,
        pageSize: 10,
        hasNext: false,
      }
    }
  })
</script>

<style lang="scss" scoped>
  .form_title {
    padding: 16px 0;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
  }

  .form_box {
    border-radius: 8px;
    background-color: #fff;
  }

  .record_list {
    margin-top: 32px;
  }

  .bottom_button {
    display: flex;
    position: sticky;
    bottom: 10px;
    margin-top: 20px;
    gap: 0 8px;
  }
</style>
