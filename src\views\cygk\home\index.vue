<template>
  <div class="main_page">
    <div class="img" />
    <div class="switch_tab">
      <Tabs>
        <Tab
          title="优势产业"
          name="优势产业">
          <div class="content">
            <div
              class="content_item"
              @click="goToIndustry('光芯屏端网')">
              <img
                src="@/assets/images/cygk/光芯屏端网.png"
                class="h-full w-full rounded-[12px]"
                alt="" />
              <span class="title">光芯屏端网</span>
            </div>
            <div
              class="content_item"
              @click="goToIndustry('汽车制造和服务')">
              <img
                src="@/assets/images/cygk/汽车制造和服务.jpeg"
                class="h-full w-full rounded-[12px]"
                alt="" />
              <span class="title">汽车制造和服务</span>
            </div>
            <div
              class="content_item"
              @click="goToIndustry('高端装备')">
              <img
                src="@/assets/images/cygk/高端装备.png"
                class="h-full w-full rounded-[12px]"
                alt="" />
              <span class="title">高端装备</span>
            </div>
          </div>
        </Tab>
        <Tab
          title="新兴产业"
          name="新兴产业">
          <div class="content">
            <div
              class="content_item"
              @click="goToIndustry('软件和网络安全')">
              <img
                src="@/assets/images/cygk/软件和网络安全.png"
                class="h-full w-full rounded-[12px]"
                alt="" />
              <span class="title">软件和网络安全</span>
            </div>
          </div>
        </Tab>
        <Tab
          title="未来产业"
          name="未来产业">
          <div class="content">
            <div
              class="content_item"
              @click="goToIndustry('量子科技')">
              <img
                src="@/assets/images/cygk/量子科技.png"
                class="h-full w-full rounded-[12px]"
                alt="" />
              <span class="title">量子科技</span>
            </div>
          </div>
        </Tab>
        <Tab
          title="现代服务业"
          name="现代服务业">
          <div class="content">
            <div
              class="content_item"
              @click="goToIndustry('数字创意')">
              <img
                src="@/assets/images/cygk/数字创意.jpeg"
                class="h-full w-full rounded-[12px]"
                alt="" />
              <span class="title">数字创意</span>
            </div>
          </div>
        </Tab>
      </Tabs>
    </div>
  </div>
</template>

<script setup>
  import { Tab, Tabs } from 'vant'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const goToIndustry = (key) => {
    router.push({
      path: '/cygk/industry',
      query: {
        key,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .main_page {
    width: 100%;
    height: 100%;

    .img {
      width: 100%;
      height: 126px;
      background-image: url('@/assets/images/cygk/home_top_bg.png');
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
    }

    .switch_tab {
      width: 343px;
      margin: 0 auto;
      margin-top: -100px;
      padding: 12px 8px;
      border-radius: 12px;
      background-color: #fff;

      .content {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-top: 12px;

        &_item {
          position: relative;
          width: 320px;
          height: 135px;
          margin: 0 auto;
          border-radius: 12px;

          .title {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 24px;
            padding-left: 8px;
            border-radius: 0 0 12px 12px;
            background-color: rgba($color: #000, $alpha: 50%);
            color: #fff;
            font-weight: bold;
            font-size: 16px;
          }
        }
      }
    }
  }
</style>
