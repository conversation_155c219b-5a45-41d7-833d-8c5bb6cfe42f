<template>
  <div class="contains">
    <!-- <p class="title">产业载体</p> -->
    <div class="cards">
      <div
        class="card"
        @click="onJump('building')">
        <img
          src="@/assets/images/cygk/louyu.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">楼宇</p>
        <!-- <p class="text-[#1255E4]">({{ config.building }}个)</p> -->
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
      <div
        class="card"
        @click="onJump('park')">
        <img
          src="@/assets/images/cygk/yuanqu.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">园区</p>
        <!-- <p class="text-[#1255E4]">({{ config.park }}个)</p> -->
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
    </div>
    <div class="cards">
      <div
        class="card"
        @click="onJump('scene')">
        <img
          src="@/assets/images/cygk/yingyongchangjing.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">应用场景</p>
        <!-- <p class="text-[#1255E4]">({{ config.scene }}个)</p> -->
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
      <div
        class="card"
        @click="onJump('resources')">
        <img
          src="@/assets/images/cygk/association.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">创新资源</p>
        <!-- <p class="text-[#1255E4]"
          >(
          {{
            config.colleges +
            config.research +
            config.innovate +
            config.association
          }}
          个)</p
        > -->
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
    </div>
    <!-- <div class="cards">
      <div
        class="card"
        @click="onJump('scene')">
        <img
          src="@/assets/images/cygk/yingyongchangjing.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">应用场景</p>
        <p class="text-[#1255E4]">({{ config.scene }}个)</p>
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
      <div class="flex-1"></div>
      <div class="flex-1"></div>
    </div> -->
    <!-- <div class="cards">
      <div
        class="card"
        @click="onJump('research')">
        <img
          src="@/assets/images/cygk/yuanqu.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">科研院所</p>
        <p class="text-[#1255E4]">({{ config.research }}个)</p>
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
      <div
        class="card"
        @click="onJump('innovate')">
        <img
          src="@/assets/images/cygk/yingyongchangjing.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">功能平台</p>
        <p class="text-[#1255E4]">({{ config.innovate }}个)</p>
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
    </div> -->
    <!-- <div class="cards">
      <div
        class="card"
        @click="onJump('association')">
        <img
          src="@/assets/images/cygk/association.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">协会联盟</p>
        <p class="text-[#1255E4]">({{ config.association }}个)</p>
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
      <div
        class="card"
        @click="onJump('scene')">
        <img
          src="@/assets/images/cygk/yingyongchangjing.png"
          class="h-[60px] w-[60px]"
          alt="" />
        <p class="card_title">应用场景</p>
        <p class="text-[#1255E4]">({{ config.scene }}个)</p>
        <img
          class="right_circle"
          src="@/assets/images/cygk/RightCircle.png"
          alt="" />
      </div>
    </div> -->
    <!-- <img
        class="absolute bottom-0 left-[50%] h-[220px] w-[375px] translate-x-[-50%]"
        src="@/assets/images/cygk/foot.png"
        alt="" /> -->
  </div>
</template>

<script setup>
  import * as apis from '@/apis/index'
  import { onMounted, reactive } from 'vue'
  import { useRouter } from 'vue-router'
  const router = useRouter()
  const config = reactive({
    building: 0,
    park: 0,
    scene: 0,
    colleges: 0,
    research: 0,
    innovate: 0,
    association: 0,
  })
  const onJump = (type) => {
    if (type == 'resources') {
      router.push({
        path: '/cygk/innovation-resources',
      })
    } else {
      router.push({
        path: '/cygk/introduce-info',
        query: {
          type,
          key: {
            building: '楼宇',
            park: '园区',
            scene: '应用场景',
            colleges: '高等院校',
            research: '科研院所',
            innovate: '功能平台',
            association: '协会联盟',
          }[type],
        },
      })
    }
  }
  const getBuildingTotal = async () => {
    try {
      const res = await apis.getAllPageList('building')
      config.building = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }
  const getSchoolTotal = async () => {
    try {
      const res = await apis.getAllPageList('colleges')
      config.colleges = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }
  const getResearchTotal = async () => {
    try {
      const res = await apis.getAllPageList('research')
      config.research = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }
  const getInnovateTotal = async () => {
    try {
      const res = await apis.getAllPageList('innovate')
      config.innovate = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }

  const getParkTotal = async () => {
    try {
      const res = await apis.getAllPageList('park')
      config.park = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }
  const getSceneTotal = async () => {
    try {
      const res = await apis.getAllPageList('scene')
      config.scene = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }
  const getAssociationTotal = async () => {
    try {
      const res = await apis.getAllPageList('association')
      config.association = res.data.data.length
    } catch (error) {
      console.log(error)
    }
  }

  onMounted(() => {
    getBuildingTotal()
    getParkTotal()
    getSchoolTotal()
    getSceneTotal()
    getResearchTotal()
    getInnovateTotal()
    getAssociationTotal()
  })
</script>

<style lang="scss" scoped>
  .contains {
    width: 100%;
    height: 100%;
    padding: 16px;
    background-image: url('@/assets/images/cygk/foot.png');
    background-position: bottom;
    background-repeat: no-repeat;

    // background-color: #fff;

    .title {
      font-weight: bold;
      font-size: 30px;
    }

    .cards {
      display: flex;
      gap: 16px;
      justify-content: space-between;
      width: 100%;

      .card {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        width: 160px;
        height: 160px;

        // height: 100%;
        padding: 16px 0;
        border-radius: 12px;
        background-color: #fff;
        box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%);

        &_title {
          margin-top: 16px;
          font-weight: bold;
          font-size: 16px;
        }

        .right_circle {
          margin-top: 6px;
        }
      }

      & + .cards {
        margin-top: 20px;
      }
    }
  }
</style>
