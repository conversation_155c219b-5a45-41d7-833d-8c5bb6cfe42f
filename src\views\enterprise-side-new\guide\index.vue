<template>
  <div class="bg-[#F7F8FA] w-full h-full p-[16px]">
    <div
      v-for="(item, index) in list"
      :key="index"
      class="card">
      <div
        class="flex items-center justify-center rounded-[8px] w-[36px] h-[36px] flex-shrink-0"
        :style="getLinear(item.bg)">
        <i
          class="iconfont text-[20px] text-white"
          :class="item.icon"></i>
      </div>
      <div>
        <div class="font-[600]">{{ item.title }}</div>
        <div class="mt-[4px] text-[14px] text-[rgba(0,0,0,0.6)] leading-[22px]">
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  const list = [
    {
      icon: 'icon-rk',
      bg: ['#71DBFD', '#41AFFF'],
      title: '政务端入口',
      content:
        '进入“投资武汉”公众号，在底部操作栏点击“数智招商”按钮，点击“政务端”，即可进入政务端。',
    },
    {
      icon: 'icon-lunbo',
      bg: ['#74A8FE', '#547AF7'],
      title: '轮播图',
      content: '点击数据图可跳转到对应的数据页面。',
    },
    {
      icon: 'icon-yxwh-1',
      bg: ['#FFA878', '#F77C54'],
      title: '印象武汉',
      content: '查看武汉简介、武汉景点、武汉美食、武汉文化相关信息。',
    },
    {
      icon: 'icon-tzjh-1',
      bg: ['#71DBFD', '#41AFFF'],
      title: '投资机会',
      content: '查看投资政策、产业园区、商务楼宇等招商资源信息。',
    },
    {
      icon: 'icon-znlkf',
      bg: ['#FFCA65', '#F6AB52'],
      title: '智能客服',
      content: '接人deepseek大模型响应用户需求。',
    },
    {
      icon: 'icon-wyzx',
      bg: ['#2BF4C3', '#78F1D9'],
      title: '我要咨询',
      content: '提供武汉市招商投资电话，快速解答企业招商相关疑问。',
    },
  ]

  function getLinear(colors) {
    return {
      background: `linear-gradient(${colors.join(',')})`,
    }
  }
</script>

<style lang="scss" scoped>
  .card {
    display: flex;
    gap: 0 16px;
    padding: 16px;
    border-radius: 12px;
    background-color: #fff;

    & + .card {
      margin-top: 16px;
    }
  }
</style>
