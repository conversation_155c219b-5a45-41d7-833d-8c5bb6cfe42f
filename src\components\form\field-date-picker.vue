<template>
  <Field
    v-model="displayValue"
    :is-link="!readonly"
    :readonly="readonly"
    :name="name"
    :label="label"
    :placeholder="placeholder"
    :rules="rules"
    @click="showPicker = true" />
  <Popup
    v-if="!readonly"
    v-model:show="showPicker"
    destroy-on-close
    position="bottom">
    <DatePicker
      :model-value="pickerValue"
      @confirm="onConfirm"
      @cancel="showPicker = false" />
  </Popup>
</template>

<script setup>
  import dayjs from 'dayjs'
  import customParseFormat from 'dayjs/plugin/customParseFormat'
  import { computed, ref, watch } from 'vue'
  import { Field, Popup, DatePicker } from 'vant'

  // 启用dayjs自定义解析格式插件
  dayjs.extend(customParseFormat)

  // 定义组件名称
  defineOptions({
    name: 'DatePickerField',
  })

  // 定义props
  const props = defineProps({
    // 输出格式
    valueFormat: {
      type: String,
      default: 'YYYY-MM-DD',
    },
    // 显示格式
    displayFormat: {
      type: String,
      default: 'YYYY年MM月DD日',
    },
    // 表单字段名
    name: {
      type: String,
      default: 'datePicker',
    },
    // 标签
    label: {
      type: String,
      default: '日期选择',
    },
    // 占位符
    placeholder: {
      type: String,
      default: '点击选择日期',
    },
    rules: {
      type: Array,
      default: () => [],
    },
    readonly: Boolean,
  })

  // 所选中的选项的值
  const dateValue = defineModel({
    type: [String, Array, Date],
    required: true,
  })

  // 定义emits
  const emit = defineEmits(['change'])

  // 响应式数据
  const showPicker = ref(false)
  const pickerValue = ref([])

  // 解析日期为dayjs对象
  const parseDate = (dateInput) => {
    if (!dateInput) return null

    // 如果已经是Date对象
    if (dateInput instanceof Date) {
      return dayjs(dateInput)
    }

    // 如果是数组格式 ['2021', '01', '01']
    if (Array.isArray(dateInput)) {
      const [year, month, day] = dateInput
      return dayjs(`${year}-${month}-${day}`, 'YYYY-MM-DD')
    }

    // 处理字符串格式
    let dateStr = String(dateInput).trim()

    // 处理中文格式：2010年10月10日
    if (
      dateStr.includes('年') &&
      dateStr.includes('月') &&
      dateStr.includes('日')
    ) {
      return dayjs(dateStr, 'YYYY年MM月DD日')
    }

    // 处理斜杠格式：2010/10/10
    if (dateStr.includes('/')) {
      return dayjs(dateStr, 'YYYY/MM/DD')
    }

    // 处理横杠格式：2010-10-10
    if (dateStr.includes('-')) {
      return dayjs(dateStr, 'YYYY-MM-DD')
    }

    // 其他格式尝试自动解析
    const parsed = dayjs(dateStr)
    return parsed.isValid() ? parsed : null
  }

  // 将dayjs对象转换为vant要求的数组格式
  const dayjsToArray = (dayjsObj) => {
    if (!dayjsObj || !dayjsObj.isValid()) return []

    return [
      dayjsObj.format('YYYY'),
      dayjsObj.format('MM'),
      dayjsObj.format('DD'),
    ]
  }

  // 将vant数组格式转换为dayjs对象
  const arrayToDayjs = (arr) => {
    if (!arr || !Array.isArray(arr) || arr.length < 3) return null

    const [year, month, day] = arr
    const dayjsObj = dayjs(`${year}-${month}-${day}`, 'YYYY-MM-DD')
    return dayjsObj.isValid() ? dayjsObj : null
  }

  // 显示值
  const displayValue = computed(() => {
    if (!pickerValue.value || pickerValue.value.length === 0) return ''

    const dayjsObj = arrayToDayjs(pickerValue.value)
    if (!dayjsObj) return ''

    return dayjsObj.format(props.displayFormat)
  })

  // 确认选择
  const onConfirm = ({ selectedValues }) => {
    pickerValue.value = selectedValues
    showPicker.value = false

    // 生成格式化后的值
    const dayjsObj = arrayToDayjs(selectedValues)
    if (!dayjsObj) return

    const formattedValue = dayjsObj.format(props.valueFormat)
    // 更新v-model绑定的值
    dateValue.value = formattedValue

    emit('change', {
      value: formattedValue,
      date: dayjsObj.toDate(),
      dayjs: dayjsObj,
      array: selectedValues,
    })
  }

  // 监听dateValue变化，更新pickerValue
  watch(
    dateValue,
    (newValue) => {
      if (newValue) {
        const dayjsObj = parseDate(newValue)
        if (dayjsObj && dayjsObj.isValid()) {
          pickerValue.value = dayjsToArray(dayjsObj)
        }
      } else {
        pickerValue.value = []
      }
    },
    { immediate: true },
  )
</script>

<style scoped>
  /* 如果需要自定义样式可以在这里添加 */
</style>
