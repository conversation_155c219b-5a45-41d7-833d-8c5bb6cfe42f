<template>
  <div class="wuhan-info-container">
    <div class="image-container">
      <img
        :src="cityData.imageUrl"
        :alt="cityData.title" />
    </div>

    <h1 class="headTitle">{{ cityData.title }}</h1>

    <div class="info-list">
      <div
        v-for="(item, index) in cityData.infoItems"
        :key="index"
        class="info-item">
        <div class="icon">
          <img
            :src="edgeFrame"
            alt="Icon Frame"
            class="icon-frame" />
          <img
            :src="item.innerIconUrl"
            :alt="item.title"
            class="icon-inner" />
        </div>
        <div class="content">
          <div class="content-title">{{ item.title }}</div>
          <div class="content-subtitle">{{ item.subtitle }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import banner from '@/assets/images/enterprise-side/banner.png'
  import edge from '@/assets/images/enterprise-side/edge.png'
  // 1. 导入你需要的内部图标
  import shipIcon from '@/assets/images/enterprise-side/gjbl.png' // 示例：轮船图标
  import railwayIcon from '@/assets/images/enterprise-side/gt.png' // 高铁
  import planeIcon from '@/assets/images/enterprise-side/jc.png' // 示例：飞机图标
  import sxhslIcon from '@/assets/images/enterprise-side/sxhsl.png' // 双循环
  import trainIcon from '@/assets/images/enterprise-side/zobl.png' // 铁路

  // planeIcon, shipIcon, railwayIcon, sxhslIcon, trainIcon

  import { ref } from 'vue'

  // 将边框图片赋值给一个变量，方便在模板中使用
  const edgeFrame = edge

  const cityData = ref({
    imageUrl: banner,
    title: '区位交通',
    // 2. 更新 infoItems 数据结构，添加 innerIconUrl
    infoItems: [
      {
        innerIconUrl: sxhslIcon, // 使用导入的 sxhsl.png 图标
        title: '国内国际双循环枢纽城市',
        subtitle:
          "Wuhan serves as a hub in China's domestic and international circulations",
      },
      {
        innerIconUrl: trainIcon, // 使用导入的火车图标
        title: '中欧班列覆盖亚欧大陆40个国家119个城市',
        subtitle:
          'The China Railway Express links 119 cities across 40 countries on the Eurasian continent',
      },
      {
        innerIconUrl: railwayIcon, // 使用导入的铁路图标
        title: '4小时高铁圈，覆盖中国80%的重要城市，近10亿人口，约90%经济总量',
        subtitle:
          "The 4-hour high-speed rail circle covers 80% of China's major cities , serving nearly 1 billion people and representing about 90% of the country's economic aggregate",
      },
      {
        innerIconUrl: shipIcon, // 使用导入的轮船图标
        title: '江海直达航线接驳远洋国际班轮可达世界各地，中西部最佳出海口',
        subtitle:
          'The river-sea through shipping routeconnected to international ocean linersenables ships departing from Wuhan toreach every corner of the world,makingthe city the best gateway in central andwestern China',
      },
      {
        innerIconUrl: planeIcon, // 使用导入的飞机图标
        title:
          '天河机场与亚洲唯一的国际专业货运机场鄂州花湖国际机场联手打造内陆开放空中出海口',
        subtitle:
          'Wuhan Tianhe international Airport and Ezhou Huahu Airport (the only international professional cargo airport in Asia) have partnered to establish an air gateway for inland opening-up',
      },
    ],
  })
</script>

<style scoped>
  .wuhan-info-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    background-image: url('@/assets/images/enterprise-side/whBackgrordImg.png');
    background-position: center;
    background-repeat: repeat;
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  }

  .image-container {
    overflow: hidden;
    width: 100%;
    height: 200px;
  }

  .image-container img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .headTitle {
    margin: 0;
    padding-left: 16px;
    color: rgb(159 96 0 / 100%);
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
  }

  .info-list {
    display: flex;
    flex-direction: column;
    padding: 8px 24px;
  }

  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    padding: 8px 0; /* 调整 padding */
    border-bottom: 1px solid #eee;
  }

  /* 4. 修改 Icon 样式 */
  .icon {
    position: relative; /* 设置为相对定位，作为内部绝对定位元素的基准 */
    display: flex; /* 可选，如果内部图片也需要flex布局 */
    flex-shrink: 0;
    justify-content: center; /* 可选 */
    align-items: center; /* 可选 */
    width: 40px;
    height: 40px;
    margin-right: 15px;
  }

  .icon img {
    /* 通用样式，如果需要区分可以移除或修改 */
    display: block; /* 防止图片下方有额外空隙 */
  }

  .icon-frame {
    position: absolute; /* 相对于 .icon 定位 */
    top: 0;
    left: 0;
    z-index: 1; /* 确保边框在底层 (如果需要明确层级) */
    object-fit: contain; /* 或 fill/cover，根据边框图实际情况调整 */
    width: 100%;
    height: 100%;
  }

  .icon-inner {
    position: absolute; /* 相对于 .icon 定位 */
    top: 20%; /* (100% - 60%) / 2 = 20% */
    left: 20%; /* (100% - 60%) / 2 = 20% */
    z-index: 2; /* 确保内部图标在上面 */

    /* 示例 2: 填充，但可能有内边距 */

    /* width: 100%;
  height: 100%;
  padding: 5px; /* 添加内边距让图标不紧贴边框 */

    /* box-sizing: border-box; /* 让 padding 包含在 width/height 内 */

    object-fit: contain; /* 保持内部图标的比例 */

    /* --- 调整内部图标的大小和位置 --- */

    /* 示例 1: 稍微缩小并居中 */
    width: 60%; /* 例如，设置为父容器的 60% */
    height: 60%; /* 例如，设置为父容器的 60% */
  }

  /* --- End of Icon Style Modifications --- */

  .content {
    flex-grow: 1;
  }

  .content-title {
    margin-bottom: 5px;
    color: rgb(159 96 0 / 100%);
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: left;
  }

  .content-subtitle {
    margin-bottom: 5px;
    color: rgb(0 0 0 / 40%);
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    text-align: left;
  }
</style>
