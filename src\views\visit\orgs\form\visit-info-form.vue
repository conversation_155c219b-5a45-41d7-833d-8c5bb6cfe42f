<template>
  <div class="page">
    <Form
      validate-trigger="onSubmit"
      :readonly="isDetail"
      @submit="onSubmit">
      <div class="group">
        <div class="group_header">
          <h1 class="header_title">基本信息</h1>
        </div>
        <CellGroup inset>
          <FieldDatePicker
            v-model="formData.visitDate"
            :readonly="isDetail"
            label="走访日期"
            name="visitDate"
            :rules="[
              {
                required: true,
                message: '请选择走访日期',
              },
            ]" />
          <Field
            v-model="formData.visitPerson"
            name="visitPerson"
            label="走访人"
            placeholder="请输入走访人"
            :rules="[
              {
                required: true,
                message: '请输入走访人',
              },
            ]" />
          <Field
            v-model="formData.validInformation"
            name="validInformation"
            rows="2"
            autosize
            label="招商有效信息"
            type="textarea"
            placeholder="请输入招商有效信息"
            :rules="[
              {
                required: true,
                message: '请输入招商有效信息',
              },
            ]" />
          <Field
            v-model="formData.schedule"
            name="schedule"
            rows="2"
            autosize
            label="后续计划安排"
            type="textarea"
            placeholder="请输入后续计划安排"
            :rules="[
              {
                required: true,
                message: '请输入后续计划安排',
              },
            ]" />
          <FieldUpload
            v-model="attachmentUrl"
            preview-path="/visit/file-view"
            :readonly="isDetail"
            name="attachmentUrl"
            :max-size="10"
            label="附件上传" />
        </CellGroup>
      </div>
      <div class="group">
        <div class="group_header">
          <h1 class="header_title">对接人</h1>
          <div
            v-if="!isDetail"
            class="header_action">
            <i class="iconfont icon-PlusCircleOutlined1"></i>
            <span @click="addDocking">添加对接人</span>
          </div>
        </div>
        <!-- 对接人表单列表 -->
        <template v-if="formData.dockingPeopleList.length > 0">
          <div v-if="!isDetail">
            <Space
              :size="16"
              direction="vertical"
              fill>
              <DockingForm
                v-for="(item, index) in formData.dockingPeopleList"
                :key="item.bid || item.c_bid"
                v-model="formData.dockingPeopleList[index]"
                :index="index"
                @on-delete="
                  () => formData.dockingPeopleList.splice(index, 1)
                " />
            </Space>
          </div>
          <!-- 详情列表 -->
          <div
            v-else
            class="pl-[16px] pr-[16px]">
            <DockingList :list="formData.dockingPeopleList" />
          </div>
        </template>
        <Empty
          v-else
          image-size="8rem"
          description="暂无对接人数据" />
      </div>

      <div style="margin: 16px">
        <Button
          v-if="!isDetail"
          class="bottom_btn"
          block
          type="primary"
          native-type="submit">
          提交
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup>
  import FieldDatePicker from '@/components/form/field-date-picker.vue'
  import FieldUpload from '@/components/form/field-upload.vue'
  import { useVisitOrgFormStore, useVisitUserStore } from '@/stores/visit'
  import { getFileNameFromUrl } from '@/utils/file'
  import { isEmpty } from '@/utils/is'
  import { nanoid } from 'nanoid/non-secure'
  import { Button, CellGroup, Empty, Field, Form, Space } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import DockingForm from './components/docking-form.vue'
  import DockingList from './components/docking-list.vue'

  const route = useRoute()
  const router = useRouter()
  // 编辑联系人会带来当前联系人索引
  const itemIndex = !isEmpty(route.params.index)
    ? Number(route.params.index)
    : -1
  // 是否是详情模式
  const isDetail = route.meta?.status === 'detail'

  // 走访信息表单数据
  const formData = ref({
    visitDate: '',
    visitPerson: '',
    validInformation: '',
    schedule: '',
    attachmentUrl: [
      // 'https://oss.youpin-k8s.net/shared-space/2025-06-04/73056757b29846bca7f5aba819e72c45/avatar.jpg',
    ],
    sourceNickname: '',
    dockingPeopleList: [
      // {
      //   c_bid: 'xxx',
      //   name: '',
      //   contactInformation: '',
      //   post: '',
      // },
    ],
  })

  // 附件上传组件双向绑定数据格式转换
  const attachmentUrl = computed({
    get() {
      return formData.value.attachmentUrl.map((url) => {
        return {
          file: {
            name: getFileNameFromUrl(url),
          },
          url: url,
        }
      })
    },
    set(value) {
      formData.value.attachmentUrl = value.map((item) => {
        return item.url
      })
    },
  })

  // 添加联系人
  function addDocking() {
    // 添加对接人表单数据
    formData.value.dockingPeopleList.push({
      c_bid: nanoid(5),
      name: '',
      contactInformation: '',
      post: '',
    })
  }

  const userStore = useVisitUserStore()
  const store = useVisitOrgFormStore()
  function onSubmit(values) {
    const submitData = {
      ...values,
      ...formData.value,
      sourceNickname: userStore.userInfo?.nickname || '',
    }

    // 编辑模式
    if (itemIndex !== -1) {
      store.formData.visitBasicInformationDTOList[itemIndex] = submitData
      // 新增模式
    } else {
      store.formData.visitBasicInformationDTOList.push(submitData)
    }
    goBack()
  }

  function goBack() {
    router.back()
  }

  onMounted(() => {
    // 编辑模式时，拿到详情填充表单数据
    if (itemIndex !== -1) {
      const data = store.formData.visitBasicInformationDTOList[itemIndex]
      formData.value = {
        ...data,
      }
    }
  })
</script>

<style lang="scss" scoped>
  .page {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 20px 0;
    overflow-y: auto;
    background-color: #f7f8fa;

    .group {
      .group_header {
        display: flex;
        justify-content: space-between;
        padding: 0 16px 16px 16px;
        .header_title {
          color: #969799;
          font-size: 14px;
          line-height: 20px;
          text-align: left;
        }

        .header_action {
          display: flex;
          align-items: center;
          gap: 5px;
          color: #1989fa;
          font-size: 14px;
        }
      }

      & + .group {
        margin-top: 32px;
      }
    }

    // .bottom_btn {
    //   position: fixed;
    //   right: 16px;
    //   bottom: 16px;
    //   left: 20px;
    //   width: 90%;
    // }
  }
</style>
