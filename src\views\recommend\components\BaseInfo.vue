<template>
  <div class="base-info">
    <div class="tag-box">
      <!-- <div
        class="tag"
        @click="router.push('/shareholder')"
        >股东：0
      </div>
      <div
        class="tag"
        @click="router.push('/manager')"
        >高管：0
      </div>
      <div
        class="tag"
        @click="showToast('暂无数据')">
        分支机构：0
      </div>
      <div
        class="tag"
        @click="showToast('暂无数据')">
        工商变更：0
      </div> -->
      <!-- <div class="tag">股东：0 </div>
      <div class="tag">高管：0 </div>
      <div class="tag"> 分支机构：0 </div>
      <div class="tag"> 工商变更：0 </div> -->
    </div>
    <div class="info">
      <div class="row">
        <div class="label">法定代表人</div>
        <div class="value">{{ info.companyBasicInfoVo?.operName }}</div>
      </div>
      <div class="row">
        <div class="label">注册地址</div>
        <div class="value">{{ info.companyBasicInfoVo?.address }}</div>
      </div>
      <div class="row">
        <div class="label">统一社会信用代码</div>
        <div class="value">{{ info.companyBasicInfoVo?.creditCode }}</div>
      </div>
      <div class="row">
        <div class="label">工商注册号</div>
        <div class="value">{{ info.companyBasicInfoVo?.no }}</div>
      </div>
      <div class="row">
        <div class="label">公司类型</div>
        <div class="value">{{ info.companyBasicInfoVo?.econKind }}</div>
      </div>
      <div class="row">
        <div class="label">经营状态</div>
        <div class="value">{{ info.companyBasicInfoVo?.status }}</div>
      </div>
      <div class="row">
        <div class="label">成立日期</div>
        <div class="value">{{
          dayjs(info.companyBasicInfoVo?.startDate).format('YYYY-MM-DD')
        }}</div>
      </div>
      <div class="row">
        <div class="label">注册资本</div>
        <div class="value">{{
          parseInt(info.companyBasicInfoVo?.registCapi) + '万'
        }}</div>
      </div>
      <!-- <div class="row">
        <div class="label">人员规模：</div>
        <div class="value">2615人</div>
      </div> -->
      <div class="row">
        <div class="label">联系方式</div>
        <div class="value">{{ info.companyBasicInfoVo?.phoneNumber }}</div>
      </div>
      <!-- <div class="row">
        <div class="label">主营产品：</div>
        <div class="value">半导体集成电路、芯片封装测试</div>
      </div> -->
      <div class="row">
        <div class="label">经营范围</div>
        <div class="value">
          {{ info.companyBasicInfoVo?.scope }}
        </div>
      </div>
      <!-- <div class="row">
        <div class="label">所属产业链：</div>
        <div class="value">
          “光芯屏端网”新一代信息技术产业、汽车制造和服务(含氢能)产业
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup>
  import dayjs from 'dayjs'

  // import { useRouter } from 'vue-router'

  // const router = useRouter()

  defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })
</script>

<style lang="scss" scoped>
  .base-info {
    margin-top: 16px;

    .tag-box {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 16px;

      .tag {
        height: 24px;
        padding: 0 8px;
        border-radius: 6px;
        background: rgb(18 85 228 / 10%);
        color: #1255e4;
        font-size: 12px;
        line-height: 24px;
      }
    }

    .info {
      margin-top: 16px;

      .row {
        display: flex;
        margin-bottom: 10px;
        font-size: 14px;

        .label {
          flex-shrink: 0;
          width: 72px;
          margin-right: 8px;
          color: rgb(0 0 0 / 60%);
          word-break: break-all;
        }

        .value {
          color: rgb(0 0 0 / 90%);
        }
      }
    }
  }
</style>
