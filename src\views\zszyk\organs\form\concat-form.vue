<template>
  <div class="page">
    <Form
      validate-trigger="onSubmit"
      :readonly="isDetail"
      @submit="onSubmit">
      <CellGroup inset>
        <Field
          v-model="formData.name"
          name="name"
          label="联系人"
          placeholder="请输入联系人"
          :rules="[
            {
              required: true,
              message: '请输入联系人',
              trigger: 'onSubmit',
            },
          ]" />
        <Field
          :model-value="sexPickerValue.label"
          :is-link="!isDetail"
          readonly
          name="sex"
          label="性别"
          placeholder="点击选择性别"
          @click="showPicker = true" />
        <Popup
          v-if="!isDetail"
          v-model:show="showPicker"
          destroy-on-close
          position="bottom">
          <Picker
            :columns="sexPickerOptions"
            :model-value="sexPickerValue.pValue"
            @confirm="onSexPickerConfirm"
            @cancel="showPicker = false" />
        </Popup>
        <Field
          v-model="formData.post"
          name="post"
          label="职务"
          placeholder="请输入职务" />
        <Field
          v-model="formData.phone"
          name="phone"
          label="联系电话"
          placeholder="请输入联系电话"
          :rules="[
            {
              required: true,
              message: '请输入联系电话',
              trigger: 'onSubmit',
            },
          ]" />
        <Field
          v-model="formData.introduction"
          rows="2"
          autosize
          label="简介"
          type="textarea"
          placeholder="请输入简介" />
      </CellGroup>

      <Button
        v-if="!isDetail"
        class="bottom_btn"
        block
        type="primary"
        native-type="submit">
        提交
      </Button>
    </Form>
  </div>
</template>

<script setup>
  import { useOrgFormStore } from '@/stores/zszyk'
  import { isEmpty } from '@/utils/is'
  import { Button, CellGroup, Field, Form, Picker, Popup } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const route = useRoute()
  const router = useRouter()
  // 编辑联系人会带来当前联系人索引
  const itemIndex = !isEmpty(route.params.index)
    ? Number(route.params.index)
    : -1
  // 是否是详情模式
  const isDetail = route.meta?.status === 'detail'

  const showPicker = ref(false)

  const formData = ref({
    name: '',
    sex: '', // _0:女_1:男
    post: '',
    phone: '',
    introduction: '',
  })

  /**
   * {
        "selectedValues": [
            1
        ],
        "selectedOptions": [
            {
                "text": "男",
                "value": 1
            }
        ],
        "selectedIndexes": [
            0
        ]
    }
   * @param value
   */
  function onSexPickerConfirm({ selectedValues }) {
    sexPickerValue.value = selectedValues
    showPicker.value = false
  }
  const sexPickerOptions = [
    { text: '女', value: 0 },
    { text: '男', value: 1 },
  ]
  const sexPickerValue = computed({
    get() {
      const sex = formData.value.sex
      if (isEmpty(formData.value.sex)) {
        return []
      }
      const label =
        sexPickerOptions.find((item) => item.value === sex)?.text || ''
      // 返回的是选中项value的数组
      return {
        pValue: [sex],
        label,
      }
    },
    set(value) {
      formData.value.sex = value[0]
    },
  })

  const store = useOrgFormStore()
  function onSubmit(values) {
    if (itemIndex !== -1) {
      store.formData.contactDTOList[itemIndex] = {
        ...values,
        ...formData.value,
      }
    } else {
      store.formData.contactDTOList.push({
        ...values,
        ...formData.value,
      })
    }
    goBack()
  }

  function goBack() {
    router.back()
  }

  onMounted(() => {
    if (itemIndex !== -1) {
      const contact = store.formData.contactDTOList[itemIndex]
      formData.value = {
        ...contact,
      }
    }
  })
</script>

<style lang="scss" scoped>
  .page {
    position: relative;
    width: 100%;
    height: 100%;
    padding: 20px 0;
    overflow-y: auto;
    background-color: #f7f8fa;

    .bottom_btn {
      position: absolute;
      right: 16px;
      bottom: 16px;
      left: 20px;
      width: 90%;
    }
  }
</style>
