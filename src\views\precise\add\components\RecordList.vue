<template>
  <div class="record_list_box">
    <div class="list_header">
      <div class="gray_text">跟进记录</div>
      <div
        v-if="addStore.pageType !== 'view'"
        class="text-[#1255E4]"
        @click="jumpAddRecord">
        <i class="iconfont icon-PlusCircleOutlined1 text-[#1255e4]!" />
        添加跟进记录
      </div>
    </div>
    <div class="list">
      <div
        v-for="(item, index) in addStore.getRecords"
        :key="item.bid || index"
        class="list_item">
        <div class="item_line item_header">
          <div class="item_date">{{ item.communicationDate }}</div>
          <div
            v-if="item.createdByName"
            class="item_person">
            <span>记录添加人：</span>
            <span>{{ item.createdByName }}</span>
          </div>
        </div>
        <div class="item_line">
          <div class="item_label">接待人</div>
          <div class="item_value">{{ item.receptionists }}</div>
        </div>
        <div
          v-if="item.clients.length > 0"
          class="item_line">
          <div class="item_label">对接人</div>
          <div class="item_value">
            <span>{{ item.clients[0]?.name || '' }}&nbsp;</span>
            <span>{{ item.clients[0]?.phone || '' }}&nbsp;</span>
            <span>{{ item.clients[0]?.job || '' }}&nbsp;</span>
          </div>
        </div>
        <div class="item_line item_footer">
          <div
            class="footer_bottom"
            @click="toRecordItem('view', item, index)">
            查看
          </div>
          <div
            v-if="item.createdBy === preciseStore.uid || !item.createdBy"
            class="footer_bottom"
            @click="toRecordItem('edit', item, index)">
            编辑
          </div>
          <div
            v-if="item.createdBy === preciseStore.uid || !item.createdBy"
            class="footer_bottom danger"
            @click="toRecordItem('remove', item, index)">
            删除
          </div>
        </div>
      </div>
      <Empty
        v-show="addStore.records.length === 0"
        description="暂无跟进记录"
        image-size="125"></Empty>
    </div>
  </div>
</template>

<script setup>
  import { deleteRecord, getRecords } from '@/apis/precise'
  import { useAddStore } from '@/stores/add'
  import { usePreciseStore } from '@/stores/percise'
  import { useRequest } from 'alova/client'
  import { Empty, showToast } from 'vant'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const addStore = useAddStore()
  const preciseStore = usePreciseStore()

  function jumpAddRecord() {
    router.push({
      path: '/precise/record',
      query: {
        type: 'add',
      },
    })
  }

  const { send: fetchRecords } = useRequest(getRecords, {
    immediate: false,
  })
  const { send: fetchRemoveRecord } = useRequest(deleteRecord, {
    immediate: false,
  })
  async function toRecordItem(type, item, index) {
    if (type !== 'remove') {
      router.push({
        path: '/precise/record',
        query: {
          type,
          id: addStore.pageType === 'add' ? index : item.bid,
        },
      })
    } else {
      if (addStore.pageType === 'add') {
        addStore.records.splice(index, 1)
        showToast('删除成功')
      } else {
        const idx = addStore.records.findIndex((i) => i.bid === item.bid)
        if (idx == -1) {
          return
        }
        try {
          await fetchRemoveRecord(item.bid)
        } catch {
          return
        }
        const thisPage = Math.ceil((idx + 1) / addStore.recordPaginate.pageSize)
        const start = (thisPage - 1) * addStore.recordPaginate.pageSize
        fetchRecords({
          page: {
            pageNum: addStore.recordPaginate.pageNum,
            pageSize: addStore.recordPaginate.pageSize - 1,
          },
          params: {
            cardBid: addStore.cardBid,
          },
        }).then((data) => {
          addStore.records.splice(
            start,
            addStore.recordPaginate.pageSize,
            ...data.list,
          )
          showToast('删除成功')
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .record_list_box {
    .gray_text {
      color: #969799;
      font-size: 14px;
    }
    .list_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .list {
      .list_item {
        margin-top: 16px;
        border-radius: 16px;
        background-color: #fff;

        .item_line {
          display: flex;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #f2f3f5;

          .item_label {
            width: 86px;
            font-size: 14px;
          }

          .item_value {
            flex: 1;
            margin-left: 12px;
            overflow: hidden;
            font-size: 14px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &.item_footer {
            justify-content: flex-end;
            gap: 8px;
            border: none;

            .footer_bottom {
              padding: 1px 16px;
              border-radius: 26px 26px 26px 26px;
              background: rgba(18, 85, 228, 0.06);
              color: #1255e4;
              font-size: 14px;

              &.danger {
                background: rgba(255, 77, 79, 0.06);
                color: #ff4d4f;
              }
            }
          }
        }

        .item_header {
          .item_date {
            flex: 1;
            color: #1255e4;
            font-weight: 500;
            font-size: 16px;
          }

          .item_person {
            color: #969799;
            font-size: 14px;
          }
        }
      }
    }
  }
</style>
