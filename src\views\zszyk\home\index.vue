<template>
  <div class="page">
    <img src="@/assets/images/zszyk/home-banner.png" />
    <div class="card_list">
      <div
        v-for="card in cardList"
        :key="card.type"
        class="card"
        @click="() => handleClick(card.type, card.label)">
        <div class="icon_wrapper">
          <i
            class="iconfont"
            :style="card.style"
            :class="card.icon"></i>
        </div>
        <h2>{{ card.label }}</h2>
        <h1>{{ card.value }}</h1>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getInvestmentCount } from '@/apis/zszyk'
  import { useOrgFormStore } from '@/stores/zszyk'
  import { getTextGradient } from '@/utils'
  import { useRequest } from 'alova/client'

  import { computed, onActivated } from 'vue'
  import { useRouter } from 'vue-router'

  const store = useOrgFormStore()

  const { data, send } = useRequest(getInvestmentCount, {
    initialData: [],
  }).onSuccess(({ data }) => {
    store.typeOptions = data
  })

  // 不同的机构类型列表
  const cardList = computed(() => {
    const cardStyle = [
      {
        icon: 'icon-sh',
        style: getTextGradient(['#69D9F4', '#54B5F6'], '135deg'),
      },
      {
        icon: 'icon-enterprise',
        style: getTextGradient(['#74A8FE', '#547AF7'], '135deg'),
      },
      {
        icon: 'icon-xyh',
        style: getTextGradient(['#FFCA65', '#F6AB52'], '135deg'),
      },
      {
        icon: 'icon-zk',
        style: getTextGradient(['#78F1D9', '#2BF4C3'], '135deg'),
      },
      {
        icon: 'icon-tzjg',
        style: getTextGradient(['#D8B0FF', '#A953FF'], '135deg'),
      },
    ]

    return data.value.map((item, index) => {
      return {
        ...item,
        ...cardStyle[index],
      }
    })
  })

  const router = useRouter()
  const handleClick = (type) => {
    router.push(`/zszyk/orgs/${type}`)
  }

  onActivated(() => {
    send()
  })
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    overflow-y: auto;
    background-color: #f7f8fa;
    & > img {
      width: 100%;
      height: 120px;
    }

    .card_list {
      display: grid;
      grid-template-rows: repeat(3, 130px);
      grid-template-columns: repeat(2, 1fr);
      padding: 16px;
      gap: 12px;

      .card {
        width: 100%;
        height: 100%;
        padding: 16px;
        border: 1px solid #ffffff;
        border-radius: 10px 10px 10px 10px;
        background: linear-gradient(135deg, #eef8ff 0%, #ffffff 100%);

        .icon_wrapper {
          width: 36px;
          height: 36px;
          margin-bottom: 8px;
          border-radius: 8px 8px 8px 8px;
          background: #ffffff;
          line-height: 36px;
          text-align: center;
          & > i {
            font-size: 20px;
          }
        }

        & > h2 {
          margin-bottom: 4px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 14px;
          line-height: 22px;
        }

        & > h1 {
          color: rgba(0, 0, 0, 0.9);
          font-weight: bold;
          font-size: 20px;
          line-height: 28px;
        }
      }
    }
  }
</style>
