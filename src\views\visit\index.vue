<template>
  <div class="h-full w-full">
    <template v-if="uid">
      <Skeleton
        v-if="loading"
        title
        :row="12" />
      <Empty
        v-else-if="error"
        image="error"
        description="获取用户信息失败！请刷新!" />
      <RouterView
        v-else-if="visitUserStore.hasPermission"
        v-slot="{ Component }">
        <VuePageStack>
          <component
            :is="Component"
            :key="$route.fullPath"></component>
        </VuePageStack>
      </RouterView>
      <Empty
        v-else
        image="error"
        description="此用户没有任何权限！" />
    </template>
    <Empty
      v-else
      image="error"
      description="请携带用户标识访问此界面！" />
  </div>
</template>

<script setup>
  import { getVisitUserInfoById } from '@/apis/visit'
  import { useVisitUserStore } from '@/stores/visit'
  import { useRequest } from 'alova/client'
  import { Empty, Skeleton } from 'vant'
  import { computed } from 'vue'
  import { RouterView, useRoute } from 'vue-router'

  const route = useRoute()
  const visitUserStore = useVisitUserStore()
  const uid = computed(() => {
    return route.query.uid || visitUserStore.uid
  })
  const { loading, error } = useRequest(
    getVisitUserInfoById({
      uid: uid.value,
    }),
  ).onSuccess(({ data }) => {
    visitUserStore.setUserInfo(data)
  })
  if (uid.value) {
    visitUserStore.setUid(uid.value)
  }
</script>
