<template>
  <div class="contacts">
    <template v-if="list.length > 0">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="item"
        @click="$emit('itemClick', index)">
        <Row class="info">
          <Col
            class="truncate"
            span="6">
            {{ item.name }}
          </Col>
          <Col
            class="rl_border truncate"
            span="10">
            {{ item.post }}
          </Col>
          <Col
            class="truncate"
            span="8">
            {{ item.phone }}
          </Col>
        </Row>
        <footer v-if="isEdit">
          <div class="footer_content">
            <span
              class="primary active-button"
              @click="$emit('edit', index)">
              编辑
            </span>
            <span
              class="danger active-button"
              @click="$emit('delete', index)">
              删除
            </span>
          </div>
        </footer>
      </div>
    </template>
    <!-- 指定单位，支持 rem, vh, vw -->
    <Empty
      v-else
      image-size="8rem"
      description="暂无联系人数据" />
  </div>
</template>

<script setup>
  import { Col, Empty, Row } from 'vant'

  defineEmits(['edit', 'delete', 'itemClick'])

  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    // 是否是编辑状态
    isEdit: {
      type: Boolean,
      default: false,
    },
  })
</script>

<style lang="scss" scoped>
  .contacts {
    .item {
      border-radius: 8px;
      background: #ffffff;

      .info {
        padding: 16px 0;
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        line-height: 22px;
        text-align: center;

        .rl_border {
          border: none;
          border-right: 1px solid rgba(0, 0, 0, 0.06);
          border-left: 1px solid rgba(0, 0, 0, 0.06);
        }
      }

      & > footer {
        padding: 0 16px;
        .footer_content {
          display: flex;
          justify-content: flex-end;
          padding: 16px 0;
          border-top: 1px solid rgba(0, 0, 0, 0.06);

          .active-button {
            padding: 3px 16px;
            border-radius: 26px 26px 26px 26px;
            font-size: 14px;
            & ~ .active-button {
              margin-left: 12px;
            }
            &:active {
              opacity: 0.8;
            }
          }
          .primary {
            background: rgba(18, 85, 228, 0.06);
            color: #1255e4;
          }
          .danger {
            background: rgba(255, 77, 79, 0.06);
            color: #ff4d4f;
          }
        }
      }

      & + .item {
        margin-top: 16px;
      }
    }
  }
</style>
