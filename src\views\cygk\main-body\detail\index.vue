<template>
  <div class="container">
    <EnterpriseInfo
      :info="{
        ...info.companyBasicInfoVo,
        ...info.recommendReasonVo,
        ...info.companyPushInfoVo,
        ...info.companyStrengthVo,
      }">
      <template #local_button>
        <div
          class="local_button mt-[16px]"
          @click="onAssociation">
          关联信息
        </div>
      </template>
    </EnterpriseInfo>
    <div class="mt-[16px]">
      <CorporatePortrait :info />
    </div>
  </div>
</template>

<script setup>
  import { getRecommendAllDetail } from '@/apis/recommend'
  import { getToken } from '@/apis/recommend-detail'
  import CorporatePortrait from '@/views/recommend/components/CorporatePortrait.vue'
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import EnterpriseInfo from '../components/EnterpriseInfo.vue'
  import { itemInfoHandle } from '../options'

  const route = useRoute()
  const router = useRouter()

  const info = ref({})

  async function getApiToken() {
    try {
      const {
        data: { data },
      } = await getToken()
      sessionStorage.setItem('apiToken', data)
    } catch (error) {
      console.log(error)
    }
  }

  onMounted(() => {
    const apiToken = sessionStorage.getItem('apiToken')
    if (!apiToken) {
      getApiToken()
    }
  })

  async function getDetail() {
    try {
      const {
        data: { data: res },
      } = await getRecommendAllDetail({
        companyId: route.query.companyId,
      })
      info.value = {
        ...res,
        recommendReasonVo: itemInfoHandle(res.recommendReasonVo),
      }
    } catch (error) {
      console.log(error)
    }
  }

  function onAssociation() {
    router.push(
      `/connection?companyName=${info.value.companyBasicInfoVo.companyName}`,
    )
  }

  onMounted(async () => {
    getDetail()
  })
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f7f8fa;

    .local_button {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 44px;
      border-radius: 999px;
      background: rgb(0 118 246 / 6%);
      color: #0076f6;
      font-size: 14px;
    }
  }
</style>
