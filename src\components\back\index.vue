<template>
  <div
    class="back"
    @click="onBack">
    返回
  </div>
</template>

<script setup>
  import { useRoute, useRouter } from 'vue-router'
  import wx from 'weixin-js-sdk'

  const router = useRouter()
  const route = useRoute()

  function onBack() {
    if (route.query.outside) {
      console.log('小程序行为')
      wx.miniProgram.navigateBack()
    } else {
      router.back()
    }
  }
</script>

<style lang="scss" scoped>
  .back {
    position: fixed;
    right: 24px;
    bottom: 80px;
    z-index: 99999;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 38px;
    height: 38px;
    border-radius: 50%;
    background: linear-gradient(135deg, #a9caff 0%, #0d55cd 100%);
    color: #fff;
    box-shadow:
      0 5px 5px -3px rgb(0 123 255 / 10%),
      0 8px 10px 1px rgb(0 123 255 / 6%),
      0 3px 14px 2px rgb(0 123 255 / 5%);
    font-size: 12px;
  }
</style>
