<template>
  <div class="h-full w-full bg-[#f7f8fa]">
    <Swipe
      class="swiper_box"
      indicator-color="white"
      autoplay="3000">
      <SwipeItem class="relative">
        <div class="mark absolute bottom-[28px] pl-[16px]">
          <div class="text-[14px] text-white">武汉概况</div>
        </div>
        <img
          src="@/assets/images/enterprise-side/hb1.png"
          width="100%"
          height="200px"
          alt=""
          @click="proxyClick('swipe', 0)" />
      </SwipeItem>
      <SwipeItem class="relative">
        <div class="mark absolute bottom-[28px] pl-[16px]">
          <div class="text-[14px] text-white">区位交通</div>
        </div>
        <img
          src="@/assets/images/enterprise-side/hb2.png"
          width="100%"
          height="200px"
          alt=""
          @click="proxyClick('swipe', 1)" />
      </SwipeItem>
      <SwipeItem class="relative">
        <div class="mark absolute bottom-[28px] pl-[16px]">
          <div class="text-[14px] text-white">快捷操作指引</div>
        </div>
        <img
          src="@/assets/images/enterprise-side/hb3.png"
          width="100%"
          height="200px"
          alt=""
          @click="proxyClick('swipe', 2)" />
      </SwipeItem>
    </Swipe>
    <div class="menu_box relative z-[999]">
      <div class="menu_list">
        <div class="flex gap-x-[12px] rounded-[16px] bg-white p-[12px]">
          <div
            class="top_button"
            @click="proxyClick('IMPRESSIONS')">
            <div class="button_text">
              <div class="text">印象武汉</div>
              <div class="en">IMPRESSIONS</div>
            </div>
          </div>
          <div
            class="top_button"
            @click="proxyClick('INVESTMENT')">
            <div class="button_text">
              <div class="text">投资机会</div>
              <div class="en">INVESTMENT</div>
            </div>
          </div>
        </div>
        <div class="mt-[12px] rounded-[16px] bg-white p-[12px]">
          <div
            class="list_button"
            @click="proxyClick('CUSTOMER')"></div>
          <div
            class="list_button"
            @click="proxyClick('CONSULTING')">
            <!-- <div
              class="absolute top-[70px] left-[138px] text-[13px] text-[#FF5274]"
              @click.stop="useCall('027-82792795')">
              <a
                href="tel:027-82792795"
                class="block w-[98px] h-[28px]">
              </a>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Swipe, SwipeItem } from 'vant'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const linkMap = {
    INVESTMENT: {
      path: '/ent-side/opportunity',
    },
    CUSTOMER: {
      link: 'https://jzzs.whcftzcj.com/zsys/zhinengwenda/answer-m?id=f0d9d7c277de9abe',
    },
    CONSULTING: {
      path: '/ent-side/consulting',
    },
    IMPRESSIONS: {
      path: '/ent-side/impression',
    },
  }

  const proxyClick = (type, key) => {
    if (type === 'swipe') {
      switch (key) {
        case 0:
          router.push('/ent-side/overview_1')
          break
        case 1:
          router.push('/ent-side/overview_2')
          break
        case 2:
          router.push('/ent-side/guide')
          break
      }
    } else {
      // if (type === 'CONSULTING') {
      //   showToast('系统维护中')
      //   return
      // }
      linkMap[type]?.path
        ? router.push(linkMap[type].path)
        : window.open(linkMap[type].link)
    }
  }
</script>

<style lang="scss" scoped>
  .mark {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: linear-gradient(
      180deg,
      rgb(0 0 0 / 25%) 0%,
      rgb(0 0 0 / 50%) 71%
    );
    pointer-events: none;
  }

  .swiper_box {
    :deep(.van-swipe__indicators) {
      bottom: 33px;
    }

    :deep(.van-swipe__indicator--active) {
      width: 20px;
      border-radius: 6px;
      background-color: #1677ff !important;
    }
  }

  .menu_box {
    margin-top: -50px;

    .menu_list {
      padding: 0 16px;
      transform: translateY(26px);

      .button_text {
        position: absolute;
        top: 16px;
        left: 16px;

        .text {
          color: #fff;
          font-size: 18px;
          line-height: 26px;
        }

        .en {
          color: rgb(255 255 255 / 35%);
          font-size: 12px;
          line-height: 20px;
        }
      }

      .top_button {
        position: relative;
        flex: 1;
        height: 90px;

        &:first-child {
          background-image: url('@/assets/images/enterprise-side/button1.png');
          background-size: cover;
        }

        &:last-child {
          background-image: url('@/assets/images/enterprise-side/button2.png');
          background-size: cover;
        }
      }

      .list_button {
        position: relative;
        height: 120px;

        &:first-child {
          background-image: url('@/assets/images/enterprise-side/button3.png');
          background-size: cover;
        }

        &:last-child {
          margin-top: 12px;

          // background-image: url('@/assets/images/enterprise-side/button4.png');
          background-image: url('@/assets/images/enterprise-side/bt4-np.png');
          background-size: cover;
        }
      }
    }
  }
</style>
