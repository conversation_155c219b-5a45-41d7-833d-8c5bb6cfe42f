export function transformNumber(n, unit = '个') {
  const unitMap = {
    个: 1,
    万: 10000,
    百万: 1000000,
  }
  let number = +n * unitMap[unit]
  let numberUnit = ''
  if (isNaN(number)) {
    number = parseFloat(n) * unitMap[unit]
  }

  if (number / 10000 >= 1) {
    number /= 10000
    numberUnit = '万'
  }
  if (number / 10000 >= 1) {
    number /= 10000
    numberUnit = '亿'
  }

  return `${number.toFixed(2)}${numberUnit}`
}

export const listInfoHandle = (list) => {
  return list.map((item) => {
    return {
      ...item,
      reason: {
        // 对外投资数量
        investinfoCount: item.investinfoCount,
        // 对外投资金额
        investinfoTotalAmount: item.investinfoTotalAmount,
        // 中标数量
        localWtbCount: item.localWtbCount,
        // 中标金额
        localWtbTotalAmount: item.localWtbTotalAmount,
        financingInfo: item.financingInfo?.split(',')?.map((item) => {
          item = item.split('|')
          return `${item[2]}${item[0]}${item[0].includes('轮') ? '' : '轮'}融资${transformNumber(item[1])}`
        }),
      },
    }
  })
}

export const targetColorTag = {
  领导交办: {
    bg: '#E6FFFB',
    txt: '#13C2C2',
  },
  部门推荐: {
    bg: '#F6FFED',
    txt: '#52C41A',
  },
  自主对接: {
    bg: '#FFF7E6',
    txt: '#FA8C16',
  },
  其他来源: {
    bg: '#F9F0FF',
    txt: '#722ED1',
  },
}
