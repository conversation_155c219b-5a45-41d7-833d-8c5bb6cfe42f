<template>
  <div
    class="title"
    :style="{
      'font-size': fontSize,
    }">
    <span class="ml-[6px]">
      <slot></slot>
    </span>
  </div>
</template>

<script setup>
  const { fontSize } = defineProps({
    fontSize: {
      type: String,
      default: '16px',
    },
  })
</script>

<style lang="scss" scoped>
  .title {
    position: relative;
    height: 24px;
    font-weight: bold;

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      display: block;
      width: 100%;
      width: 4px;
      height: 16px;
      border-radius: 16px;
      background-color: #1255e4;
      transform: translateY(-50%);
    }
  }
</style>
