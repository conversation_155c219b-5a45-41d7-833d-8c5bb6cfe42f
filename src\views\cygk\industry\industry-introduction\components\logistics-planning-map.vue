<!-- 物流规划图 -->
<template>
  <div class="container">
    <div class="h-[330px] w-full">
      <img
        class="h-full w-full"
        :src="currentImage"
        @click="show = true" />
    </div>
    <div class="bottom">
      <div class="text">
        <h2 class="text-[18px] font-bold">{{
          currentData?.data?.cyfl || '暂无数据'
        }}</h2>
        <div class="introduce">
          {{ currentData?.data?.jianjie || '暂无数据' }}
        </div>
      </div>
    </div>
  </div>
  <ImagePreview
    v-model:show="show"
    :images="[currentImage]"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import allData from '@/assets/js/cyjs'
  import { ImagePreview } from 'vant'
  import { computed, inject, ref } from 'vue'
  const currentType = inject('currentType')
  const currentData = computed(() => {
    return Object.values(allData).find((item) => {
      return item.data.cyfl.includes(currentType.value)
    })
  })
  const currentImage = computed(() => {
    return new URL(`./images/16.2-物流.png`, import.meta.url).href
  })
  const show = ref(false)
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 10px;
    background-color: #f7f8fa;

    .bottom {
      width: 100%;
      padding: 16px;

      .box {
        width: 100%;
        height: 226px;
        font-size: 14px;
      }

      .text {
        padding: 16px;
        background-color: #fff;

        .introduce {
          display: flex;
          gap: 10px;
          margin-top: 8px;
          color: rgb(0 0 0 / 40%);
          font-size: 14px;
        }
      }
    }
  }
</style>
