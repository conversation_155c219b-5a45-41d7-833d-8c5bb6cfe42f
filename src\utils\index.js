/**
 * 后端返回blob对象进行下载
 *  + axios 配置 responseType: 'blob'
 * @param {*} data
 * @param {*} fileName
 */
export function downloadFile(data, fileName = 'download') {
  const blob = new Blob([data])
  const downloadElement = document.createElement('a')
  // 创建下载的链接
  const href = window.URL.createObjectURL(blob)
  downloadElement.href = href
  // 下载后文件名
  downloadElement.download = fileName
  document.body.appendChild(downloadElement)
  // 点击下载
  downloadElement.click()
  // 下载完成移除元素
  document.body.removeChild(downloadElement)
  // 释放掉blob对象
  window.URL.revokeObjectURL(href)
}
export function extractFileName(filePath) {
  // 使用正则表达式匹配文件名，包括扩展名
  // eslint-disable-next-line no-useless-escape
  const match = filePath.match(/\/([^\/]+)$/)
  // 如果匹配成功，返回文件名
  if (match) {
    return match[1]
  }
  // 如果没有匹配到，返回原始文件路径
  return filePath
}
export function replaceString(str) {
  if (!str) return undefined

  return str
    .replace('“', '"')
    .replace('”', '"')
    .replace('（', '(')
    .replace('）', ')')
}

/**
 * 计算文字渐变
 * @param {Array} colors 颜色数组
 * @param {String} position 渐变方向
 */
export const getTextGradient = (colors = [], position = top) => {
  return {
    backgroundImage: `-webkit-linear-gradient(${position}, ${colors.join(',')})`,
    WebkitBackgroundClip: 'text',
    WebkitTextFillColor: 'transparent',
  }
}

/**
 * 根据当前索引循环获取颜色数组中的颜色
 * @param {number} currentIndex 当前索引
 * @param {string[]} colorArray 颜色数组
 * @returns {string} 返回的颜色
 */
export function getColorByIndex(currentIndex, colorArray) {
  // 处理空数组情况
  if (!colorArray || colorArray.length === 0) {
    return '#000000' // 默认返回黑色
  }

  // 计算实际索引（处理超出数组长度的情况）
  const actualIndex = currentIndex % colorArray.length

  // 返回对应的颜色
  return colorArray[actualIndex]
}
