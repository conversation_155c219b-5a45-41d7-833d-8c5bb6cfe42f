<template>
  <div class="main_page">
    <Tabs v-model:active="active">
      <Tab title="城市概况">
        <img
          class="img_bg"
          :src="csgk"
          alt="" />
      </Tab>
      <Tab title="行政区划">
        <img
          class="img_bg"
          :src="xz"
          alt=""
          @click="show = true" />
      </Tab>
    </Tabs>
    <ImagePreview
      v-model:show="show"
      :images="[xz]"
      :close-on-click-image="false">
      <template #image="{ src }">
        <img
          :src="src"
          class="w-full" />
      </template>
    </ImagePreview>
  </div>
</template>

<script setup>
import csgk from '@/assets/images/tzzn/home/<USER>'
import xz from '@/assets/images/tzzn/home/<USER>'
import { ImagePreview, Tab, Tabs } from 'vant'
import { ref } from 'vue'
const show = ref(false)
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .img_bg {
    width: 100%;
  }
}
</style>
