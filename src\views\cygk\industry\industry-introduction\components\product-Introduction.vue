<!-- 产业概况 -->
<template>
  <!-- <div class="w-full bg-[#fff] px-[16px]">
    <div v-if="!loading">
      <img
        v-if="industryInfo?.images && industryInfo?.images.length > 0"
        :src="industryInfo?.images[0].url"
        class="mt-[12px] h-[180px] w-full"
        alt=""
        @click="photoShow = true" />
    </div>
    <div
      v-else
      class="flex justify-center">
      <Loading></Loading>
    </div>
  </div> -->
  <div class="bg-[#fff] px-[16px] pb-[10px] pt-[10px]">
    <Title
      v-if="industryInfo.positioning"
      class="mt-[12px]"
      >产业介绍</Title
    >
    <p
      v-if="industryInfo.positioning"
      class="mt-[12px] indent-8 text-[rgb(0,0,0,0.4)]"
      v-html="industryInfo.positioning"></p>
    <Title
      v-if="industryInfo.scale"
      class="mt-[12px]"
      >产业规模</Title
    >

    <p
      v-if="industryInfo.scale"
      class="mt-[12px] indent-8 text-[rgb(0,0,0,0.4)]"
      v-html="industryInfo.scale"></p>

    <Title
      v-if="industryInfo.synopsis"
      class="mt-[12px]"
      >产业规划</Title
    >
    <p
      v-if="industryInfo.synopsis"
      class="mt-[12px] indent-8 text-[rgb(0,0,0,0.4)]"
      v-html="industryInfo.synopsis"></p>
  </div>
  <ImagePreview
    v-model:show="photoShow"
    :images="photoShowImages"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import * as apis from '@/apis/index'
  import Title from '@/components/title/index.vue'
  import { ImagePreview } from 'vant'
  import { computed, inject, ref, watchEffect } from 'vue'
  const currentType = inject('currentType')
  const photoShow = ref(false)
  const photoShowImages = computed(() => {
    if (industryInfo.value?.images && industryInfo.value.images.length > 0) {
      return industryInfo.value.images.map((item) => item.url)
    }
    return []
  })
  const loading = ref(false)
  const industryInfo = ref({})
  const onSearch = async () => {
    try {
      loading.value = true
      const res = await apis.getKeyDetail('overview', {
        industrychain: currentType.value,
      })
      const { data } = res.data
      industryInfo.value = data
      const images = JSON.parse(data?.images || '[]')
      if (images.length > 0) {
        industryInfo.value.images = images.map((item) => ({
          ...item,
          url: `${import.meta.env.VITE_IMAGES_BASE_URL}${item.value}`,
        }))
      }

      industryInfo.value.positioning = data.positioning
        ?.replace(/\\n/g, '\n')
        ?.replace(/\n/g, '<br> &nbsp; &nbsp; &nbsp; &nbsp;')
      industryInfo.value.scale = data.scale
        ?.replace(/\\n/g, '\n')
        ?.replace(/\n/g, '<br> &nbsp; &nbsp; &nbsp; &nbsp;')
      industryInfo.value.synopsis = data.synopsis
        ?.replace(/\\n/g, '\n')
        ?.replace(/\n/g, '<br> &nbsp; &nbsp; &nbsp; &nbsp;')
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }
  watchEffect(() => {
    onSearch()
  })
</script>
