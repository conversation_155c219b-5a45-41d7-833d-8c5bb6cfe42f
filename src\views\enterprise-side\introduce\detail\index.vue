<template>
  <div class="w-[100%] bg-[#F7F8FA]">
    <!-- <BackButton /> -->
    <!-- <div
      id="map"
      class="map"></div> -->
    <div class="w-full px-[16px] py-[12px]">
      <div
        v-if="info?.images?.length > 0 && route.query.type !== '应用场景'"
        class="swipe">
        <Swipe
          :autoplay="info?.images?.length === 1 ? 0 : 3000"
          :loop="!info?.images?.length === 1">
          <SwipeItem
            v-for="(item, index) in info.images"
            :key="index">
            <img
              class="h-[160px] w-[284px]"
              :src="item.url"
              alt="" />
          </SwipeItem>
        </Swipe>
      </div>
      <div v-if="route.query.type === 'park'">
        <div class="card">
          <p class="title">{{ info.name }}</p>

          <div class="text_line">
            <span class="small_title">区域位置:</span>
            <p class="info">{{ info.address || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">实地面积:</span>
            <p class="info">{{ info.grossarea || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">投资热线：</span>
            <p class="info">{{
              info.phone?.replaceAll('\\n', '') || '暂无数据'
            }}</p>
          </div>
        </div>
        <div class="card">
          <div class="text_line">
            <span class="small_title">产业定位:</span>
            <p class="info">{{ info.positioning || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">简 介:</span>
            <p class="info">{{
              info.synopsis?.replaceAll('\\n', '') || '暂无数据'
            }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">重点企业:</span>
            <p class="info">{{ info.enterprises || '暂无数据' }} </p>
          </div>
        </div>
      </div>
      <div v-else-if="route.query.type === 'building'">
        <div class="card">
          <p class="title">{{ info.name }}</p>
          <div class="text_line">
            <span class="small_title">开发商:</span>
            <p class="info">{{ info.kfs || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">所在行政区:</span>
            <p class="info">{{ info.szxzq || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">楼层数：</span>
            <p class="info">{{ info.cs || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">总面积：</span>
            <p class="info">{{ info.tl || '暂无数据' }}</p>
          </div>
          <!-- <div
            v-if="info.industrychain"
            class="text_line">
            <span class="small_title">产业链：</span>
            <p class="info">
              <Tag
                type="primary"
                class="ml-[5px]"
                >{{ info.industrychain }}</Tag
              >
            </p>
          </div> -->
        </div>
        <div class="card">
          <div class="text_line">
            <span class="small_title">联系人:</span>
            <p class="info">{{ info?.dataInfo?.lxr || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">联系电话:</span>
            <p class="info">{{
              info?.dataInfo?.lxdh?.replaceAll('\\n', '') || '暂无数据'
            }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">地址:</span>
            <p class="info">{{ info.dz || '暂无数据' }} </p>
          </div>
        </div>
        <div class="card">
          <div class="text_line">
            <span class="small_title">楼宇简介:</span>
            <p class="info">{{ info?.dataInfo?.jj || '暂无数据' }}</p>
          </div>
        </div>
      </div>
      <div
        v-else-if="
          route.query.type === 'colleges' ||
          route.query.type === 'association' ||
          route.query.type === 'research' ||
          route.query.type === 'innovate'
        ">
        <div class="card">
          <p class="title">{{ info.name }}</p>
        </div>
        <div class="card">
          <div
            v-if="route.query.type === 'colleges'"
            class="text_line">
            <span class="small_title">学院:</span>
            <p class="info">{{ info?.xy || '暂无数据' }}</p>
          </div>
          <div
            v-else
            class="text_line">
            <span class="small_title">行政区:</span>
            <p class="info">{{ info?.ssxzq || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">产业:</span>
            <p class="info">{{ info?.industrychain || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">地址:</span>
            <p class="info">{{ info.dz || '暂无数据' }} </p>
          </div>
        </div>
        <div class="card">
          <div class="text_line">
            <span class="small_title">简介:</span>
            <p
              class="info"
              v-html="info?.jj?.replace(/\\n/g, '<br />')" />
          </div>
        </div>
      </div>
      <div v-else>
        <div class="card">
          <p class="title">{{ info.name }}</p>
          <div class="text_line">
            <span class="small_title">场景来源:</span>
            <p class="info">{{ info.cjly || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">联系人:</span>
            <p class="info">{{ info.lxr || '暂无数据' }}</p>
          </div>
          <div class="text_line">
            <span class="small_title">联系电话：</span>
            <p class="info">{{
              info.lxdh?.replaceAll('\\n', '') || '暂无数据'
            }}</p>
          </div>
          <!-- <div
            v-if="info.industrychain"
            class="text_line">
            <span class="small_title">产业链：</span>
            <p class="info">
              <Tag
                type="primary"
                class="ml-[5px]"
                >{{ info.industrychain }}</Tag
              >
            </p>
          </div> -->
        </div>
        <div class="card">
          <div class="text_line">
            <span class="small_title">场景简介:</span>
            <p
              class="info"
              v-html="info?.cjjj?.replace(/\n/g, '<br />') || '暂无数据'"></p>
          </div>
          <div
            v-if="info.yycj"
            class="text_line">
            <span class="small_title">应用场景:</span>
            <p class="info">{{ info.yycj || '暂无数据' }}</p>
          </div>
        </div>
      </div>
    </div>
    <Back></Back>
  </div>
</template>

<script setup>
  import * as apis from '@/apis/index.js'
  // import BackButton from '@/components/back/index.vue'
  import Back from '@/components/back/index.vue'
  import { Swipe, SwipeItem } from 'vant'
  import { onActivated, onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'
  const route = useRoute()
  onMounted(() => {
    getKeyDetail()
  })

  onActivated(() => {
    getKeyDetail()
  })
  const info = ref({})
  const getKeyDetail = async () => {
    const res = await apis.getKeyDetail(route.query.type, {
      id: route.query.id || '',
    })
    const { data } = res.data
    info.value = data
    let images = []
    if (data?.images !== 'NULL') {
      images = JSON.parse(data?.images || '[]')
    }

    info.value.images = images.map((item) => ({
      ...item,
      url: `${import.meta.env.VITE_IMAGES_BASE_URL}${item.value}`,
    }))

    if (data.cjjj) {
      info.value.cjjj = data.cjjj.replace(
        /\\n/g,
        '<br/><span style="margin-left: 10px;"></span>',
      )
    }
    try {
      if (data.data_info && typeof data.data_info === 'string') {
        info.value.dataInfo = JSON.parse(data.data_info)
      }
    } catch (error) {
      if (data.data_info && typeof data.data_info === 'string') {
        info.value.dataInfo = data.data_info
      }
      console.log(error)
    }

    // var map = new BMapGL.Map('map')
    // let longitude = 114.298572
    // let latitude = 30.584355
    // if (data?.longitude && data?.longitude !== 'NULL') {
    //   longitude = data?.longitude
    // }
    // if (data?.latitude && data?.latitude !== 'NULL') {
    //   latitude = data?.latitude
    // }

    // // // 默认展示的地图中心点
    // map.centerAndZoom(new BMapGL.Point(longitude, latitude), 10)
    // // // 开启鼠标滚轮缩放
    // map.enableScrollWheelZoom(true)
    // if (
    //   data?.longitude &&
    //   data?.latitude &&
    //   data?.longitude !== 'NULL' &&
    //   data?.latitude !== 'NULL'
    // ) {
    //   // 创建点标记
    //   var marker1 = new BMapGL.Marker(new BMapGL.Point(longitude, latitude))
    //   // 在地图上添加点标记
    //   map.addOverlay(marker1)
    // }
  }
</script>

<style lang="scss" scoped>
  .map {
    height: 220px;
    box-shadow: inset 0 -20px 30px -20px rgb(0 0 0 / 20%);
  }

  .swipe {
    width: 284px;
    height: 160px;
    margin: 0 auto;
  }

  .card {
    width: 100%;
    margin-top: 16px;
    padding: 16px;
    background-color: #fff;

    .title {
      color: rgb(0 0 0 / 90%);
      font-weight: bold;
      font-size: 18px;
    }

    .text_line {
      display: flex;
      gap: 4px;
      margin-top: 6px;

      .small_title {
        min-width: 80px;
      }

      .info {
        color: rgba($color: #000, $alpha: 40%);
      }
    }
  }
</style>
