<template>
  <span v-if="label">{{ label }}</span>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    tags: {
      type: String,
      default: '',
    },
    options: {
      type: Array,
      default: () => [],
    },
  })

  const label = computed(() => {
    const tag = props.tags?.split(',')[0] || ''
    const item = props.options.find((dict) => dict.value == tag)
    return item ? item.label : null
  })
</script>

<style lang="scss" scoped>
  span {
    max-width: 32%;
    padding: 2px 8px;
    border-radius: 3px 3px 3px 3px;
    background: #e6f4ff;
    color: #1677ff;
    font-size: 12px;
    line-height: 20px;
  }
</style>
