<template>
  <div class="feedback_box">
    <Field
      v-model="text"
      class="rounded-[8px]"
      placeholder="请输入您的问题或建议"
      label="问题或建议："
      type="textarea">
    </Field>
    <Field
      v-model="contact"
      class="mt-[16px] rounded-[8px]"
      label="联系方式："
      placeholder="请输入您的联系方式" />
    <Button
      block
      type="primary"
      class="mt-[16px]"
      :loading="loading"
      @click="onsubmit">
      提交
    </Button>
  </div>
</template>

<script setup>
  import { feedback } from '@/apis/enterprise-side'
  import { Button, Field, showToast } from 'vant'
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const text = ref('')
  const contact = ref('')
  const loading = ref(false)

  async function submit() {
    loading.value = true
    try {
      await feedback({
        issue: text.value,
        contactPhoneNumber: contact.value,
      })
      // showToast({ message: '提交成功', type: 'success' })
      // router.back()
    } catch (e) {
      // showToast({ message: '提交失败', type: 'fail' })
      console.log(e)
    } finally {
      loading.value = false
      showToast({ message: '提交成功', type: 'success' })
      router.back()
    }
  }

  function onsubmit() {
    if (!text.value) {
      showToast({ message: '请输入问题或建议', type: 'fail' })
      return
    }
    if (!contact.value) {
      showToast({ message: '请输入联系方式', type: 'fail' })
      return
    }
    if (!/^1[3456789]\d{9}$/.test(contact.value)) {
      showToast({ message: '请输入正确的手机号', type: 'fail' })
      return
    }
    submit()
  }
</script>

<style lang="scss" scoped>
  .feedback_box {
    width: 100%;
    height: 100%;
    padding: 16px;
    background-color: #f7f8fa;
  }
</style>
