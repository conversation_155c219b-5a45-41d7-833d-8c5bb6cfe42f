<template>
  <div class="flex h-full w-full flex-col bg-[#F7F8FA]">
    <div class="filter_box">
      <div class="flex h-[24px] w-full items-center justify-between">
        <div
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showIndustry = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ industryChian || '产业链' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div>
      </div>
      <div class="flex h-[24px] w-full items-center justify-between">
        <div
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showEssential = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ essential }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div>
      </div>
    </div>
    <div
      ref="listRef"
      class="flex-1 overflow-y-auto">
      <div class="w-full px-[16px] py-[12px]">
        <!-- 标题 总数 -->
        <div class="flex w-full items-center justify-between">
          <Title>{{ essential }}简介</Title>
          <!-- <div class="flex gap-[5px]">
            <span class="total"
              >共<span class="text-[#1255E4]">{{ totalNumber }}</span
              >个</span
            >
          </div> -->
        </div>
        <div
          v-if="!loading"
          class="list mt-[16px]">
          <!-- 列表 -->
          <div
            v-for="(item, index) in tableData"
            :key="index"
            class="card relative"
            @click="handlerToDetail(item.id)">
            <!-- 列表标题 -->
            <div class="title">
              <div class="flex w-full items-center justify-between gap-[10px]">
                <span class="flex-1">{{ item.name }}</span>
                <Tag type="primary">
                  <span class="text-ellipsis">
                    {{ item.ssxzq }}
                  </span>
                </Tag>
              </div>
            </div>

            <!-- 学院 -->
            <div
              v-if="essential === '高等院校'"
              class="line_text mt-[6px]">
              <p class="text">
                {{ item?.xy || '暂无学院' }}
              </p>
            </div>

            <div
              v-if="essential !== '协会联盟' && item?.lxdh"
              class="line_text mt-[6px]">
              <p class="text">
                {{ item?.lxdh }}
              </p>
            </div>

            <!-- 地址 -->
            <div
              v-if="item.dz"
              class="line_text mt-[6px]">
              <p class="text">
                {{ item.dz?.replaceAll('\\n', '') || '暂无地址' }}
              </p>
            </div>
            <!-- 产业链 -->
            <div class="line_text mt-[6px]">
              <p class="text">
                {{ item?.industrychain || '暂无产业链' }}
              </p>
            </div>

            <!-- 箭头icon -->
            <i
              class="iconfont icon-RightCircle absolute bottom-[10px] right-[16px] flex text-[12px] text-[#000]/[.26]"></i>
          </div>
        </div>
        <div
          v-else
          class="flex justify-center">
          <Loading></Loading>
        </div>
        <div
          v-show="!loading && tableData.length === 0"
          class="flex justify-center rounded-[8px] bg-white p-[12px]"
          >暂无数据</div
        >
      </div>
    </div>
  </div>
  <ActionSheet
    v-model:show="showIndustry"
    :actions="industryOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onIndustryOptionsChange"></ActionSheet>
  <ActionSheet
    v-model:show="showEssential"
    :actions="essentialOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onEssentialOptionsChange"></ActionSheet>
</template>
<script setup>
  import { getAllPageList } from '@/apis/index.js'
  import Title from '@/components/title/index.vue'
  import { usePageStatusStore } from '@/stores/page-status.js'
  import { useScroll } from '@vueuse/core'
  import { ActionSheet, Loading, Tag } from 'vant'
  import { computed, nextTick, onMounted, ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { INDUSTRY_OPTIONS } from '../options.js'

  const router = useRouter()
  const pageStatusStore = usePageStatusStore()
  const listRef = ref(null)

  const { y } = useScroll(listRef)

  const loading = ref(false)
  const totalNumber = ref(0)
  const tableData = ref([])

  const showIndustry = ref(false)
  const industryChian = ref('产业链')
  const industryOptions = computed(() => {
    return [{ name: '产业链' }].concat(
      INDUSTRY_OPTIONS.map((m) => ({ name: m.value })),
    )
  })
  const onIndustryOptionsChange = (item) => {
    industryChian.value = item.name
    pageStatusStore.resources.industryChian = item.name
    showIndustry.value = false
    onSearch()
  }

  const showEssential = ref(false)
  const essential = ref('高等院校')
  const essentialOptions = [
    {
      name: '高等院校',
    },
    {
      name: '科研院所',
    },
    {
      name: '功能平台',
    },
    // {
    //   name: '协会联盟',
    // },
  ]

  const onEssentialOptionsChange = (item) => {
    essential.value = item.name
    pageStatusStore.resources.essential = item.name
    showEssential.value = false
    onSearch()
  }
  const essentialValue = computed(() => {
    return {
      高等院校: 'colleges',
      科研院所: 'research',
      功能平台: 'innovate',
      协会联盟: 'association',
    }[essential.value]
  })

  watch(
    y,
    (nv) => {
      pageStatusStore.resources.y = nv
    },
    {
      immediate: false,
    },
  )

  const handlerToDetail = (id) => {
    router.push({
      path: '/cygk/introduce-detail',
      query: {
        id,
        type: essentialValue.value,
        key: {
          colleges: '高等院校详情',
          research: '科研院所详情',
          association: '协会联盟详情',
          innovate: '功能平台详情',
        }[essentialValue.value],
      },
    })
  }

  const onSearch = async () => {
    try {
      loading.value = true
      const params = {
        industrychain:
          industryChian.value === '产业链' ? '' : industryChian.value,
      }
      const { data: res } = await getAllPageList(essentialValue.value, params)
      tableData.value = res.data
      pageStatusStore.resources.list = res.data
      totalNumber.value = res.data.length
      loading.value = false
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (pageStatusStore.resources.list.length) {
      tableData.value = pageStatusStore.resources.list
      totalNumber.value = pageStatusStore.resources.list.length
      industryChian.value = pageStatusStore.resources.industryChian
      essential.value = pageStatusStore.resources.essential
      nextTick(() => {
        listRef.value.scrollTo({ top: pageStatusStore.resources.y })
      })
    } else {
      onSearch()
    }
  })
</script>
<style lang="scss" scoped>
  .filter_box {
    display: flex;
    padding: 10px 0;
    background-color: #fff;
  }

  .card {
    width: 100%;
    margin-top: 16px;
    padding: 16px;
    border-radius: 12px;
    background-color: #fff;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .text-ellipsis {
        overflow: hidden;
        max-width: 100px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .line_text {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 22px;
      color: rgb(0 0 0 / 40%);

      .text {
        overflow: hidden;
        width: 283px;
        height: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
