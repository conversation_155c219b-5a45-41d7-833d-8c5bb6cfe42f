<template>
  <!-- 统计数据 -->
  <div class="flex justify-between bg-[#f7f8fa] px-[16px] pt-[16px]">
    <div
      class="flex items-center text-[14px] font-semibold"
      @click="onShowPop">
      <div class="line"></div>
      <span>产业：</span>
      <span class="industry_list">
        {{ industryList.length === 0 ? '全部' : industryList.join(',') }}
      </span>
    </div>

    <div class="text-[12px] text-[rgba(0,0,0,0.4)]">
      <span>共计</span>
      <span class="text-[#1255E4]">{{ total }}</span>
      <span>条</span>
    </div>
  </div>
</template>

<script setup>
  import { showToast } from 'vant'

  const { industryList } = defineProps({
    total: {
      type: Number,
      default: 0,
    },
    industryList: {
      type: Array,
      default: () => [],
    },
  })

  const onShowPop = () => {
    if (industryList.length > 0) {
      showToast({
        message: industryList.join(','),
      })
    }
  }
</script>

<style lang="scss" scoped>
  .line {
    width: 4px;
    height: 16px;
    margin-right: 4px;
    border-radius: 16px;
    background: #1255e4;
  }

  .industry_list {
    overflow: hidden;
    max-width: 181px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
