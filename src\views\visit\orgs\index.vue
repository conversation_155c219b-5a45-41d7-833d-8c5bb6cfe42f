<template>
  <div class="page">
    <Tabs
      v-model:active="visitStore.activeTabIndex"
      sticky>
      <Tab title="数据看板">
        <DataPane />
      </Tab>
      <Tab title="企业看板">
        <CompanyPane />
      </Tab>
      <Tab title="个人看板">
        <UserPane />
      </Tab>
    </Tabs>
  </div>
</template>

<script setup>
  import { useVisitIndexStore } from '@/stores/visit'
  import { Tab, Tabs } from 'vant'
  import CompanyPane from './components/company-pane/index.vue'
  import DataPane from './components/data-pane/index.vue'
  import UserPane from './components/user-pane/index.vue'

  const visitStore = useVisitIndexStore()
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    // overflow-y: auto;
    overflow: auto;
    background-color: #f7f8fa;

    // :deep(.van-tabs) {
    //   height: 100%;
    // }
  }
</style>
