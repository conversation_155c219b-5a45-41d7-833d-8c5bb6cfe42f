<template>
  <div class="main_page">
    <div class="list">
      <div @click="navigate(item.path)" class="item" v-for="item in list" :key="item.path">
        <img style="width: 100%" :src="item.img" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import zdgj from '@/assets/images/tzzn/tscyy/zdgj.png'
import zfwh from '@/assets/images/tzzn/tscyy/zfwh.png'
import gjwl from '@/assets/images/tzzn/tscyy/gjwl.png'
import whwl from '@/assets/images/tzzn/tscyy/whwl.png'
import ggdz from '@/assets/images/tzzn/tscyy/ggdz.png'

const router = useRouter()

const list = [
  { path: 'zdgj', img: zdgj },
  { path: 'zfwh', img: zfwh },
  { path: 'gjwl', img: gjwl },
  { path: 'whwl', img: whwl },
  { path: 'ggdz', img: ggdz }
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    padding: 16px;
    flex-direction: column;
    gap: 16px;
  }
}
</style>
