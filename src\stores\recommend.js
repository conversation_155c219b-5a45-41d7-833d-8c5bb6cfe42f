import { getUserInfo } from '@/apis/recommend'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useRecommendStore = defineStore('recommend', () => {
  async function getRecommendUserInfo(username) {
    try {
      const {
        data: { data: res },
      } = await getUserInfo({ userName: username })
      return res
    } catch (error) {
      console.log(error)
    }
  }

  const noAcceptInfo = ref({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      next: false,
    },
    filterData: {},
    scroll: 0,
  })

  const acceptInfo = ref({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      next: false,
    },
    filterData: {},
    scroll: 0,
  })

  const allInfo = ref({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      next: false,
    },
    filterData: {},
    scroll: 0,
  })

  return { getRecommendUserInfo, noAcceptInfo, acceptInfo, allInfo }
})
