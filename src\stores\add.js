import dayjs from 'dayjs'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const useAddStore = defineStore('add', () => {
  const bid = ref('')
  const cardBid = ref('')
  /** 是否第一次进入页面 */
  const isFirst = ref(true)
  /** 表单数据 */
  const formData = ref({})
  /** 记录数据 */
  const records = ref([])
  const recordPaginate = ref({
    pageNum: 1,
    pageSize: 10,
    hasNext: false,
  })
  /** 记录添加页类型 */
  const pageType = ref('view')
  const scroll = ref(0)

  const getRecords = computed(() => {
    return records.value.sort((a, b) => {
      const result = dayjs(b.communicationDate).isBefore(
        dayjs(a.communicationDate),
      )
      return result ? -1 : 1
    })
  })

  return {
    bid,
    cardBid,
    isFirst,
    formData,
    records,
    pageType,
    recordPaginate,
    scroll,
    getRecords,
  }
})
