<template>
  <Form>
    <template
      v-for="item in options"
      :key="item.name">
      <component
        :is="getFormItem(item.type)"
        v-if="renderTemplateList.includes(item.type)"
        v-show="item?.render === void 0 ? true : item.render"
        input-align="right"
        :readonly="item.readonly || readonly"
        :disabled="item.disabled || disabled"
        :type="item.type === 'textarea' ? 'textarea' : undefined"
        :autosize="
          item.type === 'textarea' && item.autosize ? item.autosize : undefined
        "
        :model-value="item.value"
        :label="item.label"
        :required="item.required"
        :placeholder="cellValueRender(item)"
        :name="item.name"
        :rules="item?.rules"
        @update:model-value="
          (val) => {
            item.events?.change && item.events.change(val)
          }
        " />
      <!-- 需要手动添加Cell的组件（需要弹出层的组件） -->
      <template v-else-if="hasPopupList.includes(item.type)">
        <Cell
          :title="item.label"
          :is-link="itemIsEdit(item)"
          :value="cellValueRender(item)"
          :required="item.required"
          @click="itemIsEdit(item) && (showMap[item.name] = true)"></Cell>
        <!-- 单选 -->
        <ActionSheet
          v-if="item.type === 'action-sheet'"
          v-model:show="showMap[item.name]"
          :description="item.description || item.label"
          :cancel-text="item['cancel-text']"
          :actions="item.actions"
          :close-on-click-action="item['close-on-click-action']"
          @select="
            (val) => {
              item.events?.select && item.events?.select(val)
              showMap[item.name] = false
            }
          " />
        <!-- 多选 -->
        <Popup
          v-else-if="item.type === 'multiple'"
          v-model:show="showMap[item.name]"
          style="height: 60%"
          round
          close-on-click-overlay
          position="bottom">
          <div class="flex h-full flex-col p-[12px]">
            <div class="flex justify-between">
              <div @click="onMultipleClose(item)">取消</div>
              <div @click="onMultipleConfirm(item)">确定</div>
            </div>
            <CheckboxGroup
              v-model="multipleValMap[item.name]"
              class="flex-1 overflow-y-auto">
              <Checkbox
                v-for="(c, i) in item.options"
                :key="i"
                class="mt-[12px]"
                :name="c[item.valueKey ?? 'value']">
                {{ c[item.labelKey ?? 'label'] }}
              </Checkbox>
            </CheckboxGroup>
          </div>
        </Popup>
      </template>
      <!-- 自定义组件 -->
      <template v-else-if="customList.includes(item.type)">
        <AutoComplete
          v-if="item.type === 'auto-complete'"
          .="item.props"
          :model-value="item.value"
          input-align="right"
          :placeholder="cellValueRender(item)"
          :readonly="item.readonly || readonly"
          :disabled="item.disabled || disabled"
          @update:model-value="
            (val) => item.events?.change && item.events.change(val)
          " />
      </template>
    </template>
  </Form>
</template>

<script setup>
  import AutoComplete from '@/components/auto-complete/index.vue'
  import {
    ActionSheet,
    Cell,
    Checkbox,
    CheckboxGroup,
    Field,
    Form,
    Popup,
  } from 'vant'
  import { onMounted, reactive } from 'vue'

  const props = defineProps({
    /**
     * name:'字段名'
     * type:'表单项类型'
     * value：绑定的数据
     * placeholder
     * rules
     * render: 表单项是否渲染
     */
    options: {
      type: Array,
      default: () => [],
    },
    readonly: Boolean,
    disabled: Boolean,
  })

  /** 通用组件 */
  const renderTemplateList = ['field', 'textarea']
  /** 需要弹出层的组件 */
  const hasPopupList = ['action-sheet', 'multiple']
  /** 自定义组件 */
  const customList = ['auto-complete']
  // 弹出层展示管理
  const showMap = reactive({})
  // 多选项值管理
  const multipleValMap = reactive({})

  onMounted(() => {
    props.options.forEach((item) => {
      if (hasPopupList.includes(item.type)) {
        if (!item.name) {
          throw new Error(`带有弹出层的配置项未配置“name”属性，${item.type}`)
        }
        if (showMap[item.name]) {
          throw new Error(`重复注册同名组件，${item.type}`)
        }
        showMap[item.name] = false
        if (item.type == 'multiple') {
          multipleValMap[item.name] = item.value || []
        }
      }
    })
  })

  function getFormItem(type) {
    switch (type) {
      case 'field':
      case 'textarea':
        return Field
    }
  }

  function cellValueRender(item) {
    if (
      item.value === void 0 ||
      item.value === null ||
      item.value?.length === 0
    ) {
      if (props.disabled || props.readonly || item.disabled || item.readonly) {
        return ''
      }
      return item.placeholder || ''
    }
    if (typeof item.value === 'boolean') {
      return item.value ? '是' : '否'
    }
    if (Array.isArray(item.value)) {
      const result = item.options
        .filter((i) => item.value.includes(i[item?.valueKey ?? 'value']))
        .map((i) => i[item?.labelKey ?? 'label'])
        .join(',')
      return result
    }
    return item.value
  }

  function onMultipleClose(item) {
    showMap[item.name] = false
    multipleValMap[item.name] = item.value
  }

  function onMultipleConfirm(item) {
    item.events?.confirm && item.events.confirm(multipleValMap[item.name])
    showMap[item.name] = false
  }

  function itemIsEdit(item) {
    return (
      !item.readonly && !item.disabled && !props.readonly && !props.disabled
    )
  }
</script>

<style lang="scss" scoped></style>
