<template>
  <div class="flex h-full w-full flex-col overflow-hidden bg-[#F7F8FA]">
    <div>
      <Search
        v-model="keyword"
        shape="round"
        placeholder="输入搜索关键词"
        @clear="onSearch"
        @search="onSearch"></Search>
    </div>
    <!-- 产业链筛选 -->
    <div
      v-if="type !== '应用场景'"
      class="content_filter">
      <div class="flex h-[24px] w-full items-center justify-between">
        <!-- <div
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showTypeMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ type || '应用场景' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div> -->
        <div
          v-if="interfacePath === 'park' || interfacePath === 'building'"
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showAreaMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ area || '行政区' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div>
        <div
          v-if="type !== '楼宇'"
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ industry || '产业链' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div>
      </div>
    </div>

    <div class="w-full flex-1 overflow-y-auto px-[16px] py-[12px]">
      <!-- 标题 总数 -->
      <div class="flex w-full items-center justify-between">
        <Title>{{ title }}</Title>
      </div>
      <div
        v-if="!loading"
        class="list mt-[16px]">
        <!-- 列表 -->
        <div
          v-for="(item, index) in tableData"
          :key="index"
          class="card relative"
          @click="handlerToDetail(item.id)">
          <!-- 列表标题 -->
          <div class="title">
            <div class="flex w-full items-center justify-between gap-[10px]">
              <span class="flex-1">{{ item.name }}</span>
              <Tag
                v-if="interfacePath === 'building'"
                type="primary"
                >{{ item?.dataInfo?.szxzq || '暂无数据' }}</Tag
              >
              <Tag
                v-if="interfacePath === 'park' && item.district"
                type="primary">
                <!-- <span
                :style="{
                  fontSize: item.district?.length > 5 ? '8px' : '10px',
                }"> -->
                <span class="text-ellipsis">
                  {{ item.district }}
                </span>
              </Tag>
              <Tag
                v-if="
                  (interfacePath === 'colleges' ||
                    interfacePath === 'innovate' ||
                    interfacePath === 'association') &&
                  item.ssxzq
                "
                type="primary">
                <span class="text-ellipsis">
                  {{ item.ssxzq }}
                </span>
              </Tag>
            </div>
          </div>
          <!-- 联系人 -->
          <div
            v-if="interfacePath === 'park' || interfacePath === 'scene'"
            class="line_text mt-[6px]">
            <p
              v-if="interfacePath !== 'park'"
              class="text">
              <i class="iconfont icon-peace text-[12px]" />
              {{ item?.lxr || item?.dataInfo?.lxr || '暂无联系人' }}
            </p>
            <p
              v-else
              class="text">
              <i class="iconfont icon-peace text-[12px]" />
              {{ item?.phonename || '暂无联系人' }}
            </p>
          </div>
          <!-- 联系方式 -->
          <div
            v-if="
              interfacePath === 'park' ||
              interfacePath === 'scene' ||
              interfacePath === 'building'
            "
            class="line_text mt-[6px]">
            <p
              v-if="interfacePath !== 'park'"
              class="text">
              <i class="iconfont icon-xt_PhoneOutlined text-[12px]" />
              {{
                item?.lxdh?.replaceAll('\\n', '') ||
                item.dataInfo?.lxdh?.replaceAll('\\n', '') ||
                '暂无联系方式'
              }}
            </p>
            <p
              v-else
              class="text">
              <i class="iconfont icon-xt_PhoneOutlined text-[12px]" />
              {{ item.phone?.replaceAll('\\n', '') || '暂无联系方式' }}
            </p>
          </div>

          <!-- 学院 -->
          <div
            v-if="interfacePath === 'colleges'"
            class="line_text mt-[6px]">
            <p class="text">
              {{ item?.xy || '暂无学院' }}
            </p>
          </div>

          <!-- 地址 -->
          <div
            v-if="type !== '应用场景'"
            class="line_text mt-[6px]">
            <p
              v-if="interfacePath !== 'park'"
              class="text">
              <!-- <i class="iconfont icon-positioning2 text-[12px]" /> -->
              {{ item.dz?.replaceAll('\\n', '') || '暂无地址' }}
            </p>
            <p
              v-else
              class="text">
              <!-- <i class="iconfont icon-positioning2 text-[12px]" /> -->
              {{ item.address?.replaceAll('\\n', '') || '暂无地址' }}
            </p>
          </div>
          <!-- 产业链 -->
          <div
            v-if="
              interfacePath === 'colleges' ||
              interfacePath === 'innovate' ||
              interfacePath === 'association'
            "
            class="line_text mt-[6px]">
            <p class="text">
              {{ item?.industrychain || '暂无产业链' }}
            </p>
          </div>

          <!-- 箭头icon -->
          <i
            class="iconfont icon-RightCircle absolute bottom-[10px] right-[16px] flex text-[12px] text-[#000]/[.26]"></i>
        </div>
      </div>
      <div
        v-else
        class="flex justify-center">
        <Loading></Loading>
      </div>
      <div
        v-show="tableData.length === 0 && !loading"
        class="flex justify-center">
        <Empty description="暂无数据" />
      </div>
    </div>
  </div>

  <ActionSheet
    v-model:show="showMenu"
    :actions="industryOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onIndustryOptionsChange"></ActionSheet>
  <ActionSheet
    v-model:show="showAreaMenu"
    :actions="areaOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onAreaOptionsChange"></ActionSheet>
</template>

<script setup>
  import * as apis from '@/apis/index'
  import Title from '@/components/title/index.vue'
  import { INDUSTRY_OPTIONS } from '@/views/cygk/options.js'
  import { areaOptionsDefault } from '@/views/recommend/options'
  import { ActionSheet, Empty, Loading, Search, Tag } from 'vant'
  import { computed, onActivated, onMounted, ref } from 'vue'
  import { onBeforeRouteLeave, useRoute } from 'vue-router'
  import { useRouter } from 'vue-router/dist/vue-router'

  const router = useRouter()
  const route = useRoute()
  const loading = ref(false)
  const showMenu = ref(false)
  const showAreaMenu = ref(false)
  // const showTypeMenu = ref(false)
  const industry = ref('产业链')
  const area = ref('行政区')
  const type = ref('应用场景')
  const keyword = ref('')
  const industryOptions = computed(() => {
    return [{ name: '产业链' }].concat(
      INDUSTRY_OPTIONS.map((m) => ({ name: m.value })),
    )
  })
  const areaOptions = computed(() => {
    return [{ name: '行政区' }].concat(
      areaOptionsDefault.map((m) => ({ name: m.text })),
    )
  })

  const title = computed(() => {
    return {
      ['应用场景']: '应用简介',
      ['园区']: '园区',
      ['楼宇']: '楼宇',
      ['高等院校']: '高等院校简介',
      ['科研院所']: '科研院所简介',
      ['功能平台']: '功能平台简介',
      ['协会联盟']: '协会联盟简介',
    }[type.value]
  })
  // scene
  const interfacePath = ref('scene')
  const handlerToDetail = (id) => {
    router.push({
      path: '/enterprise-side/introduce-detail',
      query: {
        id,
        type: interfacePath.value,
        key: {
          building: '楼宇详情',
          park: '园区详情',
          colleges: '高等院校详情',
          research: '科研院所详情',
          association: '协会联盟详情',
          innovate: '功能平台详情',
          scene: '应用场景详情',
        }[interfacePath.value],
      },
    })
  }
  const tableData = ref([])
  const totalNumber = ref(0)

  const onIndustryOptionsChange = (option) => {
    industry.value = option.name
    onSearch()
  }

  const onAreaOptionsChange = (option) => {
    area.value = option.name
    onSearch()
  }

  const onSearch = async () => {
    try {
      loading.value = true
      const data = {
        name: keyword.value,
        industrychain: industry.value === '产业链' ? '' : industry.value,
      }
      if (interfacePath.value === 'park') {
        data.district = area.value === '行政区' ? undefined : area.value
      } else if (interfacePath.value === 'building') {
        data.szxzq = area.value === '行政区' ? undefined : area.value
      }

      if (!industry.value) delete data.industrychain
      if (type.value === '应用场景') delete data.industrychain

      const res = await apis.getAllPageList(interfacePath.value, data)
      tableData.value = res.data.data.map((item) => {
        let data
        try {
          if (item.data_info && typeof item.data_info === 'string') {
            data = JSON.parse(item?.data_info)
          }
        } catch {
          data = {}
        }
        return {
          ...item,
          dataInfo: data,
        }
      })
      // 楼宇
      if (interfacePath.value === 'building') {
        const arr = tableData.value
          .map((item) => item.tl)
          .map((item) => {
            if (item) {
              // 是否有万
              if (item.includes('万')) {
                const num = item.split('平方米')[0]
                return parseFloat(num.split('万')[0]) * 10000
              }
              return parseFloat(item.split('平方米')[0])
            } else {
              return 0
            }
          })

        const total = arr.reduce((a, b) => a + b, 0)
        totalNumber.value = total
      }
      // 园区
      if (interfacePath.value === 'park') {
        const arr = tableData.value
          .map((item) => item.grossarea)
          .map((item) => {
            if (item) {
              if (item.includes('亩')) {
                const num = item.split('亩')[0]
                if (!isNaN(parseFloat(num))) {
                  return parseFloat(num)
                }
              } else {
                return 0
              }
            } else {
              return 0
            }
          })

        const total = arr.filter((item) => item).reduce((a, b) => a + b, 0)
        totalNumber.value = total
      }
      // 记录当前的类型已经模块
      localStorage.setItem('type', type.value)
      localStorage.setItem('industry', industry.value)
      localStorage.setItem('interfacePath', interfacePath.value)
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onActivated(() => {
    if (type.value !== route.query.key) {
      type.value = route.query.key
      interfacePath.value = route.query.type
      const localIndustry = localStorage.getItem('industry')
      if (route.query.type === 'park' && localIndustry) {
        industry.value = localIndustry
      }
      onSearch()
    }
  })

  onBeforeRouteLeave((to) => {
    if (to.path !== '/enterprise-side/introduce-detail') {
      industry.value = '产业链'
      keyword.value = ''
      area.value = '行政区'
      onSearch()
    }
  })

  onMounted(() => {
    type.value = route.query.key
    interfacePath.value = route.query.type
    const localIndustry = localStorage.getItem('industry')
    if (route.query.type === 'park' && localIndustry) {
      industry.value = localIndustry
    }
    onSearch()
  })
</script>

<style lang="scss" scoped>
  .content_filter {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 16px;
    background: #fff;
  }

  .card {
    width: 100%;
    margin-top: 16px;
    padding: 16px;
    border-radius: 12px;
    background-color: #fff;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .text-ellipsis {
        overflow: hidden;
        max-width: 100px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .line_text {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 22px;
      color: rgb(0 0 0 / 40%);

      .text {
        overflow: hidden;
        width: 283px;
        height: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .total {
    font-size: 12px;
  }

  .mj {
    position: relative;
    font-size: 12px;

    &::after {
      content: '';
      position: absolute;
      top: 30%;
      right: -3px;
      width: 1px;
      height: 50%;
      background-color: rgb(0 0 0 / 6%);
    }
  }
</style>
