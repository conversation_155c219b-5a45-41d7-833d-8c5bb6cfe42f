<template>
  <div class="add_ent">
    <div class="iframe_box">
      <iframe
        :src="`${iframeBaseUrl}/addOrEdit?name=${name}&uid=${uid}&id=${id}&key=${key}`" />
    </div>
  </div>
</template>

<script setup>
  import { usePreciseStore } from '@/stores/percise'
  import { onMounted, onUnmounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const preciseStore = usePreciseStore()

  const iframeBaseUrl = import.meta.env.VITE_ADD_SCHEME_URL

  const route = useRoute()
  const router = useRouter()
  const name = route.query.name || ''
  const uid = route.query.uid || preciseStore.uid
  const id = route.query.id || ''
  const key = route.query.key || ''

  function onMessage({ data }) {
    if (data.type === 'back') {
      router.back()
    } else if (data.type === 'detail') {
      router.push({
        path: '/precise/record',
        query: {
          id: data.id,
          type: data.action,
          recordId: data.record?.bid,
          key: {
            view: '查看跟进记录',
            edit: '修改跟进记录',
            add: '新增跟进记录',
          }[data.action],
        },
      })
    }
  }

  onMounted(() => {
    window.addEventListener('message', onMessage)
  })

  onUnmounted(() => {
    window.removeEventListener('message', onMessage)
  })
</script>

<style lang="scss" scoped>
  .add_ent {
    height: 100%;
    background: #f7f8fa;

    .iframe_box {
      height: 100%;
    }

    iframe {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      border: none;
    }
  }
</style>
