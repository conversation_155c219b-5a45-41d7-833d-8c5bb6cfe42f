<template>
  <!-- 底部弹出 -->
  <Popup
    v-model:show="popupShow"
    round
    position="bottom"
    closeable
    :style="{ height: '60%' }"
    @closed="$emit('onPopupClosed')"
    @open="onOpen">
    <div class="popup_main">
      <header>
        <h1>{{ curNode.chainName }}</h1>
      </header>
      <div class="main_content">
        <template v-if="list && list.length > 0">
          <div
            v-for="item in list"
            :key="item.companyId"
            class="company_card"
            @click="handleClick(item)">
            <h1>{{ item.companyName }}</h1>
            <div
              v-if="item.companyTags && isArray(item.companyTags)"
              class="card_tags">
              <div
                v-for="tag in item.companyTags"
                :key="tag"
                class="tag">
                {{ tag }}
              </div>
            </div>
          </div>
        </template>
        <Empty
          v-else
          description="暂无数据" />
      </div>
    </div>
  </Popup>
</template>

<script setup>
  import { getCompanyListByNode } from '@/apis/precise'
  import { isArray, isEmpty } from '@/utils/is'
  import { Popup, Empty, showLoadingToast, closeToast } from 'vant'
  import { ref } from 'vue'
  import { useRouter } from 'vue-router'

  defineEmits(['onPopupClosed'])

  const props = defineProps({
    curNode: {
      type: Object,
      default: () => ({}),
    },
  })

  const popupShow = defineModel({ type: Boolean })

  // 企业列表
  const list = ref([])
  async function getCompanyList(id) {
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
      })
      const {
        data: { data: res },
      } = await getCompanyListByNode({
        page: {
          pageNum: 1,
          pageSize: 999999,
        },
        params: {
          currentChainId: id,
        },
      })
      list.value = res?.list || []
    } catch (error) {
      console.log(error)
    }
    closeToast()
  }
  // 打开弹窗
  const onOpen = () => {
    if (!isEmpty(props.curNode)) {
      getCompanyList(props.curNode.id)
    }
  }

  // 点击卡片跳转企业详情
  const router = useRouter()
  function handleClick(item) {
    const { companyId } = item
    router.push(`/recommend-detail?companyId=${companyId}`)
  }
</script>

<style lang="scss" scoped>
  .popup_main {
    display: flex;
    flex-direction: column;
    height: 100%;

    & > header {
      flex: 0;
      width: 100%;
      padding: 16px 0 10px;

      & > h1 {
        color: #323233;
        font-weight: 600;
        font-size: 16px;
        text-align: center;
      }
    }

    .main_content {
      flex: 1;
      overflow: auto;
      padding: 0 10px 16px 16px;

      .company_card {
        padding: 12px;
        border: 1px solid rgb(0 0 0 / 6%);
        border-radius: 12px;
        background: #fcfcfc;

        & > h1 {
          margin-bottom: 12px;
          color: rgb(0 0 0 / 88%);
          font-weight: 600;
          font-size: 17px;
          line-height: 25px;
        }

        .card_tags {
          display: flex;
          overflow: hidden;

          .tag {
            // flex: 0;
            padding: 2px 8px;
            background: #fffbe6;
            color: #faad14;
            font-size: 12px;
            line-height: 20px;
            white-space: nowrap;

            & + .tag {
              margin-left: 6px;
            }

            &:nth-child(1) {
              background: #fffbe6;
              color: #faad14;
            }

            &:nth-child(2) {
              background: #f6ffed;
              color: #52c41a;
            }

            &:nth-child(3) {
              background: #e6f4ff;
              color: #1677ff;
            }

            &:nth-child(4) {
              background: #f0f5ff;
              color: #2f54eb;
            }
          }
        }

        & + .company_card {
          margin-top: 10px;
        }
      }
    }
  }
</style>
