<template>
  <div class="autocomplete-container">
    <VanPopover
      v-model:show="showSuggestions"
      :placement="placement"
      :overlay="false"
      :trigger="'manual'"
      :teleport="teleport"
      @click-outside="handleClickOutside">
      <template #reference>
        <VanField
          :id="id"
          ref="fieldRef"
          v-model="inputValue"
          :label="label"
          :name="name"
          :type="type"
          :placeholder="placeholder"
          :disabled="disabled"
          :readonly="readonly"
          :clearable="clearable"
          :maxlength="maxlength"
          :border="border"
          :show-word-limit="showWordLimit"
          :error="error"
          :error-message="errorMessage"
          :formatter="formatter"
          :format-trigger="formatTrigger"
          :is-link="isLink"
          :show-error="showError"
          :show-error-message="showErrorMessage"
          :rules="rules"
          :left-icon="leftIcon"
          :right-icon="rightIcon"
          :clickable="clickable"
          :input-align="inputAlign"
          :required="required"
          :enterkeyhint="enterkeyhint"
          @update:model-value="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
          @clear="handleClear"
          @click-left-icon="$emit('click-left-icon')"
          @click-right-icon="$emit('click-right-icon')"
          @keydown.up.prevent="highlightPrevious"
          @keydown.down.prevent="highlightNext"
          @keydown.enter.prevent="handleEnter"
          @keydown.esc="handleEscape">
          <template
            v-if="$slots['left-icon']"
            #left-icon>
            <slot name="left-icon" />
          </template>
          <template
            v-if="$slots['right-icon']"
            #right-icon>
            <slot name="right-icon" />
          </template>
          <template
            v-if="$slots.label"
            #label>
            <slot name="label" />
          </template>
          <template
            v-if="$slots.input"
            #input>
            <slot name="input" />
          </template>
          <template
            v-if="$slots.button"
            #button>
            <slot name="button" />
          </template>
        </VanField>
      </template>

      <div
        class="suggestion-list"
        :style="{ width: dropdownWidth }">
        <!-- 加载状态 -->
        <div
          v-if="loading"
          class="suggestion-item loading-item">
          <slot name="loading">
            <VanLoading size="16" />
            <span class="loading-text">搜索中...</span>
          </slot>
        </div>

        <!-- 建议列表 -->
        <template v-else-if="suggestions.length > 0">
          <div
            v-for="(item, index) in suggestions"
            :key="getItemKey(item, index)"
            :class="[
              'suggestion-item',
              { 'suggestion-item--active': highlightedIndex === index },
            ]"
            @click="handleSelect(item, index)"
            @mouseenter="highlightedIndex = index">
            <slot
              :item="item"
              :index="index">
              {{ getItemValue(item) }}
            </slot>
          </div>
        </template>

        <!-- 无数据 -->
        <div
          v-else-if="!loading && inputValue"
          class="suggestion-item no-data">
          <slot name="empty">无匹配数据</slot>
        </div>
      </div>
    </VanPopover>
  </div>
</template>

<script setup>
  import { useDebounceFn } from '@vueuse/core'
  import {
    Field as VanField,
    Loading as VanLoading,
    Popover as VanPopover,
  } from 'vant'
  import { nextTick, onBeforeUnmount, ref, watch } from 'vue'

  // 组件名称
  defineOptions({
    name: 'AutoComplete',
  })

  // Props 定义
  const props = defineProps({
    // v-model 绑定值
    modelValue: {
      type: [String, Number],
      default: '',
    },
    // 名称，作为提交表单时的标识符
    name: String,
    // 输入框 id，同时会设置 label 的 for 属性
    id: String,
    // 输入框类型, 支持原生 input 标签的所有 type 属性，额外支持了 digit 类型
    type: {
      type: String,
      default: 'text',
    },
    // 是否显示内边框
    border: {
      type: Boolean,
      default: true,
    },
    // 获取建议的方法
    fetchSuggestions: {
      type: Function,
      required: true,
    },
    // 防抖延迟时间
    debounce: {
      type: Number,
      default: 500,
    },
    // 弹出位置
    placement: {
      type: String,
      default: 'bottom-start',
    },
    // 是否在输入框聚焦时触发搜索
    triggerOnFocus: {
      type: Boolean,
      default: true,
    },
    // 是否默认高亮第一个选项
    highlightFirstItem: {
      type: Boolean,
      default: false,
    },
    // 当没有匹配项时，按回车是否触发 select 事件
    selectWhenUnmatched: {
      type: Boolean,
      default: false,
    },
    // 显示值的键名
    valueKey: {
      type: String,
      default: 'value',
    },
    // 唯一标识的键名
    keyProp: {
      type: String,
      default: 'key',
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false,
    },
    // 是否隐藏加载图标
    hideLoading: {
      type: Boolean,
      default: false,
    },
    // 是否传送到 body
    teleport: {
      type: [String, Element],
      default: 'body',
    },
    // 最小触发搜索的字符长度
    minLength: {
      type: Number,
      default: 1,
    },
    // Field 组件的其他属性
    label: String,
    placeholder: String,
    clearable: Boolean,
    maxlength: [String, Number],
    showWordLimit: Boolean,
    error: Boolean,
    errorMessage: String,
    formatter: Function,
    formatTrigger: {
      type: String,
      default: 'onChange',
    },
    isLink: Boolean,
    showError: Boolean,
    showErrorMessage: {
      type: Boolean,
      default: true,
    },
    rules: Array,
    leftIcon: String,
    rightIcon: String,
    clickable: Boolean,
    required: Boolean,
    enterkeyhint: {
      type: String,
      default: 'search',
    },
    inputAlign: String,
  })

  // 事件定义
  const emit = defineEmits([
    'update:modelValue',
    'input',
    'focus',
    'blur',
    'clear',
    'select',
    'click-left-icon',
    'click-right-icon',
  ])

  // 响应式数据
  const fieldRef = ref()
  const inputValue = ref(props.modelValue)
  const suggestions = ref([])
  const showSuggestions = ref(false)
  const highlightedIndex = ref(-1)
  const loading = ref(false)
  const dropdownWidth = ref('auto')
  const activated = ref(false)

  // 防抖定时器
  let debounceTimer = null

  // 监听外部 v-model 变化
  watch(
    () => props.modelValue,
    (newVal) => {
      inputValue.value = newVal
    },
  )

  // 监听内部输入值变化
  watch(inputValue, (newVal) => {
    emit('update:modelValue', newVal)
    emit('input', newVal)
  })

  // 获取项目显示值
  const getItemValue = (item) => {
    if (typeof item === 'string') return item
    return item[props.valueKey] || ''
  }

  // 获取项目唯一标识
  const getItemKey = (item, index) => {
    if (typeof item === 'string') return `${item}-${index}`
    return item[props.keyProp] || `item-${index}`
  }

  // 获取建议
  const fetchSuggestions = async (query) => {
    if (props.disabled || loading.value) return

    // 检查最小长度
    if (query.length < props.minLength) {
      suggestions.value = []
      showSuggestions.value = false
      return
    }

    try {
      loading.value = !props.hideLoading

      const result = await props.fetchSuggestions(query)

      if (Array.isArray(result)) {
        suggestions.value = result
        highlightedIndex.value =
          props.highlightFirstItem && result.length > 0 ? 0 : -1

        if (activated.value && (result.length > 0 || loading.value)) {
          console.log('showSuggestions', showSuggestions.value)
          showSuggestions.value = true
          await nextTick()
          updateDropdownWidth()
        }
      } else {
        console.warn('fetchSuggestions should return an array')
        suggestions.value = []
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error)
      suggestions.value = []
    } finally {
      loading.value = false
    }
  }
  // 防抖搜索
  const debouncedFetchSuggestions = useDebounceFn(
    fetchSuggestions,
    props.debounce,
  )

  // 更新下拉框宽度
  const updateDropdownWidth = () => {
    if (fieldRef.value && fieldRef.value.$el) {
      dropdownWidth.value = `${fieldRef.value.$el.offsetWidth}px`
    }
  }

  // 处理输入
  const handleInput = () => {
    console.log('input', inputValue.value)
    activated.value = true

    if (!props.triggerOnFocus && !inputValue.value) {
      suggestions.value = []
      showSuggestions.value = false
      return
    }

    debouncedFetchSuggestions(inputValue.value)
  }

  // 处理聚焦
  const handleFocus = (event) => {
    activated.value = true
    emit('focus', event)

    if (props.triggerOnFocus && !props.readonly) {
      debouncedFetchSuggestions(inputValue.value || '')
    }
  }

  // 处理失焦
  const handleBlur = (event) => {
    // 延迟关闭，给点击选项留时间
    setTimeout(() => {
      if (activated.value) {
        close()
      }
      emit('blur', event)
    }, 200)
  }

  // 处理清空
  const handleClear = () => {
    activated.value = false
    suggestions.value = []
    showSuggestions.value = false
    emit('clear')
  }

  // 处理选择
  const handleSelect = (item) => {
    const value = getItemValue(item)
    inputValue.value = value

    suggestions.value = []
    showSuggestions.value = false
    highlightedIndex.value = -1
    activated.value = false

    emit('select', item)
  }

  // 处理回车
  const handleEnter = () => {
    if (!showSuggestions.value) return

    if (
      highlightedIndex.value >= 0 &&
      highlightedIndex.value < suggestions.value.length
    ) {
      handleSelect(
        suggestions.value[highlightedIndex.value],
        highlightedIndex.value,
      )
    } else if (props.selectWhenUnmatched) {
      emit('select', { [props.valueKey]: inputValue.value })
      close()
    }
  }

  // 处理 ESC
  const handleEscape = () => {
    if (showSuggestions.value) {
      close()
    }
  }

  // 高亮上一项
  const highlightPrevious = () => {
    if (!showSuggestions.value || loading.value) return

    if (highlightedIndex.value <= 0) {
      highlightedIndex.value = suggestions.value.length - 1
    } else {
      highlightedIndex.value--
    }
  }

  // 高亮下一项
  const highlightNext = () => {
    if (!showSuggestions.value || loading.value) return

    if (highlightedIndex.value >= suggestions.value.length - 1) {
      highlightedIndex.value = 0
    } else {
      highlightedIndex.value++
    }
  }

  // 关闭建议框
  const close = () => {
    activated.value = false
    showSuggestions.value = false
    highlightedIndex.value = -1
  }

  // 处理点击外部
  const handleClickOutside = () => {
    if (showSuggestions.value) {
      close()
    }
  }

  // 暴露的方法
  const focus = () => {
    fieldRef.value?.focus()
  }

  const blur = () => {
    fieldRef.value?.blur()
  }

  // 清理定时器
  onBeforeUnmount(() => {
    if (debounceTimer) {
      clearTimeout(debounceTimer)
    }
  })

  // 暴露方法和数据
  defineExpose({
    focus,
    blur,
    close,
    highlightedIndex,
    activated,
    loading,
    suggestions,
    handleSelect,
  })
</script>

<style scoped>
  .autocomplete-container {
    position: relative;
    width: 100%;
  }

  .suggestion-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    scrollbar-width: thin;
  }

  .suggestion-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .suggestion-item:last-child {
    border-bottom: none;
  }

  .suggestion-item:hover,
  .suggestion-item--active {
    background-color: #f5f7fa;
  }

  .loading-item {
    justify-content: center;
    cursor: default;
  }

  .loading-item:hover {
    background-color: #fff;
  }

  .loading-text {
    margin-left: 8px;
    color: #999;
    font-size: 14px;
  }

  .no-data {
    color: #999;
    text-align: center;
    cursor: default;
  }

  .no-data:hover {
    background-color: #fff;
  }

  /* 因为input 被 popover__wrapper 包了一层 无法获取父元素的宽  */
  :deep(.van-popover__wrapper) {
    width: 100%;
  }

  /* 滚动条样式 */
  /* .suggestion-list::-webkit-scrollbar {
    width: 4px;
  }

  .suggestion-list::-webkit-scrollbar-track {
    border-radius: 2px;
    background: #f1f1f1;
  }

  .suggestion-list::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background: #c1c1c1;
  }

  .suggestion-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  } */
</style>
