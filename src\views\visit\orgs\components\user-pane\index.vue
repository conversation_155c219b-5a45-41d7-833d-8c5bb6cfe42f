<template>
  <div class="page">
    <header>
      <Row>
        <Col :span="19">
          <Search
            v-model="keyword"
            shape="round"
            placeholder="请输入搜索关键词"
            @search="onSearch"
            @clear="onSearch" />
        </Col>
        <Col :span="5">
          <div class="flex h-full items-center">
            <button @click="handleAdd">添加企业</button>
          </div>
        </Col>
      </Row>
    </header>
    <div class="list">
      <div
        v-if="loading"
        class="flex h-full w-full items-center justify-center pt-[20px]">
        <Loading
          vertical
          :size="28">
          加载中...
        </Loading>
      </div>

      <template v-else-if="data.length">
        <CompanyCard
          v-for="item in data"
          :key="item.bid"
          :item="item"
          :has-action="true"
          :show-share-person="true"
          :show-area="false"
          @on-share="handleShare"
          @on-detail="handleDetail"
          @on-edit="handleEdit"
          @on-delete="handleDelete" />
      </template>

      <Empty
        v-else-if="data.length === 0"
        :image="keyword ? 'search' : undefined"
        :description="keyword ? '未找到相关企业' : '暂无企业数据'" />
    </div>

    <!-- 选择人员进行共享企业的访问权限 -->
    <PersonPopup
      v-model:show="showSharePop"
      title="共享"
      :list="shareUsers"
      check-key="isShare"
      empty-text="暂无符合条件的人员"
      :loading="shareLoading"
      value-key="uid"
      @confirm="submitShare" />
  </div>
</template>

<script setup>
  import { deleteOrg, getOrgs, getShearUsers, shareUser } from '@/apis/visit'
  import PersonPopup from '@/components/person-popup/index.vue'
  // import { useDict } from '@/stores/dict'
  import { useVisitOrgFormStore, useVisitUserStore } from '@/stores/visit'
  import { updateState, useRequest } from 'alova/client'
  import { Col, Empty, Loading, Row, Search, showConfirmDialog } from 'vant'
  import { onActivated, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import CompanyCard from '../company-card.vue'

  // const { companyTypes } = useDict('companyTypes')
  // 搜索关键词
  const keyword = ref('')
  const visitUserStore = useVisitUserStore()
  // 表格数据
  const { loading, data, send } = useRequest(
    () =>
      getOrgs({
        uid: visitUserStore.uid,
        keyword: keyword.value,
      }),
    {
      initialData: [],
    },
  )

  // 点击搜索
  const onSearch = () => {
    send()
  }

  const router = useRouter()
  // 点击新增
  const handleAdd = () => {
    router.push(`/visit/org-form`)
  }

  const showSharePop = ref(false)
  const curCompanyBid = ref(null)
  // 共享用户列表数据
  const {
    loading: shareLoading,
    data: shareUsers,
    send: sendGetShareUsers,
  } = useRequest(
    (bid) =>
      getShearUsers({ uid: visitUserStore.uid, basicInformationBid: bid }),
    {
      immediate: false,
    },
  )
  // 共享给其他用户
  const handleShare = (bid) => {
    showSharePop.value = true
    sendGetShareUsers(bid)
    curCompanyBid.value = bid
  }

  const { send: sendShareUsers } = useRequest((data) => shareUser(data), {
    immediate: false,
  })
  // 提交共享
  const submitShare = (ids) => {
    const users = shareUsers.value
      .filter(({ uid }) => ids.includes(uid))
      .map((item) => {
        return {
          uid: visitUserStore.uid,
          basicInformationBid: curCompanyBid.value,
          targetUid: item.uid,
          targetNickname: item.nickname,
        }
      })
    sendShareUsers(users)
  }

  // 查看
  const handleDetail = (id) => {
    router.push(`/visit/org-detail/${id}`)
  }
  // 编辑
  const handleEdit = (id) => {
    router.push(`/visit/org-form/${id}`)
  }

  const { send: deleteOrgSend } = useRequest(
    (bid) =>
      deleteOrg({
        bid,
        uid: visitUserStore.uid,
      }),
    {
      immediate: false,
    },
  )
  // 删除
  const handleDelete = (id) => {
    showConfirmDialog({
      title: '确认删除',
      message: '确认删除该条内容？',
      beforeClose: async (action) => {
        if (action === 'confirm') {
          try {
            await deleteOrgSend(id)
            // 手动更新数据
            updateState(
              getOrgs({
                uid: visitUserStore.uid,
                keyword: keyword.value,
              }),
              (oldListData) => {
                const index = oldListData.findIndex(({ bid }) => bid === id)
                oldListData.splice(index, 1)
                return oldListData
              },
            )
          } catch (error) {
            console.log(error)
          }
        }
        return Promise.resolve(true)
      },
    })
  }

  const store = useVisitOrgFormStore()
  onActivated(() => {
    // 有过更新操作，重新加载数据列表。
    if (store.formActionType) {
      send()
      store.formActionType = ''
    }
  })
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    overflow-y: auto;
    background-color: #f7f8fa;
    & > header {
      width: 100%;
      height: 66px;
      padding: 8px 0px;
      background-color: #fff;

      --van-search-input-height: 40px;

      button {
        color: #1255e4;
        font-size: 16px;
        &:active {
          opacity: 0.6;
        }
      }
    }

    .list {
      padding: 16px;
    }
  }
</style>
