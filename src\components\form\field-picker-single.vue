<template>
  <div class="w-full">
    <Field
      :model-value="pickerValue.label"
      :is-link="!readonly"
      :readonly="readonly"
      :name="name"
      :label="label"
      placeholder="点击选择"
      :rules="rules"
      @click="visible = true" />
    <Popup
      v-if="!readonly"
      v-model:show="visible"
      destroy-on-close
      :position="position">
      <Picker
        :columns="options"
        :model-value="pickerValue.pValue"
        @confirm="onPickerConfirm"
        @cancel="visible = false" />
    </Popup>
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { Field, Popup, Picker } from 'vant'
  import { computed, ref } from 'vue'
  // 单列选择器
  defineOptions({
    name: 'FieldPickerSingle',
  })

  const props = defineProps({
    name: String,
    label: String,
    options: {
      type: Array,
      default: () => [],
    },
    readonly: Boolean,
    position: {
      type: String,
      default: 'bottom',
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    rules: {
      type: Array,
      default: () => [],
    },
  })

  // 所选中的选项的值
  const selectedValue = defineModel({
    type: [String, Number],
    required: true,
  })

  // 控制选择器显示
  const visible = ref(false)
  const pickerValue = computed({
    get() {
      const value = selectedValue.value
      if (isEmpty(selectedValue.value)) {
        return []
      }
      const label =
        props.options.find((item) => item.value === value)?.text || ''
      // 返回的是选中项value的数组
      return {
        pValue: [value],
        label,
      }
    },
    set(value) {
      selectedValue.value = value[0]
    },
  })

  /**
 * {
      "selectedValues": [
          1
      ],
      "selectedOptions": [
          {
              "text": "男",
              "value": 1
          }
      ],
      "selectedIndexes": [
          0
      ]
  }
  * @param value
  */
  function onPickerConfirm({ selectedValues }) {
    pickerValue.value = selectedValues
    visible.value = false
  }
</script>

<style lang="scss" scoped></style>
