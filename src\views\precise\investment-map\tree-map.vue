<template>
  <div
    id="treeMap"
    class="tree_map">
  </div>
</template>

<script setup>
  import { isEmpty } from '@/utils/is'
  import { Graph, NodeEvent, treeToGraphData } from '@antv/g6'
  import { onMounted, onUnmounted, watch } from 'vue'

  const emit = defineEmits(['handleNodeClick'])

  const props = defineProps({
    // 当前选中的产业链编码
    selectedCode: {
      type: String,
      default: '',
      required: true,
    },
    // 当前选中的产业链对象
    selectedChainData: {
      type: Object,
      default: () => ({}),
      required: true,
    },
  })

  // 图实例
  let graph = null

  function toTreeData(data) {
    return treeToGraphData(data, {
      getNodeData: (datum, depth) => {
        if (!datum.style) datum.style = {}
        datum.style.collapsed = depth >= 1
        if (!datum.children) return datum
        const { children, ...restDatum } = datum
        return { ...restDatum, children: children.map((child) => child.id) }
      },
    })
    // return graphData
  }

  onMounted(() => {
    // 把树形数据转换为图数据格式
    const data = !isEmpty(props.selectedChainData)
      ? toTreeData(props.selectedChainData)
      : []
    graph = new Graph({
      container: 'treeMap',
      autoFit: 'view',
      // zoom: 1.5,
      data: data,
      behaviors: [
        'drag-canvas',
        { type: 'zoom-canvas' },
        // 手机端
        { type: 'zoom-canvas', trigger: ['pinch'] },
        {
          type: 'collapse-expand',
          animation: false,
          trigger: 'click',
        },
        {
          type: 'click-select',
          key: 'click-select-1',
          degree: 0, // 选中扩散范围
        },
      ],
      node: {
        type: 'diamond',
        style: (node) => {
          // console.log(node.style.collapsed)
          const options = {
            // collapsed: true,
            size: 30,
            fill: '#76A2FF', // 填充色
            labelText: `${node.chainName}（${node.companyCount || 0}）`,
            labelPlacement: 'left',
            labelFill: 'rgba(0, 0, 0, 0.90)',
            labelFontSize: 16,
            labelPadding: [4, 10],
            labelOffsetX: -10,
            // labelOffsetX: 20,
            // labelTextAlign: 'left',
            labelLineHeight: 30,
            labelBackground: true,
            labelBackgroundFill: '#E8F0FF',
            labelBackgroundRadius: 4,
          }
          // 根据节点类型设置背景颜色
          switch (String(node.chainAttribute)) {
            // 强链
            case '1':
              options.labelBackgroundFill = '#92DAB2'
              break
            // 补链
            case '2':
              options.labelBackgroundFill = '#BED2FF'
              break
            // 延链
            case '3': // 根节点下的第一个子节点，固定蓝色背景
              options.labelBackgroundFill = '#FF9285'
              break
          }

          // 根节点固定蓝色，label颜色为白色
          if (node.isRoot) {
            options.labelBackgroundFill = 'rgba(18, 85, 228, 1)'
            options.labelFill = '#FFFFFF'
            // options.labelTextAlign = 'right'
            options.labelOffsetX = -15
          }

          // 当前节点下有子节点，且子节点没有children，则在右侧显示label
          if (node.labelPlacementRight) {
            options.labelPlacement = 'right'
            options.labelOffsetX = 10
          }

          return options
        },
        animation: {
          enter: false,
        },
      },
      edge: {
        type: 'cubic-horizontal',
        animation: {
          enter: false,
        },
      },
      layout: {
        type: 'compact-box',
        direction: 'LR',
        getHeight: function getHeight() {
          return 42
        },
        getWidth: function getWidth() {
          return 32
        },
        getVGap: function getVGap() {
          return 14
        },
        getHGap: function getHGap() {
          return 100
        },
      },
    })

    graph.render()

    // 渲染开始前
    // graph.on(GraphEvent.BEFORE_RENDER, () => {
    //   console.log('渲染开始...')
    // })

    // // 渲染完成后
    // graph.on(GraphEvent.AFTER_RENDER, () => {
    //   console.log('渲染完成')
    // })

    /**
     * 监听节点点击事件
     * 可以通过点击的原始目标来区别点击的是节点label还是节点元素
     */
    graph.on(NodeEvent.CLICK, (evt) => {
      const { target, originalTarget } = evt // 获取被点击节点的 ID

      console.log('点击的节点:', originalTarget)

      if (originalTarget.nodeName !== 'text') return

      // 获取节点数据
      const nodeData = graph.getNodeData(target.id)
      console.log('节点数据:', nodeData)
      emit('handleNodeClick', nodeData)
    })
  })

  // 组件销毁时，清除图实例
  onUnmounted(() => {
    if (graph) {
      graph.destroy()
      graph = null
    }
  })

  // 监听selectedCode变化，重新渲染图
  watch(
    () => props.selectedChainData,
    (newVal) => {
      if (!isEmpty(newVal) && graph) {
        graph.setData(toTreeData(props.selectedChainData))
        graph.render()
      }
    },
  )
</script>

<style lang="scss" scoped>
  .tree_map {
    width: 100%;
    height: 100%;
  }
</style>
