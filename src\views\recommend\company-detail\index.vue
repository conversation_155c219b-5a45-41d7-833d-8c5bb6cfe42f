<template>
  <div class="container">
    <div
      ref="listRef"
      class="list">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="p-[8px]"
        :style="{
          backgroundColor: index % 2 === 0 ? '#f1f7fc' : '#e8eefd',
        }">
        <div
          class="info"
          @click="onClick(item)">
          <template
            v-for="(labelkeyItem, idx) in detailLabeKeyMap[route.query.key]
              .list"
            :key="idx">
            <div
              v-if="labelkeyItem.type === 'title'"
              class="title">
              {{ item[labelkeyItem.key] }}
            </div>

            <div
              v-else
              class="info_item">
              <label v-if="labelkeyItem.label">{{ labelkeyItem.label }}</label>
              <p class="text">
                <template v-if="item[labelkeyItem.key]">
                  {{
                    labelkeyItem.handle
                      ? labelkeyItem.handle(item[labelkeyItem.key], item)
                      : item[labelkeyItem.key]
                  }}
                  <span v-if="labelkeyItem.unit">{{ labelkeyItem.unit }}</span>
                </template>
                <span v-else>-</span>
              </p>
            </div>
          </template>
        </div>
      </div>
      <div
        v-show="loading"
        class="mt-[16px] flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
        <Loading v-show="loading"> 加载中... </Loading>
      </div>
      <div
        v-show="!loading && list.length === 0"
        class="flex justify-center rounded-[8px] bg-[#f7f8fa] px-[16px] py-[8px] text-[14px]">
        暂无数据
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useInfiniteScroll } from '@vueuse/core'
  import { Loading, showFailToast, showSuccessToast } from 'vant'
  import { onMounted, reactive, ref, useTemplateRef } from 'vue'
  import { useRoute } from 'vue-router'
  import { detailApiMap, detailLabeKeyMap } from '../options'

  const route = useRoute()
  const listRef = useTemplateRef('listRef')
  const list = ref([])
  const loading = ref(false)

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    total: 100,
  })

  useInfiniteScroll(
    listRef,
    () => {
      onSearch()
    },
    {
      interval: 200,
      canLoadMore: () => !loading.value && pagination.total > list.value.length,
    },
  )

  async function onClick(item) {
    console.log(item)
    const linkKey = detailLabeKeyMap[route.query.key]?.actions?.copy
    if (linkKey) {
      if (!item[linkKey.key]) {
        showFailToast('暂无详情链接')
        return
      }
      try {
        await navigator.clipboard.writeText(item[linkKey.key])
        showSuccessToast('详情链接复制成功')
      } catch (err) {
        showFailToast('详情链接复制失败')
        console.log(err)
      }
    }
  }

  const onSearch = async () => {
    loading.value = true
    try {
      const key = route.query.key
      const companyId = route.query.companyId
      const {
        data: { data },
      } = await detailApiMap[key]({
        companyId,
        page: pagination.page,
        pageSize: pagination.pageSize,
      })
      console.log(data)
      list.value.push(...data.rowData)
      pagination.page++
      pagination.total = data.total
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    onSearch()
  })
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px - 44px);

    .list {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: 10px;
      overflow-y: auto;
      padding: 16px;
      background-color: #fff;

      .title {
        color: #333;
        font-weight: bold;
        font-size: 16px;
      }

      .info {
        &_item {
          display: flex;
          gap: 5px;
          margin-top: 5px;

          .text {
            flex: 1;
          }
        }
      }
    }
  }
</style>
