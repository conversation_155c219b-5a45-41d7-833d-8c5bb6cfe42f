import httpClient from '@/utils/http'

// 企业推荐-详情数据接口
const baseUrl = import.meta.env.VITE_API_RECOMMEND_DETAIL_URL

// 获取token
export const getToken = () =>
  httpClient.get(
    `${baseUrl}/token/generate?appKey=JiqO05S0b9SChmF9&appSecret=12TtCpSiTwB8GE7uO3l9TivFCgNEHGQc`,
  )

const apiHandler = (url, params) => {
  return httpClient.post(`${baseUrl}${url}`, params, {
    headers: {
      apiToken: sessionStorage.getItem('apiToken'),
    },
  })
}

// 投资线索
export const investmentLeadsApi = (params) =>
  apiHandler('/erecommend/subdomain/intention/investment', params)

// 融资线索
export const financingLeadsApi = (params) =>
  apiHandler('/erecommend/subdomain/intention/financing', params)

// 新闻线索
export const newsCluesApi = (params) =>
  apiHandler('/erecommend/subdomain/intention/newsinfo', params)
// 开庭公告
export const announcementApi = (params) =>
  apiHandler('/erecommend/subdomain/risk/courtnotice', params)
// 行政处罚
export const administrativePenaltyApi = (params) =>
  apiHandler('/erecommend/subdomain/risk/penalty', params)
// 欠税信息
export const taxArrearsApi = (params) =>
  apiHandler('/erecommend/subdomain/risk/tax', params)
// 限制高消费
export const restrictingHighConsumptionApi = (params) =>
  apiHandler('/erecommend/enterprise-portraits/risk/limit-highconsume', params)
// 重大税收违法
export const taxViolationApi = (params) =>
  apiHandler('/erecommend/enterprise-portraits/risk/tax-illegal-info', params)
// 对外投资
export const overseasInvestmentApi = (params) =>
  apiHandler(
    '/erecommend/enterprise-portraits/business-info/investinfo',
    params,
  )
// 融资事件
export const financingEventApi = (params) =>
  apiHandler(
    '/erecommend/enterprise-portraits/business-info/financing-event',
    params,
  )
// 资质证书
export const qualificationCertificateApi = (params) =>
  apiHandler(
    '/erecommend/enterprise-portraits/business-info/certificate',
    params,
  )
// 专利
export const patentApi = (params) =>
  apiHandler('/erecommend/enterprise-portraits/intell/patent', params)
// 软著
export const softCopyrightApi = (params) =>
  apiHandler('/erecommend/enterprise-portraits/intell/soft-works', params)
// 商标
export const trademarkApi = (params) =>
  apiHandler('/erecommend/enterprise-portraits/intell/trademark', params)
// 招投标
export const biddingApi = (params) =>
  apiHandler('/erecommend/subdomain/intention/tendering-info', params)
// 招聘信息
export const recruitmentApi = (params) =>
  apiHandler('/erecommend/subdomain/recruitinfo', params)
