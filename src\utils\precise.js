import {
  getCountBy<PERSON><PERSON>,
  getFavListByUid,
  starEnt,
  unStarEnt,
} from '@/apis/precise'

/**
 * 收藏
 */
export const onFavEnt = async (uid, entId, cb) => {
  try {
    const res = await starEnt({
      userBid: uid,
      companyId: entId,
    })
    cb?.onSuccess && cb.onSuccess(res)
  } catch (error) {
    console.log(error)
    cb?.onError && cb.onError(error)
  }
}

/**
 * 取消收藏
 */
export const onUnFavEnt = async (uid, bid, cb) => {
  try {
    const res = await unStarEnt({
      userBid: uid,
      relatedLabelBid: bid,
    })
    cb.onSuccess && cb.onSuccess(res)
  } catch (error) {
    console.log(error)
    cb.onError && cb.onError(error)
  }
}
export const onUnFavEnt2 = async (uid, companyId, cb) => {
  try {
    const res = await unStarEnt({
      userBid: uid,
      companyId,
    })
    cb.onSuccess && cb.onSuccess(res)
  } catch (error) {
    console.log(error)
    cb.onError && cb.onError(error)
  }
}

/**
 * 获取收藏列表
 */
export const getFavIds = async (uid, cb) => {
  try {
    const res = await getFavListByUid(uid)
    cb.onSuccess && cb.onSuccess(res)
  } catch (error) {
    console.log(error)
    cb.onError && cb.onError(error)
  }
}

/**
 * 获取计数
 */
export const useCount = async (param, cb) => {
  try {
    const res = await getCountByKey(param)
    cb.onSuccess && cb.onSuccess(res)
  } catch (error) {
    console.log(error)
    cb.onError && cb.onError(error)
  }
}
