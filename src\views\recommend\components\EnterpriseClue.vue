<template>
  <div class="card">
    <div class="title">
      <div class="deco"></div>
      <div class="title-text">企业线索</div>
    </div>
    <div
      class="tabs"
      @click="onTabClick">
      <div
        class="tab"
        :class="{ is_active: isActive === '0' }"
        data-key="0"
        >企业实力</div
      >
      <div
        class="tab"
        :class="{ is_active: isActive === '1' }"
        data-key="1"
        >扩张意愿</div
      >
      <div
        class="tab"
        :class="{ is_active: isActive === '2' }"
        data-key="2"
        >企业风险</div
      >
    </div>
    <div>
      <Component
        :is="showComponent"
        :info />
    </div>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue'
  import EnterpriseRisk from './EnterpriseClue/EnterpriseRisk.vue'
  import EnterpriseStrength from './EnterpriseClue/EnterpriseStrength.vue'
  import ExpansionIntention from './EnterpriseClue/ExpansionIntention.vue'

  const isActive = ref('0')

  defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  function onTabClick(e) {
    isActive.value = e.target.dataset.key
  }

  const showComponent = computed(() => {
    if (isActive.value === '0') {
      // 企业实力
      return EnterpriseStrength
    } else if (isActive.value === '1') {
      // 扩张意愿
      return ExpansionIntention
    } else {
      // 企业风险
      return EnterpriseRisk
    }
  })
</script>

<style lang="scss" scoped>
  .card {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;

    .title {
      display: flex;
      align-items: center;

      .deco {
        width: 4px;
        height: 16px;
        margin-right: 4px;
        border-radius: 16px;
        background: #1255e4;
      }

      .title-text {
        color: rgb(0 0 0 / 88%);
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
      }
    }

    .tabs {
      display: flex;
      flex-wrap: wrap;
      gap: 13px;
      margin-top: 12px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e7e7e7;

      .tab {
        display: flex;
        flex-shrink: 0;
        justify-content: center;
        align-items: center;
        width: 76px;
        height: 28px;
        border-radius: 6px;
        background: #f3f3f3;
        color: rgb(0 0 0 / 60%);
        font-weight: 600;
        font-size: 14px;

        &.is_active {
          border: 1px solid #1255e4;
          background: #fff;
          color: #1255e4;
        }
      }
    }
  }
</style>
