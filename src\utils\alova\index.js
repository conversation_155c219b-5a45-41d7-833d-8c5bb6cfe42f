import { axiosMockResponse, axiosRequestAdapter } from '@alova/adapter-axios'
import { createAlovaMockAdapter } from '@alova/mock'
import { createAlova, Method } from 'alova'
import VueHook from 'alova/vue'
import service from '../http.js'
import mockGroup from './mock/alova.mock.js'

// 继承request 的请求适配器实例
const axiosRequestAdapterInstance = axiosRequestAdapter({
  axios: service.instance,
})

// mock 请求适配器实例
const mockAdapter = createAlovaMockAdapter([...Object.values(mockGroup)], {
  // 全局控制是否启用mock接口，默认为true
  enable: true,

  // 非模拟请求适配器，用于未匹配mock接口时发送请求
  httpAdapter: axiosRequestAdapterInstance,

  // mock接口响应延迟，单位毫秒
  delay: 1000,

  // 是否打印mock接口请求信息
  mockRequestLogger: true,
  /**
   * 使用的axios适配器，所以需要将模拟数据转换为AxiosResponse和AxiosError兼容的格式
   * httpAdapter 不会走onMockResponse
   */
  // onMockResponse(response) {
  //   return {
  //     response: {
  //       ...response,
  //       data: response.body,
  //     },
  //   }
  // },
  ...axiosMockResponse,
})

// 创建 Alova 实例
const alovaInstance = createAlova({
  // 请求适配器 在不同环境下使用不同的请求适配器实例
  requestAdapter: import.meta.env.DEV
    ? mockAdapter
    : axiosRequestAdapterInstance,
  // 客户端
  statesHook: VueHook,
  // 缓存时间，单位毫秒
  cacheFor: import.meta.env.DEV ? 0 : 8000,
})

function fetchData(method, url, data = {}, config = {}) {
  const defaultConfig = {
    // 是否提取res数据的data
    transformRes: true,
  }
  config = { ...defaultConfig, ...config }

  if (config.transformRes && !config.transform) {
    // data.data 作为返回结果
    config.transform = (res) => {
      return res?.data?.data ? res.data.data : res
    }
  }

  let methodInstance
  if (method === 'GET') {
    methodInstance = new Method(method, alovaInstance, url, {
      params: data,
      ...config,
    })
  } else {
    methodInstance = new Method(method, alovaInstance, url, { ...config }, data)
  }

  return methodInstance
}

export function get(url, params, config) {
  return fetchData('GET', url, params, config)
}

export function post(url, data, config) {
  return fetchData('POST', url, data, config)
}

export function put(url, data, config) {
  return fetchData('PUT', url, data, config)
}

export function remove(url, data, config) {
  return fetchData('DELETE', url, data, config)
}

export function upload(url, data, config) {
  const formData = new FormData()
  Object.keys(data).forEach((child) => {
    formData.append(child, data[child])
  })
  return post(url, formData, config)
}

export default alovaInstance
