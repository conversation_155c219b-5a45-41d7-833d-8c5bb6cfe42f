<template>
  <!-- iframe嵌入pc方案生成 ，页面中政策详情使用postMessage进行跳转-->
  <div class="scheme">
    <iframe
      :src="`${iframeBaseUrl}/scheme?uid=${preciseStore.uid}&name=${name}`" />
  </div>
</template>

<script setup>
  import { usePreciseStore } from '@/stores/percise'
  import { onMounted, onUnmounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const preciseStore = usePreciseStore()

  const iframeBaseUrl = import.meta.env.VITE_ADD_SCHEME_URL

  const route = useRoute()
  const router = useRouter()
  const name = route.query.name || ''

  function onMessage(e) {
    if (e.data.type === 'list') {
      router.push('/cygk/policy-list')
    } else if (e.data.type === 'detail') {
      router.push({
        path: '/cygk/policy-detail',
        query: {
          id: e.data.id,
        },
      })
    } else if (e.data.type === 'preview') {
      const id = e.data.data
      router.push({
        path: '/precise/scheme-preview',
        query: {
          id,
        },
      })
    }
  }

  onMounted(() => {
    window.addEventListener('message', onMessage)
  })

  onUnmounted(() => {
    window.removeEventListener('message', onMessage)
  })
</script>

<style lang="scss" scoped>
  .scheme {
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    iframe {
      width: 100%;
      height: 100%;
      margin: 0;
      padding: 0;
      border: none;
      resize: none;
    }
  }
</style>
