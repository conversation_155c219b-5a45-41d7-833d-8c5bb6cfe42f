import { getImgUrl } from '@/apis'
import { phoneClickCount } from '@/apis/precise'

/**
 * 电话计数
 */
export const useCall = (call) => {
  try {
    phoneClickCount({
      phoneNumber: call,
    })
  } catch (error) {
    console.log(error)
  }
}

export const getImageUrl = async (url) => {
  try {
    const {
      data: { data },
    } = await getImgUrl(url)
    return data
  } catch (error) {
    console.log(error)
  }
}
