<!-- 重点企业热力图 -->
<template>
  <div class="container">
    <div class="h-[330px] w-full">
      <img
        class="h-full w-full"
        :src="currentImage"
        @click="show = true" />
    </div>
    <div class="bottom">
      <div class="bg-[#fff] pb-[10px]">
        <div class="h-[1040px] w-full">
          <VChart
            autoresize
            :option="option"
            class="h-full w-full"></VChart>
        </div>

        <h2 class="text-center font-[16px] text-[#1255E4]">
          重点企业合计：{{ currentData.data.qiye_depth || 0 }}家
        </h2>
      </div>
    </div>
  </div>
  <ImagePreview
    v-model:show="show"
    :images="[currentImage]"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import allData from '@/assets/js/cyjs'
  import { extractFileName } from '@/utils/index'
  import { ImagePreview } from 'vant'
  import { computed, inject, ref } from 'vue'
  const currentType = inject('currentType')
  const currentData = computed(() => {
    return Object.values(allData).find((item) => {
      return item.data.cyfl.includes(currentType.value)
    })
  })
  const currentImage = computed(() => {
    const imagesPath = currentData.value.data['rltimg']

    if (!imagesPath) return undefined
    const _thisImagesName = extractFileName(imagesPath)
    console.log('_thisImagesName', _thisImagesName)

    return new URL(`./images/${_thisImagesName}`, import.meta.url).href
  })
  const show = ref(false)
  const option = computed(() => ({
    xAxis: {
      type: 'value',
    },
    yAxis: {
      type: 'category',
      data: currentData.value.data.qiye_data2.map((item) => item.country),
      axisLabel: {
        show: false,
      },
      splitLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: currentData.value.data.qiye_data2.map((item) => item.litres),
        type: 'bar',
        barWidth: 20,
        zlevel: 1,
        itemStyle: {
          barBorderRadius: [0, 20, 20, 0], // 圆角（左上、右上、右下、左下）
        },
        label: {
          normal: {
            color: '#000',
            show: true,
            position: [0, '-20px'],
            textStyle: {
              fontSize: 12,
              color: '#999999',
            },
            formatter: '{b}',
          },
        },
      },
      {
        type: 'bar',
        data: currentData.value.data.qiye_data2.map((item) => item.litres),
        barWidth: 20,
        barGap: '-100%',
        itemStyle: {
          normal: {
            color: '#f5f8ff',
          },
          emphasis: {
            color: '#f5f8ff',
          },
        },
        label: {
          normal: {
            color: '#333333',
            show: true,
            position: 'right',
            distance: 4,
            textStyle: {
              fontSize: 14,
            },
            formatter: '{c}',
          },
        },
      },
    ],
  }))
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 10px;
    background-color: #f7f8fa;

    .bottom {
      width: 100%;
      margin-top: 16px;
      padding: 16px;
      background-color: #f7f8fa;
    }
  }
</style>
