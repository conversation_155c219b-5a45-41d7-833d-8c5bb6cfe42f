<template>
  <div class="container">
    <EnterpriseInfo
      :info="{
        ...info.companyBasicInfoVo,
        ...info.recommendReasonVo,
        ...info.companyPushInfoVo,
        ...info.companyStrengthVo,
      }"></EnterpriseInfo>

    <div class="flex-1 overflow-y-auto">
      <div class="flex items-center px-[16px]">
        <div
          class="local_button mt-[16px] flex-1"
          @click="onAssociation">
          本地关联
        </div>
        <div
          class="fav_button"
          @click="handleFav">
          <i
            class="iconfont"
            :class="info?.stared ? 'icon-StarFilled' : 'icon-StarOutlined'"
            :style="
              info?.stared
                ? getTextGradient(['#FFE292', '#FF9C4A'], 'top')
                : { color: 'rgba(0, 0, 0, 0.1)' }
            "></i>
          <div class="fav_text">{{ info?.stared ? '取消' : '收藏' }}</div>
        </div>
      </div>
      <CorporatePortrait
        :info
        class="mt-[16px]" />
    </div>
  </div>
</template>

<script setup>
  import { getRecommendAllDetail } from '@/apis/recommend'
  import { getToken } from '@/apis/recommend-detail'
  import { usePreciseStore } from '@/stores/percise'
  import { useRecommendStore } from '@/stores/recommend'
  import { getTextGradient } from '@/utils'
  import { onFavEnt, onUnFavEnt2 } from '@/utils/precise'
  import CorporatePortrait from '@/views/recommend/components/CorporatePortrait.vue'
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import EnterpriseInfo from './components/EnterpriseInfo.vue'

  const route = useRoute()
  const router = useRouter()
  const recommendStore = useRecommendStore()
  const preciseStore = usePreciseStore()

  const info = ref({})
  const userInfo = ref({})

  function onAssociation() {
    router.push(
      `/local-connection?companyName=${info.value.companyBasicInfoVo.companyName}`,
    )
  }

  async function getApiToken() {
    try {
      const {
        data: { data },
      } = await getToken()
      sessionStorage.setItem('apiToken', data)
    } catch (error) {
      console.log(error)
    }
  }

  onMounted(() => {
    const apiToken = sessionStorage.getItem('apiToken')
    if (!apiToken) {
      getApiToken()
    }
  })

  async function getDetail() {
    try {
      const {
        data: { data: res },
      } = await getRecommendAllDetail({
        companyId: route.query.companyId,
        userBid: preciseStore.uid,
      })
      info.value = res
    } catch (error) {
      console.log(error)
    }
  }

  onMounted(async () => {
    userInfo.value = await recommendStore.getRecommendUserInfo(
      route.query.userName,
    )
    getDetail()
  })

  function handleFav() {
    console.log(preciseStore)

    info.value?.stared
      ? onUnFavEnt2(preciseStore.uid, info.value.companyBasicInfoVo.companyId, {
          onSuccess() {
            const obj = preciseStore.preciseRecommendCache.list.find(
              (i) => i.companyId === info.value.companyBasicInfoVo.companyId,
            )
            obj?.stared && (obj.stared = false)
            info.value.stared = false
          },
        })
      : onFavEnt(preciseStore.uid, info.value.companyBasicInfoVo.companyId, {
          onSuccess() {
            const obj = preciseStore.preciseRecommendCache.list.find(
              (i) => i.companyId === info.value.companyBasicInfoVo.companyId,
            )
            obj?.stared && (obj.stared = true)
            info.value.stared = true
          },
        })
  }
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f7f8fa;

    .local_button {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 44px;
      border-radius: 999px;
      background: rgb(0 118 246 / 6%);
      color: #0076f6;
      font-size: 14px;
    }

    .fav_button {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 16px;
      margin-left: 16px;

      .fav_text {
        color: #9e9e9e;
        font-size: 14px;
      }
    }

    .reason {
      display: flex;
      flex-direction: column;
      margin-bottom: 16px;
      padding: 0 16px 16px;
      background-color: #fff;
    }

    .title {
      display: flex;
      align-items: center;

      .deco {
        width: 4px;
        height: 16px;
        margin-right: 4px;
        border-radius: 16px;
        background: #1255e4;
      }

      .title-text {
        color: rgb(0 0 0 / 88%);
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
</style>
