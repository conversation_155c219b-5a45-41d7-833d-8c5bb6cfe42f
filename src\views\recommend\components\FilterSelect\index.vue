<template>
  <div class="h-[56px] px-[16px] py-[8px]">
    <Search
      v-model="keyword"
      style="height: 40px"
      placeholder="搜索企业"
      shape="round"
      @search="onSearch"
      @clear="onSearch" />
  </div>
  <div class="filter_select">
    <template v-if="showArea">
      <template v-if="!isAll">
        <div class="filter_item">
          <span
            class="mr-[4px]"
            @click="isShowArea = true">
            {{ area ? area : '区域' }}
          </span>
          <Icon
            v-if="area"
            name="close"
            class="text-[#999ba5]"
            @click="
              () => {
                area = ''
                onSearch()
              }
            " />
          <i
            v-else
            class="iconfont icon-caret-down-small text-[8px] text-[#DCDEE0]" />
        </div>
        <Popup
          v-if="isShowArea"
          v-model:show="isShowArea"
          position="bottom">
          <Picker
            :columns="areaOptions"
            @cancel="isShowArea = false"
            @confirm="
              ({ selectedValues }) => {
                area = selectedValues[0]
                isShowArea = false
                onSearch()
              }
            " />
        </Popup>
      </template>
      <template v-else>
        <div class="filter_item">
          <span
            class="mr-[4px]"
            @click="isShowArea = true">
            <span>区域</span>
          </span>
          <Icon
            v-if="area?.length"
            name="close"
            class="text-[#999ba5]"
            @click="
              () => {
                area = []
                areaTreeRef.reset()
                emits('clearArea')
                onSearch()
              }
            " />
          <i
            v-else
            class="iconfont icon-caret-down-small text-[8px] text-[#DCDEE0]" />
        </div>
        <TreePicker
          ref="areaTreeRef"
          v-model:show="isShowArea"
          :options="preJson"
          label-name="name"
          value-name="code"
          :selected="initData.area"
          @cancel="isShowArea = false"
          @confirm="
            (vals) => {
              console.log('vals', vals)
              area = vals
              isShowArea = false
              onSearch()
            }
          " />
      </template>
    </template>

    <template v-if="isAll">
      <div class="filter_item">
        <span
          class="mr-[4px]"
          @click="isShowIndustry = true">
          产业
        </span>
        <i
          v-show="industry.length === 0"
          class="iconfont icon-caret-down-small text-[8px] text-[#DCDEE0]" />
        <Icon
          v-if="industry.length > 0"
          name="close"
          class="text-[#999ba5]"
          @click="
            () => {
              industry = []
              DeepTreePickerRef.reset()
              onSearch()
            }
          " />
      </div>
      <DeepTreePicker
        ref="DeepTreePickerRef"
        v-model:show="isShowIndustry"
        :selected="industry"
        label-name="name"
        value-name="code"
        :options="industryOptions"
        @cancel="isShowIndustry = false"
        @confirm="
          (vals) => {
            industry = vals
            isShowIndustry = false
            onSearch()
          }
        " />
    </template>
    <template v-if="!isAll">
      <div class="filter_item">
        <span
          class="mr-[4px]"
          @click="isShowTime = true">
          推送时间
        </span>

        <Icon
          v-if="times.end.length > 0"
          name="close"
          class="text-[#999ba5]"
          @click="
            () => {
              times = { start: [], end: [] }
              onSearch()
            }
          " />
        <i
          v-else
          class="iconfont icon-caret-down-small text-[8px] text-[#DCDEE0]" />
      </div>
      <Popup
        v-model:show="isShowTime"
        destroy-on-close
        round
        position="bottom">
        <PickerGroup
          title="推送时间"
          :tabs="['开始时间', '结束时间']"
          @confirm="
            () => {
              isShowTime = false
              onSearch()
            }
          "
          @cancel="isShowTime = false">
          <DatePicker
            v-model="times.start"
            :min-date="new Date(2025, 0, 1)"
            :max-date="new Date()" />
          <DatePicker
            v-model="times.end"
            :min-date="minData"
            :max-date="new Date()" />
        </PickerGroup>
      </Popup>
    </template>
  </div>
</template>

<script setup>
  import preJson from '@/assets/json/pca-code.json'
  import DeepTreePicker from '@/components/deep-tree-picker/index.vue'
  import TreePicker from '@/components/tree-picker/index.vue'
  import { DatePicker, Icon, Picker, PickerGroup, Popup, Search } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import { areaOptions } from '../../options'

  const emits = defineEmits(['search', 'clearArea', 'clearTime'])
  const props = defineProps({
    isAll: {
      type: Boolean,
      default: false,
    },
    industryOptions: {
      type: Array,
      default: () => [],
    },
    showArea: {
      type: Boolean,
      default: true,
    },
    initData: {
      type: Object,
      default: () => {},
    },
  })

  const keyword = ref('')
  const times = ref({ start: [], end: [] })
  const area = ref('')
  const industry = ref([])
  const DeepTreePickerRef = ref(null)
  const areaTreeRef = ref(null)

  const isShowArea = ref(false)
  const isShowTime = ref(false)
  const isShowIndustry = ref(false)

  const minData = computed(() => {
    if (times.value.start.length > 0) {
      return new Date(
        times.value.start[0],
        times.value.start[1] - 1,
        times.value.start[2],
      )
    } else {
      return undefined
    }
  })

  function onSearch() {
    const params = {
      keyword,
      area,
      industry,
      times,
    }
    if (props.isAll) {
      delete params.times
    } else {
      delete params.industry
    }
    emits('search', params)
  }

  onMounted(() => {
    console.log('初始化筛选', props.initData)
    keyword.value =
      props.initData?.similarCompanyName || props.initData?.keyword || ''

    let _area = ''
    if (props.initData?.region) {
      _area = props.initData?.region
    } else {
      _area = props.initData?.area
    }
    area.value = _area

    const _times = { start: [], end: [] }
    if (props.initData?.after && props.initData?.after !== '') {
      _times.start =
        props.initData?.after?.split('-').map((item) => Number(item)) || []
    }
    if (props.initData?.before && props.initData?.before !== '') {
      _times.end =
        props.initData?.before?.split('-').map((item) => Number(item)) || []
    }
    times.value = _times
    industry.value = props.initData?.industry || []
  })
</script>

<style lang="scss" scoped>
  .filter_select {
    display: flex;
    align-items: center;
    height: 48px;

    .filter_item {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      width: 180px;
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
