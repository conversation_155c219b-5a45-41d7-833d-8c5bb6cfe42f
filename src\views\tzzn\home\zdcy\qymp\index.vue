<template>
  <div class="main_page">
    <img
      :src="qymp"
      alt=""
      @click="show = true" />
    <ImagePreview
      v-model:show="show"
      :images="[qymp]"
      :close-on-click-image="false">
      <template #image="{ src }">
        <img
          :src="src"
          class="w-full" />
      </template>
    </ImagePreview>
  </div>
</template>

<script setup>
import qymp from '@/assets/images/tzzn/zdcy/qymp-detail.png'
import { ImagePreview } from 'vant'
import { ref } from 'vue'
const show = ref(false)
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  img {
    width: 100%;
  }
}
</style>
