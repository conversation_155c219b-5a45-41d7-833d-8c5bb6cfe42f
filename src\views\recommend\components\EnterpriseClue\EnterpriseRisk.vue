<template>
  <div class="pt-[24px]">
    <div
      v-for="(_, index) in Math.ceil(items.length / 2)"
      :key="index"
      class="risk_line">
      <div
        v-if="index * 2 <= items.length"
        class="risk_item">
        <div class="h-[4px] w-[4px] bg-[rgba(0,0,0,0.9)]"></div>
        <div class="label">{{ items[index * 2].label }}</div>
        <div
          class="value"
          @click="onItemClick(items[index * 2])"
          >{{ items[index * 2].value }}</div
        >
        <div class="unit">{{ items[index * 2].unit }}</div>
      </div>
      <div
        v-if="index * 2 + 1 < items.length"
        class="risk_item">
        <div class="h-[4px] w-[4px] bg-[rgba(0,0,0,0.9)]"></div>
        <div class="label">{{ items[index * 2 + 1].label }}</div>
        <div
          class="value"
          @click="onItemClick(items[index * 2 + 1])"
          >{{ items[index * 2 + 1].value }}</div
        >
        <div class="unit">{{ items[index * 2 + 1].unit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const router = useRouter()

  const items = computed(() => [
    {
      label: '开庭公告',
      value: info.companyRiskVo?.courtnoticeCount || 0,
      unit: '起',
    },
    {
      label: '行政处罚',
      value: info.companyRiskVo?.penaltyCount || 0,
      unit: '起',
    },
    {
      label: '欠税信息',
      value: info.companyRiskVo?.taxOweCount || 0,
      unit: '项',
    },
    {
      label: '限制高消费',
      value: info.companyRiskVo?.limitHighconsumeCount || 0,
      unit: '条',
    },
    {
      label: '重大税收违法',
      value: info.companyRiskVo?.taxIllegalCount || 0,
      unit: '起',
    },
    // {
    //   label: '负债',
    //   value: 0,
    //   unit: '',
    // },
    // {
    //   label: '负债率',
    //   value: 0,
    //   unit: '%',
    // },
  ])

  function onItemClick(item) {
    console.log(item)
    if (item.value && item.value != 0) {
      router.push({
        path: '/company-detail',
        query: {
          key: item.label,
          companyId: info.companyBasicInfoVo.companyId,
        },
      })
    }
  }
</script>

<style lang="scss" scoped>
  .risk_line {
    display: flex;

    .risk_item {
      display: flex;
      align-items: center;
      width: 165px;
      height: 38px;
      padding: 0 8px;
      border-radius: 6px;

      .label {
        flex: 1;
        margin-left: 4px;
        color: rgb(0 0 0 / 90%);
        font-size: 14px;
      }

      .value {
        color: #1255e4;
        font-weight: 600;
        font-size: 14px;
      }

      .unit {
        color: rgb(0 0 0 / 60%);
        font-size: 14px;
      }

      & + .risk_item {
        margin-left: 13px;
      }
    }

    &:nth-child(odd) {
      .risk_item {
        background: rgb(18 85 228 / 6%);
      }
    }

    &:nth-child(even) {
      .risk_item {
        background: rgb(18 190 228 / 6%);
      }
    }

    & + .risk_line {
      margin-top: 13px;
    }
  }
</style>
