<template>
  <div class="page">
    <Skeleton
      v-if="detailLoading"
      title
      :row="12" />
    <Form
      v-else
      validate-trigger="onSubmit"
      :readonly="isDetail"
      @submit="onSubmit">
      <div class="group">
        <div class="group_header">
          <h1 class="header_title">基本信息</h1>
        </div>
        <CellGroup inset>
          <Field
            :model-value="typeLabel"
            name="type"
            label="类型"
            placeholder="请选择机构类型"
            readonly
            :rules="[
              {
                required: true,
                message: '请选择机构类型',
                trigger: 'onSubmit',
              },
            ]" />
          <Field
            v-model="formData.unitName"
            name="unitName"
            label="单位名称"
            placeholder="请输入单位名称"
            :rules="[
              {
                required: true,
                message: '请输入单位名称',
                trigger: 'onSubmit',
              },
            ]" />
          <Field
            v-model="formData.remarks"
            rows="2"
            autosize
            label="备注"
            type="textarea"
            placeholder="请输入备注" />
        </CellGroup>
      </div>
      <div class="group">
        <div class="group_header">
          <h1 class="header_title">联系人信息</h1>
          <div
            v-if="!isDetail"
            class="header_action">
            <i class="iconfont icon-PlusCircleOutlined1"></i>
            <span @click="addConcat">添加联系人</span>
          </div>
        </div>
        <div class="pl-[16px] pr-[16px]">
          <ContactList
            :list="formData.contactDTOList"
            :is-edit="!isDetail"
            @item-click="onConcatClick"
            @delete="onDeleteConcat"
            @edit="onEditConcat" />
        </div>
      </div>
      <div style="margin: 16px">
        <Button
          v-if="isDetail"
          block
          type="primary"
          @click="toEdit">
          编辑
        </Button>
        <Button
          v-else
          block
          type="primary"
          native-type="submit"
          :loading="loading || updateLoading">
          提交
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup>
  import { addOrg, getOrgDetail, updateOrgInfo } from '@/apis/zszyk'
  import { useOrgFormStore } from '@/stores/zszyk'
  import { isEmpty } from '@/utils/is'
  import { useRequest } from 'alova/client'
  import { storeToRefs } from 'pinia'
  import {
    Button,
    CellGroup,
    Field,
    Form,
    showConfirmDialog,
    Skeleton,
  } from 'vant'
  import { computed, onMounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ContactList from '../components/contact-list.vue'
  import { useOrgTypeOptions } from '../hook'

  const store = useOrgFormStore()
  const { formData } = storeToRefs(store)

  // 机构类型
  const orgTypeOptions = useOrgTypeOptions()

  const route = useRoute()
  const router = useRouter()

  // 类型标签名称
  const typeLabel = computed(() => {
    const type = route.params.type
    const item = orgTypeOptions.value.find((item) => item.type === type)

    return !isEmpty(item) ? item.label : ''
  })
  // 是否是详情模式
  const isDetail = route.meta?.status === 'detail'
  // 机构ID 存在代表编辑机构
  const orgId = route.params.id

  // 进入编辑路由
  function toEdit() {
    router.replace(`/zszyk/org-form/${route.params.type}/${orgId}`)
  }

  // 添加联系人
  const addConcat = () => {
    router.push('/zszyk/concat-form')
  }
  const onConcatClick = (index) => {
    if (isDetail) {
      router.push(`/zszyk/concat-detail/${index}`)
    }
  }
  // 删除联系人
  const onDeleteConcat = (index) => {
    showConfirmDialog({
      title: '确认删除',
      message: '确认删除该联系人？',
    }).then(() => {
      formData.value.contactDTOList.splice(index, 1)
    })
  }
  // 编辑联系人
  const onEditConcat = (index) => {
    router.push(`/zszyk/concat-form/${index}`)
  }

  const { loading, send: addOrgSend } = useRequest((form) => addOrg(form), {
    immediate: false,
  })
  const { loading: updateLoading, send: updateOrgSend } = useRequest(
    (form) => updateOrgInfo(form),
    {
      immediate: false,
    },
  )
  // 表单提交
  const onSubmit = async (values) => {
    console.log('submit', values, formData)
    try {
      if (isEmpty(orgId)) {
        await addOrgSend({
          ...formData.value,
          type: route.params.type,
        })
        store.formActionType = 'added'
      } else {
        await updateOrgSend({
          ...formData.value,
          contactUpdateDTOList: formData.value.contactDTOList,
          type: route.params.type,
        })
        // 编辑机构
        store.formActionType = 'edited'
      }
      back()
    } catch (error) {
      console.log(error)
    }
  }

  function back() {
    router.back()
  }

  // 每次初始进入重置表单数据
  store.$reset()

  const { loading: detailLoading, send: detailSend } = useRequest(
    getOrgDetail({
      bid: orgId,
    }),
    {
      initialData: {},
      immediate: false,
    },
  ).onSuccess(({ data }) => {
    formData.value = {
      ...data.basicInformation,
      contactDTOList: [].concat(data.contactList),
    }
  })
  onMounted(() => {
    if (orgId) {
      // 编辑机构详情加载
      detailSend()
    }
  })
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    padding: 20px 0;
    overflow-y: auto;
    background-color: #f7f8fa;
    .group {
      .group_header {
        display: flex;
        justify-content: space-between;
        padding: 0 16px 16px 16px;
        .header_title {
          color: #969799;
          font-size: 14px;
          line-height: 20px;
          text-align: left;
        }

        .header_action {
          display: flex;
          align-items: center;
          gap: 5px;
          color: #1989fa;
          font-size: 14px;
        }
      }

      & + .group {
        margin-top: 32px;
      }
    }
  }
</style>
