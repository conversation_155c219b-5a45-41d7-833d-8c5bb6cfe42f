import { defineStore } from 'pinia'
import { ref } from 'vue'

export const usePreciseStore = defineStore('precise', () => {
  const preciseRecommendCache = ref({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      next: false,
    },
    filterData: {},
    scroll: 0,
  })

  const pullRecommendCache = ref({
    list: [],
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      next: false,
    },
    filterData: {},
    scroll: 0,
  })

  const targetCache = ref({
    scroll: 0,
    length: 0,
    pagination: {
      total: 0,
      pageNum: 1,
      pageSize: 10,
      next: false,
    },
    keyword: '',
  })

  const uid = ref('')
  const token = ref('')

  return {
    preciseRecommendCache,
    pullRecommendCache,
    uid,
    token,
    targetCache,
  }
})
