// store/modules/dict.js
import { getAreaOptions, getEntTypeList } from '@/apis/options'
import { isEmpty } from '@/utils/is'
import { defineStore } from 'pinia'
import { computed, ref, toRefs } from 'vue'

// 字典类型与接口的映射配置
const DICT_API_MAP = {
  // 企业类别
  companyTypes: {
    api: getEntTypeList,
    default: [],
    params: {}, // 需要有个默认参数值
  },
  // 区下拉
  areaOptions: {
    api: getAreaOptions,
    default: [],
    params: {},
  },
  // 可以添加需要参数的接口
  // company_list: {
  //   api: getCompanyList,
  //   params: { status: 1, pageSize: 100 }
  // },
}

export const useDictStore = defineStore('dict', {
  state: () => ({
    dict: new Map(), // 使用Map提高查找效率
    loading: new Set(), // 记录正在加载的字典类型
  }),

  getters: {
    // 获取所有字典
    getAllDict: (state) => {
      const result = {}
      for (const [key, value] of state.dict) {
        result[key] = value
      }
      return result
    },

    // 检查字典是否存在
    hasDict: (state) => (dictType) => {
      return state.dict.has(dictType)
    },

    // 检查字典是否正在加载
    isLoading: (state) => (dictType) => {
      return state.loading.has(dictType)
    },
  },

  actions: {
    // 获取字典数据
    getDict(dictType) {
      if (!dictType) {
        return null
      }
      return this.dict.get(dictType) || null
    },

    // 设置字典数据
    setDict(dictType, value) {
      if (dictType && value) {
        this.dict.set(dictType, value)
      }
    },

    // 删除指定字典
    removeDict(dictType) {
      if (dictType && this.dict.has(dictType)) {
        this.dict.delete(dictType)
        return true
      }
      return false
    },

    // 清空所有字典
    cleanDict() {
      this.dict.clear()
      this.loading.clear()
    },

    // 从服务器加载字典数据
    async loadDict(dictType, customParams = {}) {
      const loadKey = `${dictType}_${JSON.stringify(customParams)}`

      if (!dictType || this.loading.has(loadKey)) {
        return 'loading'
      }

      // 检查是否有对应的API映射
      const dictConfig = DICT_API_MAP[dictType]
      if (!dictConfig || !dictConfig.api) {
        console.warn(`字典类型 ${dictType} 没有对应的API映射`)
        return
      }

      this.loading.add(loadKey)

      try {
        // 合并默认参数和自定义参数
        const params = { ...dictConfig.params, ...customParams }

        // 调用API获取数据
        const apiData = await dictConfig.api(params)
        // 添加默认值
        const dictData = isEmpty(apiData) ? dictConfig.default : apiData

        // 生成存储key，如果有自定义参数则包含参数信息
        const storeKey =
          Object.keys(customParams).length > 0
            ? `${dictType}_${JSON.stringify(customParams)}`
            : dictType

        this.setDict(storeKey, dictData)
        return dictData
      } catch (error) {
        console.error(`加载字典 ${dictType} 失败:`, error)
        throw error
      } finally {
        this.loading.delete(loadKey)
      }
    },

    // 批量加载字典
    async loadDicts(dictConfigs) {
      const promises = dictConfigs.map((config) => {
        if (typeof config === 'string') {
          return this.loadDict(config)
        } else {
          return this.loadDict(config.dictType, config.params)
        }
      })
      try {
        await Promise.all(promises)
      } catch (error) {
        console.error('批量加载字典失败:', error)
      }
    },

    // 添加新的字典映射
    addDictMapping(dictType, apiConfig) {
      DICT_API_MAP[dictType] = apiConfig
    },

    // 获取字典映射配置
    getDictMapping() {
      return { ...DICT_API_MAP }
    },
  },
})

/**
 * 字典Hook函数
 * @param {...(string|Object)} args 字典类型列表或配置对象
 * @returns {Object} 响应式的字典数据对象
 */
export function useDict(...args) {
  const dictStore = useDictStore()
  const res = ref({})

  // 解析参数，支持字符串和对象两种格式
  const dictConfigs = args.map((arg) => {
    if (typeof arg === 'string') {
      return { dictType: arg, params: {}, key: arg }
    } else {
      const key = arg.key || arg.dictType
      return {
        dictType: arg.dictType,
        params: arg.params || {},
        key,
      }
    }
  })

  // 初始化所有字典类型的响应式数据
  dictConfigs.forEach((config) => {
    res.value[config.key] = []
  })

  // 加载字典数据
  const loadDictData = async () => {
    for (const config of dictConfigs) {
      try {
        // 生成存储key
        const storeKey =
          Object.keys(config.params).length > 0
            ? `${config.dictType}_${JSON.stringify(config.params)}`
            : config.dictType

        // 先检查store中是否已有数据
        const existingDict = dictStore.getDict(storeKey)
        if (existingDict && existingDict.length > 0) {
          res.value[config.key] = existingDict
        } else {
          // 从服务器加载数据
          const dictData = await dictStore.loadDict(
            config.dictType,
            config.params,
          )

          // loading代表此type正在被加载
          if (dictData === 'loading') {
            const unsubscribe = dictStore.$onAction(
              ({
                name, // action 名称
                args,
                after, // 在 action 返回或解决后的钩子
              }) => {
                after(() => {
                  if (name === 'setDict' && args[0] === storeKey) {
                    const existingDict = dictStore.getDict(storeKey)
                    if (existingDict && existingDict.length > 0) {
                      res.value[config.key] = existingDict
                      // 删除订阅
                      unsubscribe()
                    }
                  }
                })
              },
            )
          } else if (!isEmpty(dictData)) {
            res.value[config.key] = dictData
          }
        }
      } catch (error) {
        console.error(`加载字典 ${config.dictType} 失败:`, error)
        res.value[config.key] = []
      }
    }
  }

  // 立即加载数据
  loadDictData()

  return toRefs(res.value)
}

/**
 * 字典标签获取Hook
 * @param {string} dictType 字典类型
 * @param {*} value 值
 * @param {Object} options 配置选项
 * @param {Object} options.params 接口参数
 * @param {string} options.labelKey 标签键名，默认'label'
 * @param {string} options.valueKey 值键名，默认'value'
 * @returns {Function} 根据值获取标签的函数
 */
export function useDictLabel(dictType, value, options = {}) {
  const { params = {}, labelKey = 'label', valueKey = 'value' } = options

  // 使用useDict确保字典数据存在
  const dictKey = dictType

  const dictData = useDict({ dictType, params, key: dictKey })

  // 返回计算属性函数
  const getDictLabel = computed(() => {
    const data = dictData[dictKey].value
    if (!data || !Array.isArray(data)) {
      return null
    }

    const item = data.find((dict) => dict[valueKey] == value)
    return item ? item[labelKey] : ''
  })

  return getDictLabel
}

/**
 * 字典值获取Hook
 * @param {string} dictType 字典类型
 * @param {*} label 标签
 * @param {Object} options 配置选项
 * @param {Object} options.params 接口参数
 * @param {string} options.labelKey 标签键名，默认'label'
 * @param {string} options.valueKey 值键名，默认'value'
 * @returns {Function} 根据标签获取值的函数
 */
export function useDictValue(dictType, label, options = {}) {
  const { params = {}, labelKey = 'label', valueKey = 'value' } = options

  // 使用useDict确保字典数据存在
  const dictKey = dictType

  const dictData = useDict({ dictType, params, key: dictKey })

  // 返回计算属性函数
  const getDictValue = computed(() => {
    const data = dictData[dictKey].value
    if (!data || !Array.isArray(data)) {
      return null
    }

    const item = data.find((dict) => dict[labelKey] === label)
    return item ? item[valueKey] : null
  })

  return getDictValue
}

/**
 * 字典选项获取Hook
 * @param {string} dictType 字典类型
 * @param {Object} options 配置选项
 * @param {Object} options.params 接口参数
 * @returns {ComputedRef} 字典选项数组
 */
export function useDictOptions(dictType, options = {}) {
  const { params = {} } = options

  // 使用useDict确保字典数据存在
  const dictKey = dictType

  const dictData = useDict({ dictType, params, key: dictKey })

  return computed(() => dictData[dictKey].value || [])
}

// 兼容性函数（保持原有API）
/**
 * 根据字典值获取标签
 * @param {string} dictType 字典类型
 * @param {string|number} value 字典值
 * @param {Object} options 配置选项
 * @returns {string} 字典标签
 */
export function getDictLabel(dictType, value, options = {}) {
  const dictStore = useDictStore()
  const { params = {}, labelKey = 'label', valueKey = 'value' } = options

  const storeKey =
    Object.keys(params).length > 0
      ? `${dictType}_${JSON.stringify(params)}`
      : dictType

  const dictData = dictStore.getDict(storeKey)

  if (!dictData || !Array.isArray(dictData)) {
    return null
  }

  const item = dictData.find((dict) => dict[valueKey] == value)
  return item ? item[labelKey] : null
}

/**
 * 根据字典标签获取值
 * @param {string} dictType 字典类型
 * @param {string} label 字典标签
 * @param {Object} options 配置选项
 * @returns {string|number} 字典值
 */
export function getDictValue(dictType, label, options = {}) {
  const dictStore = useDictStore()
  const { params = {}, labelKey = 'label', valueKey = 'value' } = options

  const storeKey =
    Object.keys(params).length > 0
      ? `${dictType}_${JSON.stringify(params)}`
      : dictType

  const dictData = dictStore.getDict(storeKey)

  if (!dictData || !Array.isArray(dictData)) {
    return null
  }

  const item = dictData.find((dict) => dict[labelKey] === label)
  return item ? item[valueKey] : null
}

/**
 * 获取字典选项（用于下拉框等组件）
 * @param {string} dictType 字典类型
 * @param {Object} options 配置选项
 * @returns {Array} 字典选项数组
 */
export function getDictOptions(dictType, options = {}) {
  const dictStore = useDictStore()
  const { params = {} } = options

  const storeKey =
    Object.keys(params).length > 0
      ? `${dictType}_${JSON.stringify(params)}`
      : dictType

  return dictStore.getDict(storeKey) || []
}

// 使用示例：

// 1. 基本使用（无参数）
/*
<template>
  <div>
    <el-select v-model="userStatus">
      <el-option
        v-for="item in user_status"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<script setup>
import { useDict, useDictLabel } from '@/store/modules/dict'

// 使用字典
const { user_status } = useDict('user_status')
const userStatus = ref('')

// 使用Hook获取标签
const getDictLabel = useDictLabel('user_status')
const statusLabel = computed(() => getDictLabel.value(userStatus.value))
</script>
*/

// 2. 带参数使用
/*
<script setup>
import { useDict, useDictLabel, useDictOptions } from '@/store/modules/dict'

// 带参数的字典
const { company_list } = useDict({
  dictType: 'company_list',
  params: { status: 1, type: 'active' },
  key: 'active_companies'
})

// 带参数和自定义键名的标签获取
const getDictLabel = useDictLabel('company_list', {
  params: { status: 1, type: 'active' },
  labelKey: 'name',
  valueKey: 'id'
})

// 获取字典选项
const companyOptions = useDictOptions('company_list', {
  params: { status: 1, type: 'active' }
})
</script>
*/

// 3. 在store中添加需要参数的字典映射
/*
import { useDictStore } from '@/store/modules/dict'
import { getCompanyList } from '@/apis/company'

const dictStore = useDictStore()
dictStore.addDictMapping('company_list', {
  api: getCompanyList,
  params: { pageSize: 100 }
})
*/
