<template>
  <div class="risk-info">
    <div class="info">
      <div class="title">司法</div>
      <div class="btn-box">
        <!-- <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/judicature/1.png" />
          <div class="text">
            <span>立案信息</span>
            <span class="count">(0)</span>
          </div>
        </div>
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/judicature/2.png" />
          <div class="text">
            <span>裁判文书</span>
            <span class="count">(0)</span>
          </div>
        </div> -->
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/judicature/3.png" />
          <div class="text">
            <span>开庭公告</span>
            <span
              class="count"
              @click="
                onItemClick({
                  label: '开庭公告',
                  value: info.companyRiskJudicialVo?.courtnoticeCount,
                })
              "
              >({{ info.companyRiskJudicialVo?.courtnoticeCount || 0 }})</span
            >
          </div>
        </div>
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/judicature/4.png" />
          <div class="text">
            <span>限制高消费</span>
            <span
              class="count"
              @click="
                onItemClick({
                  label: '限制高消费',
                  value: info.companyRiskJudicialVo?.limitHighconsumeCount,
                })
              "
              >({{
                info.companyRiskJudicialVo?.limitHighconsumeCount || 0
              }})</span
            >
          </div>
        </div>
        <!-- <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/judicature/5.png" />
          <div class="text">
            <span>失信信息</span>
            <span class="count">(0)</span>
          </div>
        </div> -->
      </div>
    </div>
    <div class="info">
      <div class="title">行政</div>
      <div class="btn-box">
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/administration/1.png" />
          <div class="text">
            <span>经营异常</span>
            <span class="count"
              >({{
                info.companyRiskAdminVo?.isTagT7 == '0' ? '否' : '是'
              }})</span
            >
          </div>
        </div>
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/administration/2.png" />
          <div class="text">
            <span>行政处罚</span>
            <span class="count"
              >({{
                info.companyRiskAdminVo?.penaltyCount == '0' ? '否' : '是'
              }})</span
            >
          </div>
        </div>
        <!-- <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/administration/3.png" />
          <div class="text">
            <span>清算信息</span>
            <span class="count">(0)</span>
          </div>
        </div> -->
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/administration/4.png" />
          <div class="text">
            <span>欠税信息</span>
            <span
              class="count"
              @click="
                onItemClick({
                  label: '欠税信息',
                  value: info.companyRiskAdminVo?.taxOweCount,
                })
              "
              >({{ info.companyRiskAdminVo?.taxOweCount || 0 }})</span
            >
          </div>
        </div>
        <div class="item">
          <img
            alt=""
            src="../../../assets/images/recommend/administration/5.png" />
          <div class="text">
            <span>重大税收违法</span>
            <span
              class="count"
              @click="
                onItemClick({
                  label: '重大税收违法',
                  value: info.companyRiskAdminVo?.taxIllegalCount,
                })
              "
              >({{ info.companyRiskAdminVo?.taxIllegalCount || 0 }})</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  function onItemClick(item) {
    console.log(item)
    if (item.value && item.value != 0) {
      router.push({
        path: '/company-detail',
        query: {
          key: item.label,
          companyId: info.companyBasicInfoVo.companyId,
        },
      })
    }
  }
</script>

<style lang="scss" scoped>
  .risk-info {
    .info {
      margin-top: 16px;
      padding: 12px 8px;
      border-radius: 8px;
      background: rgb(18 85 228 / 6%);

      &:last-child {
        background: rgb(18 190 228 / 6%);
      }

      .title {
        color: rgb(0 0 0 / 90%);
        font-weight: 600;
        font-size: 16px;
      }

      .btn-box {
        display: flex;
        flex-wrap: wrap;

        .item {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 109px;
          height: 61px;
          margin-top: 20px;

          img {
            margin-bottom: 6px;
          }

          .text {
            color: rgb(0 0 0 / 90%);
            font-size: 12px;

            .count {
              color: #1255e4;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
</style>
