export const TAG_COLOR_MAP = {
  中央: {
    bg: 'rgba(25,137,250,0.1)',
    text: '#1989FA',
  },
  国家级: {
    bg: 'rgba(25,137,250,0.1)',
    text: '#1989FA',
  },
  省级: {
    bg: ' rgba(19,194,194,0.1)',
    text: '#13C2C2',
  },
  市级: {
    bg: 'rgba(250,173,20,0.1)',
    text: '#FAAD14',
  },
  区级: {
    bg: 'rgba(82,196,26,0.1)',
    text: '#52C41A',
  },
}
export const INDUSTRY_LAYOUT = [
  { text: '产业总体布局', value: '产业总体布局' },
  { text: '各区重点产业布局', value: '各区重点产业布局' },
]
/**
 * 产业map
 */
export const INDUSTRY_OPTIONS = [
  { text: '光芯屏端网', value: '光芯屏端网' },
  { text: '汽车制造和服务', value: '汽车制造和服务' },
  { text: '大健康和生物技术', value: '大健康和生物技术' },
  { text: '高端装备', value: '高端装备' },
  // { text: '北斗', value: '北斗' },
  { text: '软件和网络安全', value: '软件和网络安全' },
  { text: '超级计算和人工智能', value: '超级计算和人工智能' },
  // { text: '氢能', value: '氢能' },
  { text: '航空航天和空天信息', value: '航空航天和空天信息' },
  { text: '绿色环保', value: '绿色环保' },
  { text: '电磁能', value: '电磁能' },
  { text: '量子科技', value: '量子科技' },
  { text: '深地深海深空', value: '深地深海深空' },
  // { text: '脑科学和类脑科学', value: '脑科学和类脑科学' },
  { text: '文化旅游', value: '文化旅游' },
  { text: '现代金融', value: '现代金融' },
  { text: '商贸物流', value: '商贸物流' },
  { text: '数字创意', value: '数字创意' },
  { text: '智能建造', value: '智能建造' },
  { text: '人形机器人', value: '人形机器人' },
]
const MUST_MENU = ['产业规划图']
const Default_Menu = [
  '产业空间布局',
  '重点企业点位图',
  '重点企业热力图',
  '创新资源点位图',
  '应用场景示意图',
  '产业链链长制',
  '招商图谱',
]
// 产业介绍
export const INDUSTRY_INTRO_MAP = {
  光芯屏端网: MUST_MENU.concat(Default_Menu),
  汽车制造和服务: MUST_MENU.concat(Default_Menu),
  大健康和生物技术: MUST_MENU.concat(Default_Menu),
  高端装备: MUST_MENU.concat(Default_Menu),
  软件和网络安全: MUST_MENU.concat(Default_Menu),
  超级计算和人工智能: MUST_MENU.concat(Default_Menu),
  航空航天和空天信息: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '重点企业热力图',
    '创新资源点位图',
    '应用场景示意图',
    '产业链链长制',
  ]),
  绿色环保: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '重点企业热力图',
    '创新资源点位图',
    '应用场景示意图',
    '产业链链长制',
  ]),
  电磁能: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '创新资源点位图',
    '产业链链长制',
  ]),
  量子科技: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '创新资源点位图',
    '产业链链长制',
    '招商图谱',
  ]),
  深地深海深空: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '创新资源点位图',
    '产业链链长制',
  ]),
  文化旅游: MUST_MENU.concat(Default_Menu),
  现代金融: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '重点企业热力图',
    '创新资源点位图',
    '应用场景示意图',
    '产业链链长制',
  ]),
  商贸物流: MUST_MENU.concat([
    '商贸规划图',
    '物流规划图',
    '产业空间布局',
    '重点企业点位图',
    '重点企业热力图',
    '创新资源点位图',
    '应用场景示意图',
    '产业链链长制',
    '招商图谱',
  ]),
  数字创意: MUST_MENU.concat([
    '产业空间布局',
    '重点企业点位图',
    '重点企业热力图',
    '创新资源点位图',
    '应用场景示意图',
    '产业链链长制',
  ]),
  智能建造: MUST_MENU.concat(Default_Menu),
  人形机器人: [],
}
/**
 * 类型筛选map
 */
export const TYPE_OPTIONS = [
  {
    text: 'scene',
    value: '应用场景',
  },
  {
    text: 'park',
    value: '园区',
  },
  {
    text: 'building',
    value: '楼宇',
  },
]
