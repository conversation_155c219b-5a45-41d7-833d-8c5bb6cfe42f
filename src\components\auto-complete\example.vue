<template>
  <div class="demo-container">
    <h2>AutoComplete 自动补全组件示例</h2>

    <!-- 基础用法 -->
    <div class="demo-section">
      <h3>基础用法</h3>
      <AutoComplete
        v-model="basicValue"
        :fetch-suggestions="fetchBasicSuggestions"
        placeholder="请输入内容"
        label="用户名"
        @select="handleBasicSelect" />
      <p>当前值: {{ basicValue }}</p>
    </div>

    <!-- 自定义模板 -->
    <div class="demo-section">
      <h3>自定义模板</h3>
      <AutoComplete
        v-model="customValue"
        :fetch-suggestions="fetchUserSuggestions"
        placeholder="搜索用户"
        @select="handleCustomSelect">
        <template #default="{ item }">
          <div class="user-item">
            <div class="user-avatar">{{ item.name.charAt(0) }}</div>
            <div class="user-info">
              <div class="user-name">{{ item.name }}</div>
              <div class="user-email">{{ item.email }}</div>
            </div>
          </div>
        </template>
      </AutoComplete>
      <p>选中用户: {{ selectedUser?.name }}</p>
    </div>

    <!-- 远程搜索 -->
    <div class="demo-section">
      <h3>远程搜索</h3>
      <AutoComplete
        v-model="remoteValue"
        :fetch-suggestions="fetchRemoteSuggestions"
        placeholder="搜索GitHub仓库"
        :debounce="500"
        :min-length="2"
        value-key="name"
        @select="handleRemoteSelect">
        <template #default="{ item }">
          <div class="repo-item">
            <div class="repo-name">{{ item.full_name }}</div>
            <div class="repo-desc">{{ item.description }}</div>
          </div>
        </template>
        <template #loading>
          <div class="custom-loading">
            <VanLoading size="16" />
            <span>正在搜索...</span>
          </div>
        </template>
      </AutoComplete>
    </div>

    <!-- 带图标和清除功能 -->
    <div class="demo-section">
      <h3>带图标和清除</h3>
      <AutoComplete
        v-model="iconValue"
        :fetch-suggestions="fetchBasicSuggestions"
        placeholder="请输入内容"
        left-icon="search"
        clearable
        @clear="handleClear" />
    </div>

    <!-- 禁用状态 -->
    <div class="demo-section">
      <h3>禁用状态</h3>
      <AutoComplete
        v-model="disabledValue"
        :fetch-suggestions="fetchBasicSuggestions"
        placeholder="禁用状态"
        disabled />
    </div>
  </div>
</template>

<script setup>
  import AutoComplete from '@/components/auto-complete/index.vue' // 假设组件文件名为 AutoComplete.vue
  import { Loading as VanLoading } from 'vant'
  import { ref } from 'vue'

  // 基础示例数据
  const basicValue = ref('')
  const customValue = ref('')
  const remoteValue = ref('')
  const iconValue = ref('')
  const disabledValue = ref('禁用状态')
  const selectedUser = ref(null)

  // 基础建议数据
  const basicSuggestions = [
    'Vue.js',
    'React',
    'Angular',
    'Svelte',
    'jQuery',
    'Bootstrap',
    'Tailwind CSS',
    'Element Plus',
    'Vant UI',
    'Ant Design',
  ]

  // 用户数据
  const users = [
    { id: 1, name: '张三', email: '<EMAIL>' },
    { id: 2, name: '李四', email: '<EMAIL>' },
    { id: 3, name: '王五', email: '<EMAIL>' },
    { id: 4, name: '赵六', email: '<EMAIL>' },
    { id: 5, name: '钱七', email: '<EMAIL>' },
  ]

  // 基础建议获取
  const fetchBasicSuggestions = async (query) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const filtered = basicSuggestions.filter((item) =>
          item.toLowerCase().includes(query.toLowerCase()),
        )
        resolve(filtered.map((item) => ({ value: item })))
      }, 100)
    })
  }

  // 用户建议获取
  const fetchUserSuggestions = async (query) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        const filtered = users.filter(
          (user) => user.name.includes(query) || user.email.includes(query),
        )
        resolve(filtered)
      }, 200)
    })
  }

  // 远程搜索（模拟GitHub API）
  const fetchRemoteSuggestions = async (query) => {
    if (!query) return []

    try {
      // 模拟API调用
      const response = await fetch(
        `https://api.github.com/search/repositories?q=${query}&per_page=10`,
      )
      const data = await response.json()
      return data.items || []
    } catch (error) {
      console.error('搜索失败:', error)
      return []
    }
  }

  // 事件处理
  const handleBasicSelect = (item) => {
    console.log('基础选择:', item)
  }

  const handleCustomSelect = (item) => {
    selectedUser.value = item
    console.log('用户选择:', item)
  }

  const handleRemoteSelect = (item) => {
    console.log('远程选择:', item)
  }

  const handleClear = () => {
    console.log('清除内容')
  }
</script>

<style scoped>
  .demo-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
  }

  .demo-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
  }

  .demo-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #303133;
  }

  .demo-section p {
    margin-top: 10px;
    color: #606266;
    font-size: 14px;
  }

  /* 用户项样式 */
  .user-item {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: 12px;
    border-radius: 50%;
    background: #409eff;
    color: white;
    font-weight: bold;
  }

  .user-info {
    flex: 1;
  }

  .user-name {
    margin-bottom: 2px;
    color: #303133;
    font-weight: 500;
  }

  .user-email {
    color: #909399;
    font-size: 12px;
  }

  /* 仓库项样式 */
  .repo-item {
    width: 100%;
  }

  .repo-name {
    margin-bottom: 4px;
    color: #303133;
    font-weight: 500;
  }

  .repo-desc {
    overflow: hidden;
    color: #909399;
    font-size: 12px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 自定义加载样式 */
  .custom-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #409eff;
  }
</style>
