<template>
  <Popup
    v-model:show="show"
    destroy-on-close
    style="height: 70%"
    position="bottom"
    @closed="values = selected || []">
    <div class="flex h-full flex-col overflow-hidden">
      <div class="flex items-center justify-between px-[16px] pt-[8px]">
        <div @click="emits('cancel')">取消</div>
        <div @click="emits('confirm', values)">确认</div>
      </div>
      <div class="checkbox_list">
        <CheckboxGroup
          v-model="values"
          shape="square"
          :max="max">
          <CheckBoxList
            :options
            :toggle-list="propToggleList"
            :label-name="labelName"
            :value-name="valueName"
            @toggle="onToggle" />
        </CheckboxGroup>
      </div>
    </div>
  </Popup>
</template>

<script setup>
  import { Popup, CheckboxGroup } from 'vant'
  import { ref, computed, watch } from 'vue'
  import CheckBoxList from './CheckBoxList.vue'

  const props = defineProps({
    options: {
      type: Array,
      default: () => [],
    },
    max: {
      type: Number || String,
      default: 0,
    },
    labelName: {
      type: String,
      default: 'label',
    },
    valueName: {
      type: String,
      default: 'value',
    },
    selected: {
      type: Array,
      default: () => [],
    },
  })

  const show = defineModel({ type: Boolean })
  const emits = defineEmits(['confirm', 'cancel'])

  const values = ref([])

  const toggleList = ref(new Set())

  watch(
    props.selected,
    (val) => {
      console.log('code', props.selected)

      values.value = typeof val === 'string' ? [] : val
    },
    {
      immediate: true,
    },
  )

  function onToggle(item) {
    if (toggleList.value.has(item)) {
      toggleList.value.delete(item)
    } else {
      toggleList.value.add(item)
    }
  }

  const propToggleList = computed(() => {
    return Array.from(toggleList.value)
  })

  defineExpose({
    reset() {
      values.value = []
    },
  })
</script>

<style lang="scss">
  .checkbox_list {
    flex: 1;
    overflow-y: auto;
    padding: 16px 16px 8px;

    .checkout_box_item + .checkout_box_item {
      margin-top: 10px;
    }
  }
</style>
