<template>
  <div class="finance_card flex flex-wrap gap-[12px]">
    <div class="item">
      <div class="label">投资金额：</div>
      <div class="value">--</div>
    </div>
    <div class="item">
      <div class="label">时间：</div>
      <div class="value">2025-01-01</div>
    </div>
    <div class="item">
      <div class="label">投资方：</div>
      <TextEllipsis
        content="上海武岳峰投资股份有限公司"
        class="value" />
    </div>
    <div class="item">
      <div class="label">融资轮次：</div>
      <div class="value">A轮</div>
    </div>
  </div>
</template>

<script setup>
  import { TextEllipsis } from 'vant'
</script>

<style lang="scss" scoped>
  .finance_card {
    padding: 6px;
    border-radius: 8px;
    background: rgb(18 85 228 / 4%);

    .item {
      display: flex;
      overflow: hidden;
      width: calc(50% - 12px);

      .label {
        flex-shrink: 0;
        color: rgb(0 0 0 / 60%);
        font-size: 12px;
      }

      .value {
        flex-shrink: 0;
        color: rgb(0 0 0 / 90%);
        font-size: 12px;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    & + .finance_card {
      margin-top: 8px;
    }
  }
</style>
