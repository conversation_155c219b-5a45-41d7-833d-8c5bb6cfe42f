const toString = Object.prototype.toString

export function is(val, type) {
  return toString.call(val) === `[object ${type}]`
}

export function isDef(val) {
  return val !== void 0
}

export function isUndef(val) {
  return val === void 0
}

export function isNull(val) {
  return val === null
}

export function isWhitespace(val) {
  return val === ''
}

export function isObject(val) {
  return !isNull(val) && is(val, 'Object')
}

export function isArray(val) {
  return val && Array.isArray(val)
}

export function isString(val) {
  return is(val, 'String')
}

export function isNumber(val) {
  return is(val, 'Number')
}

export function isBoolean(val) {
  return is(val, 'Boolean')
}

export function isDate(val) {
  return is(val, 'Date')
}

export function isRegExp(val) {
  return is(val, 'RegExp')
}

export function isFunction(val) {
  return typeof val === 'function'
}

export function isPromise(val) {
  return (
    is(val, 'Promise') &&
    isObject(val) &&
    isFunction(val.then) &&
    isFunction(val.catch)
  )
}

export function isElement(val) {
  return isObject(val) && !!val.tagName
}

export function isWindow(val) {
  return typeof window !== 'undefined' && isDef(window) && is(val, 'Window')
}

export function isNullOrUndef(val) {
  return isNull(val) || isUndef(val)
}

export function isNullOrWhitespace(val) {
  return isNullOrUndef(val) || isWhitespace(val)
}

export function isEmpty(val) {
  if (isNullOrUndef(val)) {
    return true
  }

  if (isArray(val) || isString(val)) {
    return val.length === 0
  }

  if (val instanceof Map || val instanceof Set) {
    return val.size === 0
  }

  if (isObject(val)) {
    return Object.keys(val).length === 0
  }

  return false
}

/**
 * 类似mysql的IFNULL函数
 * 第一个参数为null/undefined/'' 则返回第二个参数作为备用值，否则返回第一个参数
 * @param {number | boolean | string} val
 * @param {number | boolean | string} def
 * @returns
 */
export function ifNull(val, def = '') {
  return isNullOrWhitespace(val) ? def : val
}

export function isUrl(path) {
  const reg =
    /(?:^https?:(?:\/\/)?(?:[-;:&=+$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=+$,\w]+@)[A-Za-z0-9.-]+)(?:(?:\/[+~%/.\w-]*)?\??[-+=&;%@.\w]*(?:#\w*)?)?$/
  return reg.test(path)
}

/**
 * @param {string} path
 * @returns {boolean}
 */
export function isExternal(path) {
  return /^(?:https?:|mailto:|tel:)/.test(path)
}

export const isServer = typeof window === 'undefined'

export const isClient = !isServer

/**
 * 判断给定的URL是否是图片
 * @param {string} url - 要检查的URL
 * @returns {boolean} 如果是图片返回true，否则返回false
 */
export function isImageUrl(url) {
  // 常见的图片后缀名
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']

  try {
    // 创建一个URL对象（如果输入是相对路径，会抛出错误）
    const urlObj = new URL(url)
    // 获取路径名
    url = urlObj.pathname
  } catch {
    // 如果URL解析失败（可能是相对路径），继续处理原始输入
  }

  // 获取文件后缀名（不区分大小写）
  const extension = url.split('.').pop().toLowerCase()

  // 检查后缀名是否在图片扩展名列表中
  return imageExtensions.includes(extension)
}
