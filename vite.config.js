import { fileURLToPath, URL } from 'node:url'

import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import { codeInspectorPlugin } from 'code-inspector-plugin'
import { defineConfig, loadEnv } from 'vite'
import { ViteImageOptimizer } from 'vite-plugin-image-optimizer'
import VueDevTools from 'vite-plugin-vue-devtools'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // eslint-disable-next-line no-undef
  const env = loadEnv(mode, process.cwd(), '')

  // 插件列表
  const plugins = [vue(), tailwindcss()]

  if (mode === 'development') {
    plugins.push(VueDevTools())
  }

  // 生产
  if (command === 'build') {
    // 图片压缩插件
    plugins.push(ViteImageOptimizer())
    // 开发
  } else if (command === 'serve') {
    plugins.push(
      // https://inspector.fe-dev.cn/guide/start.html#%E6%96%B9%E5%BC%8F%E4%B8%80-%E6%8E%A8%E8%8D%90
      codeInspectorPlugin({
        bundler: 'vite',
      }),
    )
  }

  return {
    plugins: plugins,
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    base: env.VITE_API_BASE_PREFIX,
    // esbuild: {
    //   // or rollup/plugin-strip ?
    //   pure: ['console.log'],
    //   minify: true,
    // },
    server: {
      // port: 5175,
      host: '0.0.0.0',
      open: true,
    },
  }
})
