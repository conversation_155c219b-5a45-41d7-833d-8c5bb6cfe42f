import { defineStore } from 'pinia'
import { ref } from 'vue'

function initForm() {
  return {
    // 项目类型
    type: '',
    // 单位名称
    unitName: '',
    // 单位简介
    remarks: '',
    // 联系人列表
    contactDTOList: [],
  }
}

/**
 * 招商资源库-机构新增或编辑表单
 */
export const useOrgFormStore = defineStore('zszykOrgForm', () => {
  const formData = ref(initForm())
  // 添加表单和更新表单后的动作类型，added或edited
  const formActionType = ref('')

  // 项目类型
  const typeOptions = ref([])

  function $reset() {
    formData.value = initForm()
    formActionType.value = ''
  }

  return {
    formData,
    typeOptions,
    formActionType,
    $reset,
  }
})
