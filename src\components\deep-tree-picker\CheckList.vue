<template>
  <div
    v-for="item in options"
    :key="item[labelName]"
    class="checkout_box_item">
    <div class="checkbox">
      <!-- <Checkbox
        v-if="deep > 0"
        :name="item[valueName] || item[labelName]"
        :style="`margin-left:${14 * deep}px`"
        @click="emitCheck(item)">
        {{ item[labelName] || item[valueName] }}
      </Checkbox> -->
      <Checkbox
        :name="item[valueName] || item[labelName]"
        :style="`margin-left:${14 * deep}px`"
        @click="emitCheck(item)">
        {{ item[labelName] || item[valueName] }}
      </Checkbox>
      <!-- <div
        :style="`margin-left:${14 * deep}px`"
        @click="emits('toggle', item[valueName] || item[labelName])">
        {{ item[labelName] || item[valueName] }}
      </div> -->
      <div
        v-if="item.children && deep < 3"
        class="ml-[8px] flex h-[16px] w-[16px] items-center justify-center p-[4px]"
        @click="emits('toggle', item[valueName] || item[labelName])">
        <i
          class="iconfont icon-RightCircle mb-[-3px] text-[12px]"
          :style="{
            transition: '0.1s all linear',
            transform: `rotate(${toggleList.includes(item[valueName] || item[labelName]) ? '90' : '0'}deg)`,
          }"></i>
      </div>
    </div>
    <div
      v-if="toggleList.includes(item[valueName] || item[labelName]) && deep < 3"
      class="mt-[10px]">
      <CheckList
        :options="item.children"
        :toggle-list="toggleList"
        :deep="deep + 1"
        :label-name="labelName"
        :value-name="valueName"
        @toggle="emitToggle"
        @check="emitCheck" />
    </div>
  </div>
</template>

<script setup>
  import { Checkbox } from 'vant'

  const { deep } = defineProps({
    options: {
      type: Array,
      default: () => [],
    },
    deep: {
      type: Number,
      default: 0,
    },
    toggleList: {
      type: Array,
      default: () => [],
    },
    labelName: {
      type: String,
      default: 'label',
    },
    valueName: {
      type: String,
      default: 'value',
    },
  })

  const emits = defineEmits(['toggle', 'check'])

  function emitToggle(item) {
    emits('toggle', item)
  }

  function emitCheck(item) {
    emits('check', item, deep)
  }
</script>

<style lang="scss" scoped>
  .checkbox {
    display: flex;
    align-items: center;
  }
</style>
