<template>
  <div class="flex h-full flex-col overflow-hidden">
    <Search
      v-model="keyword"
      style="width: 100%"
      shape="round"
      placeholder="输入搜索关键词"
      @keydown.enter="onEnter"
      @clear="onEnter" />

    <div class="filter_group">
      <div
        class="flex h-[48px] items-center justify-center overflow-hidden bg-white"
        @click="showTypeSelect = true">
        <span class="text">{{ type }}</span>
        <i
          class="iconfont icon-caret-down-small text-[10px] text-[rgba(220,222,224,0.9)]"></i>
      </div>
      <ActionSheet
        v-model:show="showTypeSelect"
        :actions="[
          {
            name: '综合篇',
            value: 'default',
          },
          {
            name: '产业篇',
            value: 'industrychain',
          },
        ]"
        cancel-text="取消"
        close-on-click-action
        @select="typeChange"></ActionSheet>
      <!-- <div
        class="flex h-[48px] items-center justify-center overflow-hidden bg-white"
        @click="showLevelSelect = true">
        <span class="text">{{ levelType }}</span>
        <i
          class="iconfont icon-caret-down-small text-[10px] text-[rgba(220,222,224,0.9)]"></i>
      </div>
       <ActionSheet
        v-model:show="showLevelSelect"
        :actions="levelOptions.map((item) => ({ name: item }))"
        cancel-text="取消"
        close-on-click-action
        @select="onLevelChange"></ActionSheet> -->
      <div
        v-show="type === '产业篇'"
        class="flex h-[48px] items-center justify-center overflow-hidden bg-white"
        @click="showMenu = true">
        <span class="text">{{ selectData }}</span>
        <i
          class="iconfont icon-caret-down-small text-[10px] text-[rgba(220,222,224,0.9)]"></i>
      </div>
      <ActionSheet
        v-model:show="showMenu"
        :actions="columns"
        cancel-text="取消"
        close-on-click-action
        @select="onSelectType"></ActionSheet>
    </div>

    <!-- 列表 -->
    <div
      ref="listRef"
      class="list">
      <template
        v-for="(item, index) in policyList"
        :key="index">
        <div
          v-if="item.number"
          class="policy_card"
          @click="toDetail(item.id)">
          <div class="policy_id">
            <div class="policy_id_text"> {{ item.number }} </div>
          </div>
          <div class="policy_top">
            <div
              class="policy_tag"
              :style="{
                background: TAG_COLOR_MAP[item.level]?.bg,
                color: TAG_COLOR_MAP[item.level]?.text,
              }">
              {{ item.level }}
            </div>
          </div>
          <div class="policy_title">
            {{ item.name }}
          </div>
          <div class="policy_content">
            <TextEllipsis
              rows="4"
              :content="
                (item.content || item.synopsis).replaceAll('\\n', '')
              " />
          </div>
          <div class="policy_bottom">
            <div
              v-show="item?.industrychain"
              class="policy_tags">
              <div
                v-for="(i, idx) in item?.industrychain?.split(',')"
                :key="idx"
                class="policy_tag">
                {{ i }}
              </div>
            </div>
            <div class="policy_time_box">
              <div
                v-show="item.start_time"
                class="policy_time">
                <i class="iconfont icon-Calendar tw-text-[12px]"></i>
                <span>
                  {{ item.start_time?.split(' ')?.[0] }} ~
                  {{ getEndTime(item.lifespan?.split(' ')?.[0]) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </template>
      <Loading
        v-show="loading"
        class="policy_card loading">
        加载中...
      </Loading>

      <div
        v-show="paginate[total] === 0 && !loading"
        style="text-align: center"
        class="policy_card">
        暂无数据
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getPageList } from '@/apis'
  import cardTop from '@/assets/images/cygk/policy-card-top.png'
  import { INDUSTRY_OPTIONS, TAG_COLOR_MAP } from '@/views/cygk/options'
  import { ActionSheet, Loading, Search, TextEllipsis } from 'vant'
  import { computed, onActivated, onBeforeUnmount, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const { area } = defineProps({
    area: {
      type: String,
      default: '',
    },
  })

  const listRef = ref(null)

  const keyword = ref('')
  const selectData = ref('全产业链')
  const levelType = ref('全级别')
  // const levelOptions = ['全级别', '国家级', '省级', '市级', '区级']
  const total = Symbol()
  const paginate = ref({
    pageNum: 1,
    pageSize: 10,
    [total]: 0,
  })
  // 政策分类
  const type = ref('综合篇')
  const typeMap = {
    产业篇: 'industrychain',
    综合篇: 'default',
  }
  const columns = [
    {
      name: '全产业链',
    },
    ...INDUSTRY_OPTIONS.map((m) => ({ name: m.value })),
  ]

  const policyList = ref([])
  const loading = ref(false)

  const showMenu = ref(false)
  const showTypeSelect = ref(false)
  // const showLevelSelect = ref(false)

  const cardTopBg = computed(() => {
    return `url(${cardTop})`
  })

  // const filterLayout = computed(() => {
  //   return type.value === '综合篇' ? '1fr 1fr' : '1fr 1fr 1fr'
  // })

  async function getPageData() {
    loading.value = true
    try {
      const param = {
        page: paginate.value,
        params: {
          industrychain:
            selectData.value === '全产业链' ? undefined : selectData.value,
          level: levelType.value === '全级别' ? undefined : levelType.value,
          keyWord: keyword.value,
          policyType: typeMap[type.value],
          ssxzq: area,
        },
      }
      const {
        data: { data: res },
      } = await getPageList('policy', param)
      policyList.value.push(...res.list)
      paginate.value[total] = res?.total || 0
      paginate.value.pageNum++
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  function onEnter(e) {
    keyword.value = e.target.val
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }

  function toDetail(id) {
    router.push(`/enterprise-side/policy-detail?id=${id}`)
  }

  const scrollY = ref(0)

  /**
   * @param {Event} e
   */
  function onDownLoad(e) {
    scrollY.value = e.target.scrollTop

    if (
      loading.value ||
      (policyList.value.length >= paginate.value[total] &&
        paginate.value[total] !== 0)
    ) {
      return
    }

    let scrollTop = e.target.scrollTop
    let scrollHeight = e.target.scrollHeight
    let offsetHeight = Math.ceil(e.target.getBoundingClientRect().height)
    let currentHeight = scrollTop + offsetHeight
    if (currentHeight >= scrollHeight) {
      getPageData()
    }
  }
  // function onLevelChange(selectedValues) {
  //   if (selectedValues.name === levelType.value) {
  //     return
  //   }
  //   levelType.value = selectedValues.name
  //   paginate.value.pageNum = 1
  //   paginate.value[total] = 0
  //   policyList.value = []
  //   getPageData()
  // }

  const typeChange = (selectedValues) => {
    type.value = selectedValues.name
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    selectData.value = '全产业链'
    getPageData()
  }

  const onSelectType = (selectedValues) => {
    selectData.value = selectedValues.name
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }

  function getEndTime(time) {
    return time || '长期'
  }

  onActivated(() => {
    listRef.value?.scrollTo({
      top: scrollY.value,
    })
  })

  onMounted(() => {
    if (policyList.value.length === 0) {
      getPageData()
    }
    listRef.value?.addEventListener('scroll', onDownLoad)
  })

  onBeforeUnmount(() => {
    listRef.value?.removeEventListener('scroll', onDownLoad)
  })
</script>

<style lang="scss" scoped>
  .filter_group {
    /* grid-template-columns: v-bind('filterLayout'); */
    display: flex;
    gap: 100px;
    justify-content: center;
    width: 100%;
    background: #fff;

    .text {
      margin-right: 4px;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .list {
    flex: 1;
    overflow-y: auto;
    padding: 8px;

    .policy_card {
      position: relative;
      margin-top: 16px;
      padding: 16px;
      border-radius: 12px;
      background: #fff;
      box-shadow: 0 0 20px 0 rgb(0 0 0 / 5%);

      .policy_tag {
        padding: 1px 4px;
        font-size: 12px;
        line-height: 16px;
      }

      .policy_id {
        position: absolute;
        top: -6px;
        left: 0;
        min-width: 182px;
        height: 38px;
        padding-right: 16px;
        background-image: v-bind(cardTopBg);
        background-size: 100% 100%;
        background-repeat: no-repeat;
        color: #f7f8fa;
        font-size: 12px;
        line-height: 28px;
        white-space: nowrap;

        // .policy_bg {
        //   position: absolute;
        // }

        .policy_id_text {
          // position: absolute;
          margin-left: 38px;
        }
      }

      .policy_top {
        display: flex;
        justify-content: flex-end;
      }

      .policy_title {
        margin-top: 6px;
        color: rgb(0 0 0 / 90%);
        font-size: 16px;
        line-height: 24px;
      }

      .policy_content {
        margin-top: 14px;
        color: rgb(0 0 0 / 40%);
        font-size: 12px;
        line-height: 20px;
      }

      .policy_bottom {
        // display: flex;
        // justify-content: space-between;
        // align-items: center;
        margin-top: 10px;

        .policy_tags {
          display: flex;
          flex-wrap: wrap;
          gap: 4px;
          margin-bottom: 10px;

          .policy_tag {
            background: rgb(25 137 250 / 10%);
            color: #1989fa;
          }
        }

        .policy_time_box {
          flex-shrink: 0;
        }

        .policy_time {
          display: flex;
          align-items: center;
          color: rgb(0 0 0 / 40%);
          font-size: 12px;
          line-height: 20px;

          .iconfont {
            margin-right: 4px;
          }
        }
      }

      &.loading {
        text-align: center;
      }
    }
  }
</style>
