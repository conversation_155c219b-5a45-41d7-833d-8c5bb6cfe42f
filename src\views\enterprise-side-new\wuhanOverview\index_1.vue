<template>
  <div class="wuhan-container">
    <div class="header-image">
      <img
        src="@/assets/images/enterprise-side/banner1.png"
        alt="Wuhan Skyline" />
      <div class="red-ribbon"></div>
    </div>
    <div class="content">
      <h2 class="title">武汉概况</h2>
      <div class="description">
        <div class="chinese-text">
          武汉是湖北省省会，中部地区的中心城市，国家历史文化名城，国际性综合交通枢纽城市，面积8569平方公里，实有人口超1500万人，拥有3500多年的悠久历史。2024年全市经济总量达到2.11万亿元，位列全国城市第9；经营主体总数超236万户；固定资产投资、社会消费品零售总额、进出口总额等主要经济指标增速均居副省级城市前列；企业总量突破100万户，跃居副省级城市第4；高新技术企业总量突破1.6万家。
        </div>
        <div class="english-text">
          <!-- English translation -->
          Wuhan, the capital of Hubei Province located in the heart of central
          China, covers an area of 8,569km² and is home to over 15 million
          residents. With a history of over 3,500 years, it has earned the title
          of a national famous historical and cultural city; leveraging its
          geographical and transport advantages, it is renowned as an
          international comprehensive transport hub. In 2024, the city's
          economic aggregate reached RMB 2.11 trillion, ranking 9th in China.
          The total number of its business entities surpassed 2.36 million. The
          city ranked among the highest in sub-provincial cities in terms of the
          growth rate of key economic indicators such as fixed asset
          investments, total retail sales of consumer goods and the total
          foreign trade value. Besides, it recorded over one million
          enterprises, ranking 4th among sub-provincial competitors; among them,
          over 16,000 were in the high-tech sector.
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .wuhan-container {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    padding-bottom: 16px;
    background-image: url('@/assets/images/enterprise-side/whBackgrordImg.png');
    background-position: center;
    background-repeat: repeat;
  }

  .header-image {
    position: relative;
    overflow: hidden;
    width: 100%;
  }

  .header-image img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .red-ribbon {
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 60px;

    /* background: linear-gradient(45deg, #ff0000, #ff3333); */
    clip-path: path('M0,30 Q200,0 400,30 Q600,60 800,30 L800,60 L0,60 Z');
  }

  .content {
    margin-top: -40px;
    padding: 0 20px;
  }

  .title {
    margin-bottom: 8px;
    color: rgb(159 96 0 / 100%);
    font-weight: 600;
    font-size: 20px;
    line-height: 28px;
  }

  .description {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .chinese-text {
    color: rgb(159 96 0 / 100%);
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-indent: 2em;
  }

  .english-text {
    color: rgb(0 0 0 / 60%);
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: left;
  }
</style>
