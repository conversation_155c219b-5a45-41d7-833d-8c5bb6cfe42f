<template>
  <div class="container">
    <div class="header">
      <div class="flex items-center">
        <i
          v-if="info.gender === '男'"
          class="iconfont icon-nan text-[#4096FF]" />
        <i
          v-else
          class="iconfont icon-nv text-[#F759AB]" />
        <span class="ml-[6px] font-[600]">{{ info.name }}</span>
      </div>
      <div
        v-if="info.job"
        class="mt-[8px]">
        <div class="flex">
          <i
            class="iconfont icon-zw-1 text-[12px]! text-[rgba(0,0,0,0.26)]"></i>
          <span class="ml-[4px] text-[12px] text-[rgba(0,0,0,0.6)]">
            {{ info.job }}
          </span>
        </div>
      </div>
    </div>
    <!-- <div class="card">
      <div class="title">
        <span class="font-[600]">基本信息</span>
      </div>
      <div class="info">
        <div class="item">
          <div class="label">毕业院校：</div>
          <div class="value">{{ info.almaMater }}</div>
        </div>
        <div class="item">
          <div class="label">专业：</div>
          <div class="value">{{ info.major }}</div>
        </div>
        <div class="item">
          <div class="label">专业领域：</div>
          <div class="value">{{ info.professionalField }}</div>
        </div>
      </div>
    </div> -->
    <div class="card">
      <div class="title">
        <span class="font-[600]">所在单位</span>
      </div>
      <div class="info">
        <div class="item">
          <!-- <div class="label">所在单位：</div> -->
          <div class="value">{{ info.department }}</div>
        </div>
        <!-- <div class="item">
          <div class="label">单位类别：</div>
          <div class="value">{{ info.deptType }}</div>
        </div>
        <div class="item">
          <div class="label">所属产业链：</div>
          <div class="value">{{ info.industryChain }}</div>
        </div>
        <div class="item">
          <div class="label">办公电话：</div>
          <div class="value">{{ info.officePhoneNumber }}</div>
        </div> -->
      </div>
    </div>
    <div class="card">
      <div class="title">
        <span class="font-[600]">专家简介</span>
      </div>
      <div class="content">{{ info.achievement }}</div>
    </div>
  </div>
</template>

<script setup>
  import { expertDetail } from '@/apis/precise'
  import { onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const info = ref({})

  async function initDetail() {
    try {
      const {
        data: { data: res },
      } = await expertDetail(route.query.id)

      info.value = res
    } catch (error) {
      console.log(error)
    }
  }

  onMounted(() => {
    initDetail()
  })
</script>

<style lang="scss" scoped>
  .container {
    width: 100%;
    min-height: 100%;
    padding-bottom: 16px;
    background: #f7f8fa;

    .header {
      padding: 16px;
      background: #fff;
    }

    .card {
      margin-top: 16px;
      padding: 16px;
      background: #fff;

      .info {
        margin-top: 12px;

        .item {
          display: flex;

          & + .item {
            margin-top: 6px;
          }

          .label {
            flex-shrink: 0;
            color: rgb(0 0 0 / 60%);
            font-size: 14px;
          }

          .value {
            color: rgb(0 0 0 / 90%);
            font-size: 14px;
          }
        }
      }

      .content {
        margin-top: 12px;
        color: rgb(0 0 0 / 90%);
        font-size: 14px;
        line-height: 22px;
        white-space: pre-wrap;
      }
    }

    .title {
      position: relative;
      padding-left: 8px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        transform: translateY(-50%);
        border-radius: 16px;
        background: #1255e4;
        content: '';
      }
    }
  }
</style>
