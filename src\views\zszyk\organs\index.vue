<template>
  <div class="page">
    <header>
      <Row>
        <Col :span="20">
          <Search
            v-model="keyword"
            shape="round"
            placeholder="请输入搜索关键词"
            @search="onSearch"
            @clear="onSearch" />
        </Col>
        <Col :span="4">
          <div class="flex h-full items-center">
            <button @click="handleAdd">新增</button>
          </div>
        </Col>
      </Row>
    </header>
    <div class="list">
      <div
        v-if="loading"
        class="flex h-full w-full items-center justify-center pt-[20px]">
        <Loading
          vertical
          :size="28">
          加载中...
        </Loading>
      </div>

      <template v-else-if="data.length">
        <div
          v-for="item in data"
          :key="item.bid"
          class="list_item"
          @click="handleDetail(item.bid)">
          <Row class="flex-[1]">
            <Col :span="19">
              <h1>{{ item.unitName }}</h1>
            </Col>
            <Col :span="5">
              <div class="actions">
                <button
                  class="active-button"
                  @click.stop="handleEdit(item.bid)">
                  <i
                    class="iconfont icon-EditOutlined1"
                    style="color: #1255e4"></i>
                </button>
                <button
                  class="active-button"
                  @click.stop="handleDelete(item.bid)">
                  <i
                    class="iconfont icon-DeleteOutlined"
                    style="color: #ff4d4f"></i>
                </button>
              </div>
            </Col>
          </Row>
        </div>
      </template>

      <Empty
        v-else-if="data.length === 0"
        :image="keyword ? 'search' : undefined"
        :description="keyword ? '未找到相关机构' : '暂无数据'" />
    </div>
  </div>
</template>

<script setup>
  import { deleteOrg, getOrganList } from '@/apis/zszyk'
  import { useOrgFormStore } from '@/stores/zszyk'
  import { updateState, useRequest } from 'alova/client'
  import { Col, Empty, Loading, Row, Search, showConfirmDialog } from 'vant'
  import { onActivated, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const route = useRoute()
  const keyword = ref('')
  // 表格数据
  const { loading, data, send } = useRequest(
    () =>
      getOrganList({
        keyword: keyword.value || '',
        type: route.params.type,
      }),
    {
      initialData: [],
    },
  )

  // 点击搜索
  const onSearch = () => {
    send()
  }

  const router = useRouter()
  // 查看详情
  const handleDetail = (id) => {
    router.push(`/zszyk/org-detail/${route.params.type}/${id}`)
  }

  // 新增
  const handleAdd = () => {
    router.push(`/zszyk/org-form/${route.params.type}`)
  }

  // 编辑
  const handleEdit = (id) => {
    router.push(`/zszyk/org-form/${route.params.type}/${id}`)
  }

  const { send: deleteOrgSend } = useRequest(
    (bid) =>
      deleteOrg({
        bid,
      }),
    {
      immediate: false,
    },
  )
  // 删除
  const handleDelete = (id) => {
    showConfirmDialog({
      title: '确认删除',
      message: '确认删除该条内容？',
      beforeClose: async (action) => {
        if (action === 'confirm') {
          try {
            await deleteOrgSend(id)
            // 手动更新数据
            updateState(
              getOrganList({
                keyword: keyword.value || '',
                type: route.params.type,
              }),
              (oldListData) => {
                const index = oldListData.findIndex(({ bid }) => bid === id)
                console.log('index', index)
                oldListData.splice(index, 1)
                return oldListData
              },
            )
          } catch (error) {
            console.log(error)
          }
        }
        return Promise.resolve(true)
      },
    })
  }

  const store = useOrgFormStore()
  onActivated(() => {
    console.log('active', store.formActionType)

    // 有过更新操作，重新加载数据列表。
    if (store.formActionType) {
      send()
      store.formActionType = ''
    }
  })
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    overflow-y: auto;
    background-color: #f7f8fa;
    & > header {
      width: 100%;
      height: 66px;
      padding: 8px 0px;
      background-color: #fff;

      --van-search-input-height: 40px;

      button {
        color: #1255e4;
        font-size: 16px;
        &:active {
          opacity: 0.6;
        }
      }
    }

    .list {
      padding: 16px;
      .list_item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 56px;
        padding: 0 16px;
        border-radius: 12px 12px 12px 12px;
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
        h1 {
          color: rgba(0, 0, 0, 0.88);
          font-weight: 600;
          font-size: 16px;
          line-height: 24px;
          text-align: left;
        }
        .actions {
          display: flex;
          gap: 20px;
        }

        & + .list_item {
          margin-top: 16px;
        }
      }
    }
  }
</style>
