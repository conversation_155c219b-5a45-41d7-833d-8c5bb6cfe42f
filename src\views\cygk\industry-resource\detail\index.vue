<!-- 产业资源 -->
<template>
  <div class="resource_detail">
    <!-- banner -->
    <div class="banner">
      <img
        src="@/assets/images/cygk/resource-detail.png"
        alt=""
        class="banner_image" />
    </div>
    <div class="content">
      <Loading v-show="loading">加载中...</Loading>
      <!-- 头部信息 -->
      <div class="top_info">
        <div class="title_box">
          <div class="title">
            <div class="title_text">{{ pageData.name }}</div>
            <div
              v-if="pageData.type === '高等院校'"
              class="college"
              >（{{ pageData?.xy }}）</div
            >
          </div>
          <div
            class="tag"
            :style="{
              background: TAG_COLOR_MAP[type]?.bg,
              color: TAG_COLOR_MAP[type]?.text,
            }">
            {{ RESOURCE_TYPE_MAP[type] }}
          </div>
        </div>
        <div class="tags">
          <div
            v-for="(tag, index) in pageData.industrychain"
            :key="index"
            class="tag">
            {{ tag }}
          </div>
        </div>
        <div class="address">
          <div class="address_item">
            <div class="address_label">地址：</div>
            <div class="address_content">{{ pageData.dz }}</div>
          </div>
          <div class="address_item">
            <div class="address_label">行政区：</div>
            <div class="address_content">{{ pageData.ssxzq }}</div>
          </div>
        </div>
      </div>
      <!-- 简介 -->
      <div class="synopsis">
        <p
          v-for="(text, index) in pageData.jj"
          :key="index"
          class="synopsis_item">
          {{ text }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Loading } from 'vant'
  import { onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { getKeyDetail } from '@/apis'
  import { RESOURCE_TYPE_MAP, TAG_COLOR_MAP } from '../options'

  const route = useRoute()
  const { id, type } = route.query

  const pageData = ref({})
  const loading = ref(false)

  async function getPageData() {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await getKeyDetail(type, {
        id,
      })

      let industrychain = res.industrychain
      try {
        industrychain = JSON.parse(industrychain)
      } catch {
        if (industrychain.includes(',')) {
          industrychain = industrychain.split(',')
        } else {
          industrychain = [industrychain]
        }
      }

      pageData.value = {
        ...res,
        industrychain,
        jj: res.jj.split('\n'),
      }
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    getPageData()
  })
</script>

<style lang="scss" scoped>
  .resource_detail {
    .banner {
      width: 100%;
      height: 153px;

      .banner_image {
        width: 100%;
        height: 153px;
        object-fit: cover;
      }
    }

    .content {
      padding: 0 16px;

      .top_info {
        padding: 16px 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.06);

        .tag {
          border-radius: 2px;
          font-size: 12px;
          padding: 1px 4px;
          text-align: center;
          white-space: nowrap;
        }

        .title_box {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            display: flex;
            align-items: center;
            flex: 1;

            .title_text {
              font-weight: 600;
              font-size: 18px;
              color: rgba(0, 0, 0, 0.9);
              line-height: 26px;
            }

            .college {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.4);
              line-height: 26px;
            }
          }

          .tag {
            flex-shrink: 0;
          }
        }

        .tags {
          margin-top: 8px;
          display: flex;
          gap: 8px;
          overflow-x: auto;
          flex-wrap: wrap;

          .tag {
            background: rgba(0, 0, 0, 0.05);
            color: rgba(0, 0, 0, 0.6);
          }
        }

        .address {
          margin-top: 10px;
          .address_item {
            display: flex;
            // align-items: center;

            .address_label {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.6);
              line-height: 22px;
              flex-shrink: 0;
            }

            .address_content {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.4);
              line-height: 22px;
            }
          }
        }
      }

      .synopsis {
        margin: 16px 0;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        line-height: 22px;

        .synopsis_item {
          text-indent: 2em;
        }
      }
    }
  }
</style>
