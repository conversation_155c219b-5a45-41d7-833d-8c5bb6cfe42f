<template>
  <div class="financial-info">
    <div class="info">
      <div class="row">
        <div class="label">上市类型：</div>
        <div class="value">上海股票交易所</div>
      </div>
      <div class="row">
        <div class="label">股票代码：</div>
        <div class="value">中芯国际（60584）</div>
      </div>
      <div class="row">
        <div class="label">上市日期：</div>
        <div class="value">2023-06-03</div>
      </div>
      <div class="row">
        <div class="label">总市值：</div>
        <div class="value">4386.17亿人民币</div>
      </div>
      <div class="row">
        <div class="label">总股本：</div>
        <div class="value">249.6亿</div>
      </div>
    </div>
    <Divider>上市信息</Divider>
    <div class="info">
      <div class="row">
        <div class="label">总资产：</div>
        <div class="value">--</div>
      </div>
      <div class="row">
        <div class="label">营业规模（营业额）：</div>
        <div class="value">--</div>
      </div>
      <div class="row">
        <div class="label">盈利能力：</div>
        <div class="value">--</div>
      </div>
      <div class="row">
        <div class="label">经营活动现金流：</div>
        <div class="value">--</div>
      </div>
    </div>
    <Divider>资产信息</Divider>
    <div class="info">
      <div class="row">
        <div class="label">负债：</div>
        <div class="value red">--</div>
      </div>
      <div class="row">
        <div class="label">负债率：</div>
        <div class="value red">0%</div>
      </div>
    </div>
    <Divider>负债信息</Divider>
  </div>
</template>

<script setup>
  import { Divider } from 'vant'

  defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })
</script>

<style lang="scss" scoped>
  .financial-info {
    .info {
      margin-top: 16px;

      .row {
        display: flex;
        margin-bottom: 10px;
        font-size: 14px;

        .label {
          min-width: 70px;
          color: rgb(0 0 0 / 60%);
        }

        .value {
          color: rgb(0 0 0 / 90%);
        }

        .red {
          color: #f00;
        }
      }
    }
  }
</style>
