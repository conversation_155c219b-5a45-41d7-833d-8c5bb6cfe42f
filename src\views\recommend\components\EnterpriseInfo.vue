<template>
  <div class="info_container">
    <!-- <div class="header">
      <div class="name">
        <div class="flex-1">
          <TextEllipsis
            :content="info.companyName"
            class="text-[18px] font-[600]" />
        </div>

        <div
          v-if="!route.query.all"
          class="flex-shrink-0">
          <div
            v-if="info.isUndertaken"
            class="tag bg-[#F6FFED] text-[#52C41A]"
            >已承接</div
          >
          <div
            v-else
            class="tag bg-[#F3F3F3]"
            >未承接</div
          >
        </div>
      </div>

      <div
        v-if="!route.query.all"
        class="header_info">
        <div class="flex items-center">
          <div class="label">推送区域：</div>
          <div class="value">{{ info.region }}</div>
        </div>
        <div class="mt-[6px] flex items-center">
          <div class="label">接收人：</div>
          <div class="value">{{ info.sxlNickname }}</div>
        </div>
        <div class="mt-[6px] flex items-center">
          <div class="label">推送时间：</div>
          <div class="value">{{ info.createdAt }}</div>
        </div>
      </div>
      <div class="tags">
        <div
          v-for="(tag, tagIdx) in info.companyTags"
          :key="tagIdx"
          class="flex-shrink-0 rounded-[3px] bg-[#E6F4FF] px-[8px] py-[2px] text-[12px] text-[#1677FF]">
          {{ tag }}
        </div>
      </div>
    </div> -->
    <div class="info_container_header">
      <img
        v-if="info.logo"
        :src="info.logo"
        alt=""
        class="compony_logo" />
      <div class="compony_baseinfo">
        <div
          class="flex items-center"
          @click="jumptoCompony">
          <div class="compony_baseinfo_name">
            {{ info.companyName }}
          </div>
          <div class="flex items-center">
            <span class="text-[16px] text-[rgba(0,0,0,0.45)]">企业详情</span>
            <i
              class="iconfont icon-RightCircle ml-[6px] mt-[2px] text-[16px] text-[rgba(0,0,0,0.45)]"></i>
          </div>
        </div>
        <div class="compony_baseinfo_code_score">
          <div class="compony_code">信用代码：{{ info.creditCode }}</div>

          <!-- <div class="compony_score">
            <i class="iconfont icon-Score mr-[5px]"></i>
            <span>评分：{{ (+info.score).toFixed(2) }}</span>
          </div> -->
        </div>
        <div class="tags">
          <div
            v-for="(tag, tagIdx) in info.companyTags"
            :key="tagIdx"
            class="flex-shrink-0 rounded-[4px] bg-[#E6F4FF] px-[8px] py-[1px] text-[12px] leading-[20px] text-[#1677FF]">
            {{ tag }}
          </div>
        </div>
      </div>
    </div>
    <div class="info_container_center">
      <div class="center_item">
        <div class="center_item_label">法定代表人</div>
        <div class="center_item_value">{{ info.operName }}</div>
      </div>
      <div class="center_line"></div>
      <div class="center_item">
        <div class="center_item_label">注册资本</div>
        <div class="center_item_value">
          {{ parseInt(info.registCapi) + '万' }}
        </div>
      </div>
      <div class="center_line"></div>

      <div class="center_item">
        <div class="center_item_label">成立日期</div>
        <div class="center_item_value">
          {{ dayjs(info.startDate).format('YYYY-MM-DD') }}
        </div>
      </div>
    </div>
    <div class="info_container_footer">
      <div class="footer_content">
        <div class="flex-shrink-0 text-[rgba(0,0,0,0.4)]">经营范围：</div>
        <div class="footer_content_text">{{ info.scope }}</div>
        <div
          class="text-[#1677FF]"
          @click="onShowSynopsis">
          展开
        </div>
      </div>
      <!-- 本地关联按钮 -->
      <slot name="local_button"></slot>
    </div>
  </div>
  <Popup
    v-model:show="showSynopsis"
    position="bottom"
    :style="{
      height: '50%',
      borderRadius: '16px 16px 0px 0px',
      zIndex: 9999999,
    }">
    <div class="flex h-full flex-col p-[16px]">
      <div class="mb-[12px] text-[18px] font-[600] text-black">经营范围</div>
      <div
        class="flex-1 overflow-y-auto break-all text-[14px] text-[rgba(0,0,0,0.6)]">
        {{ info.scope }}
      </div>
    </div>
  </Popup>
</template>

<script setup>
  import dayjs from 'dayjs'
  import { Popup } from 'vant'
  import { ref } from 'vue'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const showSynopsis = ref(false)

  const onShowSynopsis = () => {
    showSynopsis.value = true
  }

  const jumptoCompony = () => {
    const url = `https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&extendInfo=110,481&redirectUrl=${encodeURIComponent(`https://isv.cnfic.com.cn/whtc-h5/#/pages/enterpriseDetails/index?id=${info.companyId}&title=${info.companyName}&hideBack=1`)}`
    window.open(url)
  }
</script>

<style lang="scss" scoped>
  .info_container {
    padding: 16px 16px 0;
    background-color: #fff;

    // .header {
    //   padding: 16px;
    //   background: #fff;

    //   .name {
    //     display: flex;
    //     justify-content: space-between;
    //     align-items: center;
    //   }

    //   .tag {
    //     padding: 2px 8px;
    //     font-size: 12px;
    //     line-height: 20px;
    //   }

    //   .header_info {
    //     margin-top: 12px;
    //     padding-bottom: 12px;
    //     border-bottom: 1px solid rgb(0 0 0 / 6%);
    //     line-height: 22px;

    //     .label {
    //       color: rgb(0 0 0 / 60%);
    //     }

    //     .value {
    //       color: rgb(0 0 0 / 90%);
    //     }
    //   }

    //   .tags {
    //     display: flex;
    //     flex-wrap: wrap;
    //     gap: 6px;
    //     padding-top: 12px;
    //   }
    // }

    &_header {
      display: flex;

      .compony_logo {
        width: 54px;
        height: 54px;
        border-radius: 6px;
      }

      .compony_baseinfo {
        flex: 1;
        margin-left: 8px;

        &_name {
          flex: 1;
          margin-right: 6px;
          color: rgb(0 0 0 / 90%);
          font-weight: 600;
          font-size: 18px;
          line-height: 26px;
          word-break: break-all;
        }

        &_code_score {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 4px;
          gap: 0 8px;
          font-size: 14px;

          .compony_code {
            color: rgb(0 0 0 / 60%);
          }

          .compony_fav {
            padding: 1px 6px;
            background-color: #fff2db;
            color: #ffb429;
          }

          .line {
            width: 1px;
            height: 10px;
            background: rgb(0 0 0 / 15%);
          }

          .compony_score {
            color: #1677ff;
          }
        }

        .tags {
          display: flex;
          flex-wrap: wrap;
          padding-top: 12px;
          gap: 8px;
        }
      }
    }

    &_center {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgb(0 0 0 / 6%);
      font-size: 14px;

      .center_item {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        line-height: 22px;

        &_label {
          color: rgb(0 0 0 / 60%);
        }

        &_value {
          color: rgb(0 0 0 / 90%);
        }
      }

      .center_line {
        width: 1px;
        height: 32px;
        background: rgb(0 0 0 / 6%);
      }
    }

    &_footer {
      .footer_content {
        display: flex;
        align-items: center;
        margin: 12px 0 8px;
        color: rgb(0 0 0 / 60%);
        font-size: 14px;
        line-height: 22px;

        &_text {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
</style>
