<template>
  <div
    class="card_box"
    @click="jumpTo">
    <div class="header">
      <div class="title">
        {{ info.companyName }}
      </div>
      <div class="info">
        <!-- <div class="flex items-center">
          <i
            class="iconfont icon-positioning2 text-[12px] text-[rgba(0,0,0,0.4)]"></i>
          <span class="ml-[5px]">{{ info.companyAddress }}</span>
        </div> -->
        <Tag
          :color="targetColorTag[info.label]?.bg || ''"
          :text-color="targetColorTag[info.label]?.txt || ''">
          {{ info.label }}
        </Tag>
        <template v-if="info.sharer">
          <div class="line"></div>
          <div class="flex items-center">
            <i
              class="iconfont icon-Contact text-[12px] text-[rgba(0,0,0,0.4)]"></i>
            <span class="ml-[5px]">{{ info.sharer }}</span>
          </div>
        </template>
      </div>
      <!-- <div class="number">
        <div class="w-[50%]">
          <span>座机号：</span>
          <span>{{ info.contactPersonMobile || '-' }}</span>
        </div>
        <div class="w-[50%]">
          <span>联系电话：</span>
          <span>{{ info.contactPersonPhone || '-' }}</span>
        </div>
      </div> -->
    </div>

    <div class="footer">
      <div
        class="footer_button"
        @click.stop="onShare">
        共享
      </div>
      <div
        class="footer_button"
        @click.stop="onView">
        查看
      </div>
      <div
        class="footer_button"
        @click.stop="onEdit">
        编辑
      </div>
      <div
        class="footer_button"
        @click.stop="jumpScheme">
        生成方案
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Tag } from 'vant'
  import { useRouter } from 'vue-router'
  import { targetColorTag } from '../options'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })
  const emits = defineEmits(['onShare'])

  const router = useRouter()

  function onShare() {
    emits('onShare', info)
  }

  function onView() {
    router.push({
      path: '/precise/addEnt',
      query: {
        key: '查看企业',
        name: info.companyName,
        bid: info.bid,
        cardBid: info.cardBid,
      },
    })
  }

  function jumpTo() {
    if (info?.companyId) {
      router.push({
        path: '/recommend-detail',
        query: { companyId: info.companyId },
      })
    } else {
      window.open(
        'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2FenterpriseSearch%2FenpSearch' +
          encodeURIComponent(`?companyName=${info.companyName}`),
      )
    }
  }

  function onEdit() {
    router.push({
      path: '/precise/addEnt',
      query: {
        key: '编辑企业',
        name: info.companyName,
        bid: info.bid,
        cardBid: info.cardBid,
        id: info.bid,
      },
    })
  }

  function jumpScheme() {
    router.push({
      path: '/precise/scheme',
      query: {
        name: info.companyName,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .card_box {
    position: relative;
    width: 100%;
    margin-bottom: 16px;
    padding: 12px;
    border-radius: 8px;
    background-color: #fff;

    .fav {
      position: absolute;
      top: 0;
      right: 12px;
    }

    .header {
      padding-bottom: 12px;
      border-bottom: 1px solid rgb(0 0 0 / 6%);

      .title {
        padding-right: 19px;
        color: rgb(0 0 0 / 90%);
        font-weight: 600;
        font-size: 17px;
      }

      .info {
        display: flex;
        align-items: center;
        color: rgb(0 0 0 / 60%);
        font-size: 12px;

        .line {
          width: 1px;
          height: 10px;
          margin: 0 13px;
          background: rgb(0 0 0 / 15%);
        }
      }

      .number {
        display: flex;
        align-items: center;
        margin-top: 4px;
        color: rgb(0 0 0 / 60%);
        font-size: 12px;
      }
    }

    .footer {
      display: flex;
      justify-content: flex-end;
      padding: 8px 0;
      gap: 0 8px;

      .footer_button {
        width: 72px;
        padding: 3px 0;
        border-radius: 100px;
        background: rgb(18 85 228 / 6%);
        color: #1255e4;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;
        text-align: center;
      }
    }
  }
</style>
