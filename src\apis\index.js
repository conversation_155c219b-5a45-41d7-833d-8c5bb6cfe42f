import { upload } from '@/utils/alova'
import { imageBaseUrl } from '@/utils/config'
import httpClient from '@/utils/http'

/**
 * 查询对象分页列表
 */

export const getPageList = (key, data) =>
  httpClient.post(`/api/v1/${key}/query/pageList`, data)

/**
 * 查询对象全部列表
 */

export const getAllPageList = (key, data) =>
  httpClient.post(`/api/v1/${key}/query/list`, data)

/**
 * 查询单个对象详情
 */

export const getKeyDetail = (key, data = {}) =>
  httpClient.post(`/api/v1/${key}/query/whereOne`, data)

/**
 * 查询产业资源分页列表
 */
export const getIndustryResourceList = (data) =>
  httpClient.post(`/api/v1/object/query/unionPageList`, data)

/**
 * 查询企业种类下拉列表
 */
export const getIndustryDropDown = (key, data) =>
  httpClient.post(`/api/v1/${key}/query/dropDown`, data)

/**
 * 获取图片url
 */
export const getImgUrl = (url) => httpClient.get(`${imageBaseUrl}${url}`)

/** 文件上传 */
export const uploadFile = (file) => {
  return upload(
    '/api/v1/file/uploads?private=false',
    {
      file,
    },
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  )
}
