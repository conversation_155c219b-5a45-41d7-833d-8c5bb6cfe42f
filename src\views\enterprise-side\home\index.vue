<template>
  <div class="enterprise_home">
    <div>
      <!-- <video
        class="w-full"
        controls
        poster="@/assets/videos/cover/home.png">
        <source src="@/assets/videos/首页宣传片.mp4" />
        <span>您的浏览器不支持播放该视频</span>
      </video> -->
    </div>
    <div class="pt-[16px]">
      <div class="flex justify-between px-[16px]">
        <div
          class="policy_button"
          @click="router.push('/enterprise-side/policy')">
          <div>查政策</div>
        </div>
        <div class="other_button">
          <div
            class="other_button_item park"
            @click="
              router.push('/enterprise-side/introduce?type=park&key=园区')
            ">
            查园区
          </div>
          <div
            class="other_button_item build"
            @click="
              router.push('/enterprise-side/introduce?type=building&key=楼宇')
            ">
            查楼宇
          </div>
        </div>
      </div>
      <div class="mt-[12px] px-[16px]">
        <div
          v-for="(item, index) in buttonList"
          :key="index"
          class="button_item"
          @click="buttonClick(item)">
          <div class="flex items-center">
            <img
              class="button_icon"
              :src="item.icon"
              alt="" />
            <span class="ml-[8px]">{{ item.title }}</span>
          </div>
          <div class="text-[rgba(0,0,0,0.4)]">></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import qjztImg from '@/assets/images/enterprise-side/qjzt.png'
  import yjfkImg from '@/assets/images/enterprise-side/yjfk.png'
  import znwdImg from '@/assets/images/enterprise-side/znwd.png'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const buttonList = [
    {
      icon: znwdImg,
      title: '智问答',
      url: `${import.meta.env.VITE_CHAT_URL}?id=f0d9d7c277de9abe`,
    },
    {
      icon: qjztImg,
      title: '区级专题',
      path: '/enterprise-side/area',
    },
    {
      icon: yjfkImg,
      title: '意见反馈',
      path: '/enterprise-side/feedback',
    },
  ]

  function buttonClick(item) {
    if (item.url) {
      window.open(item.url)
    } else if (item.path) {
      router.push(item.path)
    }
  }
</script>

<style lang="scss" scoped>
  .enterprise_home {
    width: 100%;
    height: 100%;
    background-color: #f7f8fa;
    color: rgb(0 0 0 / 90%);
    font-size: 14px;
    line-height: 22px;

    .policy_button {
      width: 165px;
      height: 160px;
      padding: 16px;
      border-radius: 12px;
      background-color: #fff;
      background-image: url('@/assets/images/enterprise-side/policy.png');
      background-size: cover;
      background-repeat: no-repeat;
    }

    .other_button {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 165px;
      height: 160px;

      .other_button_item {
        width: 100%;
        height: 74px;
        padding: 16px;
        border-radius: 12px;
        background-color: #fff;
        background-size: cover;
        background-repeat: no-repeat;

        &.park {
          background-image: url('@/assets/images/enterprise-side/park.png');
        }

        &.build {
          background-image: url('@/assets/images/enterprise-side/build.png');
        }
      }
    }

    .button_item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 16px;
      border-radius: 12px;
      background-color: #fff;
      box-shadow: 0 0 10px 0 rgb(0 0 0 / 6%);

      .button_icon {
        width: 38px;
        height: 38px;
      }

      & + .button_item {
        margin-top: 12px;
      }
    }
  }
</style>
