<!-- 重点区域 -->
<template>
  <div class="contains">
    <Tabs v-model:active="active">
      <Tab
        title="位置区位图"
        name="位置区位图"></Tab>
      <Tab
        title="重点企业点位图"
        name="重点企业点位图"></Tab>
      <Tab
        v-if="currentData.cxzydwt"
        title="创新资源点位图"
        name="创新资源点位图"></Tab>
    </Tabs>
    <div class="image relative">
      <img
        v-if="active === '位置区位图'"
        class="h-full w-[375px]"
        style="object-fit: contain"
        src="./images/map-02.png" />
      <img
        class="absolute left-0 top-0 h-full w-[375px]"
        :class="{
          show_hidden: active === '位置区位图',
        }"
        style="object-fit: contain"
        :src="currentImage"
        alt=""
        @click="show = true" />
    </div>
    <div class="bottom">
      <div
        v-show="active === '位置区位图'"
        class="text">
        <h2 class="text-[18px] font-bold">{{ route.query.key }}</h2>
        <div class="region_introduce">
          <div class="title">区域描述：</div>
          <div class="introduce">{{ currentData.jianjie || '暂无数据' }}</div>
        </div>
        <div class="region_introduce">
          <div class="title">重点产业：</div>
          <div
            v-if="currentData.zdcy"
            class="introduce">
            <Tag
              v-for="item in currentData.zdcy"
              :key="item"
              type="primary">
              {{ item }}
            </Tag>
          </div>
        </div>
      </div>
      <div
        v-show="active === '重点企业点位图' && currentData.qiye_data"
        class="box">
        <VChart
          autoresize
          :option="option"
          class="h-full w-full"></VChart>
      </div>
      <div
        v-show="active === '创新资源点位图' && currentData.cxzy_data"
        class="box">
        <VChart
          autoresize
          :option="barOption"
          class="h-full w-full"></VChart>
      </div>
    </div>
  </div>
  <ImagePreview
    v-model:show="show"
    :images="[currentBigImage]"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import fenqu_data from '@/assets/json/fenqu_data.js'
  import { extractFileName } from '@/utils/index'
  import { ImagePreview, Tab, Tabs, Tag } from 'vant'
  import { computed, ref } from 'vue'
  import { useRoute } from 'vue-router'
  const route = useRoute()
  const active = ref('位置区位图')

  const currentData = computed(() => {
    console.log('route.query.key', route.query.key)
    const data = Object.values(fenqu_data).find((item) => {
      console.log('item.name', item.name.replace('（', '(').replace('）', ')'))
      return (
        item.name.replace('(', '（').replace(')', '）') ===
        route.query.key.replace('(', '（').replace(')', '）')
      )
    })

    if (data && !Array.isArray(data.zdcy)) {
      const zdcy = data.zdcy.split('、')
      data.zdcy = zdcy
    }
    return data ? data : null
  })
  const colors = ['#5892F7', '#29D98A', '#EEA969', '#12BEE4', 'pink']
  const option = computed(() => ({
    xAxis: {
      type: 'category',
      data: currentData.value.qiye_data
        ? currentData.value.qiye_data.map((item) => item.country)
        : [],
      axisLabel: {
        fontSize: 6, // 设置 X 轴标签的字体大小
        color: '#333', // 设置 X 轴标签的字体颜色
      },
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: currentData.value.qiye_data
          ? currentData.value.qiye_data.map((item, index) => {
              return {
                value: item.litres,
                itemStyle: { color: colors[index % colors.length] },
              }
            })
          : [],
        type: 'bar',
        label: {
          show: true, // 显示标签
          position: 'top', // 标签显示在柱状图的顶部
          formatter: '{c}', // 显示柱状图的数据
          fontSize: 10,
        },
      },
    ],
  }))

  const barOption = computed(() => ({
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        // adjust the start and end angle
        data: currentData.value.cxzy_data
          ? currentData.value.cxzy_data.map((item) => ({
              value: item.litres,
              name: item.country,
            }))
          : [],
      },
    ],
  }))
  const show = ref(false)
  // const extractFileName = (filePath) => {
  //   console.log(filePath)

  //   // eslint-disable-next-line no-useless-escape
  //   const match = filePath.match(/([^\/]+)\.[^.]+$/)
  //   // 如果匹配成功，返回不包含扩展名的文件名
  //   if (match && match[1]) {
  //     return match[1]
  //   }
  //   // 如果没有匹配到，返回原始文件路径
  //   return ''
  // }
  const currentImage = computed(() => {
    const _thisImages = {
      位置区位图: 'wzqwt',
      重点企业点位图: 'qydwt',
      创新资源点位图: 'cxzydwt',
    }[active.value]

    const _thisImagesName = extractFileName(currentData.value[_thisImages])
    console.log('_thisImagesName', _thisImagesName)

    return new URL(`./images/${_thisImagesName}`, import.meta.url).href
  })
  const currentBigImage = computed(() => {
    const _thisImages = {
      位置区位图: 'wzqwt_big',
      重点企业点位图: 'qydwt',
      创新资源点位图: 'cxzydwt',
    }[active.value]

    const _thisImagesName = extractFileName(currentData.value[_thisImages])
    console.log('_thisImagesName', _thisImagesName)

    return new URL(`./images/${_thisImagesName}`, import.meta.url).href
  })
</script>
<style lang="scss" scoped>
  .contains {
    background-color: #f7f8fa;

    .image {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 330px;
    }

    .bottom {
      width: 100%;
      padding: 16px;

      .box {
        width: 100%;
        height: 226px;
        background-color: #fff;
      }

      .text {
        padding: 16px;
        background-color: #fff;

        .region_introduce {
          display: flex;
          gap: 10px;
          margin-top: 8px;
          font-size: 14px;

          .title {
            min-width: 80px;
            color: rgb(0 0 0 / 60%);
          }

          .introduce {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            color: rgb(0 0 0 / 40%);
          }
        }
      }
    }
  }

  @keyframes show-hidden {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  .show_hidden {
    animation: show-hidden 3s infinite ease-in-out;
  }
</style>
