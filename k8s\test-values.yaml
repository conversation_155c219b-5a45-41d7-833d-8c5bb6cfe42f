replicaCount: 1
imagePullSecrets:
  - name: harbor-secret
service:
  port: 80
ingress:
  enabled: true
  annotations:
    nginx.ingress.kubernetes.io/use-regex: 'true'
    nginx.ingress.kubernetes.io/rewrite-target: /$1
  hosts:
    - host: tcjzs-h5.test.youpin-k8s.net
      paths:
        - path: /zsys/(.*)
configMap:
  env:
    # WEBAPP_ENV.K8S_API_BASE_URL: https://jzzs.whcftzcj.com/zsys
    WEBAPP_ENV.K8S_API_BASE_URL: https://tcj-api.youpin-k8s.net
    WEBAPP_ENV.K8S_IMAGES_BASE_URL: https://jzzs.whcftzcj.com/zsys/api/v1/file/presigned?url=
    WEBAPP_ENV.K8S_API_BASE_PREFIX: /zsys
    # WEBAPP_ENV.K8S_API_RECOMMEND_BASE_URL: https://jzzs.whcftzcj.com/zsys/erecommend-api
    WEBAPP_ENV.K8S_API_RECOMMEND_BASE_URL: https://tcj-erecommend-api.youpin-k8s.net
    WEBAPP_ENV.K8S_API_RECOMMEND_DETAIL_URL: https://jzzs.whcftzcj.com/zsys/data-service/api
    WEBAPP_ENV.K8S_API_RELATION_BASE_URL: https://apigateway.widi.cloud:4431/apigateway/tcj-graph
    WEBAPP_ENV.K8S_CHAT_URL: https://apigateway.widi.cloud:4431/apigateway/zsys/zhinengwenda/answer-m
    WEBAPP_ENV.K8S_FEEDBACK_URL: https://tcj-api.youpin-k8s.net
    WEBAPP_ENV.K8S_ADD_SCHEME_URL: https://tcjzs-ai-chat.test.youpin-k8s.net/apigateway/zsys/zhinengwenda
    WEBAPP_ENV.K8S_SCHEME_URL: https://apigateway.widi.cloud:4431/apigateway/investment-report/api/v1
    WEBAPP_ENV.K8S_KKFILE_BASE_URL: https://file-view.youpin-k8s.net/onlinePreview
resources:
  limits:
    cpu: 100m
    memory: 100Mi
  requests:
    cpu: 100m
    memory: 100Mi
