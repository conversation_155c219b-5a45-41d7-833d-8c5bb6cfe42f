<template>
  <MyList
    ref="listComRef"
    :show-search-bar="false"
    title="机构联络库"
    :filters
    :loading
    :total="paginate.total"
    :has-next="paginate.hasNext"
    :list-arr="list"
    :show-filter-key="showFilter<PERSON>ey"
    @card-click="onCardClick"
    @down="onDown"
    @search="onSearch">
    <template #card="{ item }">
      <div class="tag">{{ item.type }}</div>
      <div class="name">{{ item.name }}</div>
      <div class="address">
        <i
          class="iconfont icon-positioning2 mt-[3px] text-[12px] text-[rgba(0,0,0,0.4)]"></i>
        <span class="ml-[4px]">{{ item.city }}</span>
      </div>
    </template>
  </MyList>
</template>

<script setup>
  import {
    citiesOption,
    getTypeOfOrganization,
    institutionListApi,
  } from '@/apis/precise'
  import router from '@/router'
  import { useResourceStore } from '@/stores/resource'
  import {
    computed,
    nextTick,
    onBeforeUnmount,
    onMounted,
    ref,
    useTemplateRef,
  } from 'vue'
  import MyList from '../components/MyList/index.vue'

  const listComRef = useTemplateRef('listComRef')
  const resourceStore = useResourceStore()

  const loading = ref(false)
  const paginate = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    hasNext: true,
  })
  // 存储传入的类型
  const types = ref([])
  // const area = ref([])
  const list = ref([])
  const keyword = ref('')
  const organizationTypeOptions = ref([])

  async function getOrganizationTypes() {
    try {
      const {
        data: { data: res },
      } = await getTypeOfOrganization()
      organizationTypeOptions.value = res.map((i) => ({
        name: i,
        value: i,
      }))
    } catch (error) {
      console.error('获取机构类型失败：', error)
    }
  }

  function showFilterKey() {
    return types.value.length > 0 ? types.value[0] : '全部类型'
  }

  const areaOptions = ref([])
  async function getCity() {
    try {
      const {
        data: { data: res },
      } = await citiesOption()
      console.log(res)
      areaOptions.value = res.map((item) => ({
        text: item,
        value: item,
      }))
    } catch (error) {
      console.log(error)
    }
  }

  const filters = computed(() => {
    return [
      {
        key: 'types',
        value: types.value,
        emptyText: '全部类型',
        options: organizationTypeOptions.value,
        // multiple: true,
        onChange(v) {
          types.value = [v.value]
          console.log(v.value)
          list.value = []
          expertList({
            page: {
              pageNum: 1,
              pageSize: 10,
            },
            params: {
              // name: keyword.value,
              types: types.value,
              // areas: area.value,
            },
          })
        },
        showTitle() {
          return types.value.length > 0 ? types.value[0] : '全部类型'
        },
        onClear() {
          types.value = []
          list.value = []
          expertList({
            page: {
              pageNum: 1,
              pageSize: 10,
            },
            params: {
              // name: keyword.value,
              types: [],
              // areas: area.value,
            },
          })
        },
      },
    ]
  })

  async function expertList(
    params = {
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      params: {},
    },
  ) {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await institutionListApi(params)
      list.value.push(...res.list)
      paginate.value.total = res.total
      paginate.value.page = res.page
      paginate.value.hasNext = res.hasNext

      resourceStore.storeMap.institution.list = list.value
      resourceStore.storeMap.institution.paginate = paginate.value
      resourceStore.storeMap.institution.types = types.value
      console.log('存入数据', resourceStore.storeMap)
    } catch (e) {
      console.log(e)
    } finally {
      loading.value = false
    }
  }

  function onSearch(val) {
    keyword.value = val
    list.value = []
    expertList({
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      params: {
        // name: val,
        types: types.value,
        // areas: area.value,
      },
    })
  }

  function onDown() {
    expertList({
      page: {
        pageNum: paginate.value.page + 1,
        pageSize: 10,
      },
      params: {
        // name: keyword.value,
        types: types.value,
        // areas: area.value,
      },
    })
  }

  function onCardClick(item) {
    router.push({
      path: '/precise/institution-detail',
      query: {
        id: item.id,
      },
    })
  }

  onMounted(() => {
    getOrganizationTypes()
    getCity()
    if (!resourceStore.storeMap.institution) {
      resourceStore.addKey('institution')
      onSearch()
    } else {
      list.value = resourceStore.storeMap.institution.list
      paginate.value = resourceStore.storeMap.institution.paginate
      types.value = resourceStore.storeMap.institution.types
      nextTick(() => {
        listComRef.value.listRef.scrollTo({
          top: resourceStore.storeMap.institution.y,
        })
      })
    }
  })

  onBeforeUnmount(() => {
    resourceStore.storeMap.institution.y = listComRef.value.y
  })
</script>

<style lang="scss" scoped>
  .tag {
    position: absolute;
    top: 0;
    right: 0;
    padding: 1px 8px;
    border-top-right-radius: 8px;
    border-bottom-left-radius: 8px;
    background-color: #e6f4ff;
    color: #1677ff;
    font-size: 12px;
  }

  .name {
    color: rgb(0 0 0 / 90%);
    font-weight: 600;
    font-size: 16px;
  }

  .address {
    display: flex;
    align-items: center;
    margin-top: 4px;
    color: rgb(0 0 0 / 60%);
    font-size: 12px;
  }
</style>
