<template>
  <div class="main_page">
    <div class="list">
      <div @click="navigate(item.path)" class="item" v-for="item in list" :key="item.path">
        <img style="width: 100%" :src="item.img" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import sdlc from '@/assets/images/tzzn/xfyj/sdlc.png'
import zjxfg from '@/assets/images/tzzn/xfyj/zjxfg.png'

const router = useRouter()

const list = [
  { path: 'sdlc', img: sdlc },
  { path: 'zjxfg', img: zjxfg }
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    padding: 16px;
    flex-direction: column;
    gap: 16px;
  }
}
</style>
