<template>
  <div class="statistics-wrapper">
    <div class="statistics-container">
      <!-- 选项卡导航 -->
      <div class="tabs">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          class="tab"
          :class="{ active: activeTab === tab.id }"
          @click="switchTab(tab.id)">
          {{ tab.name }}
        </div>
      </div>

      <!-- 选项卡内容 -->
      <div class="content-wrapper">
        <div
          v-for="tab in tabs"
          :key="tab.id"
          class="content"
          :class="{ active: activeTab === tab.id }">
          <!-- 骨架屏加载状态 -->
          <div
            v-if="isTabLoading(tab.id)"
            class="skeleton-container">
            <div
              v-for="n in 11"
              :key="n"
              class="skeleton-item">
              <div class="skeleton-content">
                <div class="skeleton-title">
                  <div class="skeleton-text skeleton-name"></div>
                  <div class="skeleton-text skeleton-percentage"></div>
                  <div class="skeleton-text skeleton-count"></div>
                </div>
                <div class="skeleton-bar"></div>
              </div>
            </div>
          </div>

          <!-- 实际内容 -->
          <div
            v-else
            class="data-container">
            <div
              v-for="(item, index) in getTabData(tab.id)"
              :key="`${tab.id}-${index}`"
              class="information">
              <div class="information-content">
                <div class="information-title">
                  <p>{{ item.name }}</p>
                  <span>{{ item.percentage }}%</span>
                  <div class="information-title-count">
                    <p>{{ formatNumber(item.count) }} 条</p>
                  </div>
                </div>
                <div class="information-bar">
                  <div
                    class="progress"
                    :style="{
                      width: item.percentage + '%',
                      backgroundColor: getProgressColor(index),
                    }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    getApplicationScenario,
    getCommercialBuilding,
    getIndustryPolicy,
    getInnovationResource,
    getindustrialPark,
  } from '@/apis/precise.js'
  import { onMounted, ref, watch } from 'vue'

  const tabs = [
    { id: 'policy', name: '产业政策' },
    { id: 'park', name: '产业园区' },
    { id: 'building', name: '商务楼宇' },
    { id: 'scenario', name: '应用场景' },
    { id: 'innovation', name: '创新资源' },
  ]

  const activeTab = ref('policy')

  // 数据状态管理
  const tabDataStatus = ref({
    policy: { loading: false, loaded: false },
    park: { loading: false, loaded: false },
    building: { loading: false, loaded: false },
    scenario: { loading: false, loaded: false },
    innovation: { loading: false, loaded: false },
  })

  // 接口数据
  const tabDataMap = ref({
    policy: [],
    park: [],
    building: [],
    scenario: [],
    innovation: [],
  })

  // 默认数据
  const createDefaultData = () => {
    return Array.from({ length: 11 }, (_, index) => ({
      name: `数据项 ${index + 1}`,
      percentage: Math.floor(Math.random() * 80) + 10,
      count: Math.floor(Math.random() * 1000) + 100,
    }))
  }

  // API 映射
  const apiMap = {
    policy: getIndustryPolicy,
    park: getindustrialPark,
    building: getCommercialBuilding,
    scenario: getApplicationScenario,
    innovation: getInnovationResource,
  }

  // 切换选项卡
  const switchTab = async (tabId) => {
    if (activeTab.value === tabId) return

    activeTab.value = tabId

    // 如果数据未加载，开始加载
    if (
      !tabDataStatus.value[tabId].loaded &&
      !tabDataStatus.value[tabId].loading
    ) {
      loadTabData(tabId)
    }
  }

  // 加载标签数据
  const loadTabData = async (tabId) => {
    const status = tabDataStatus.value[tabId]
    if (status.loading || status.loaded) return

    status.loading = true

    try {
      const apiFunction = apiMap[tabId]
      const { data } = await apiFunction()
      const response = data.data

      // 模拟网络延迟
      await new Promise((resolve) => setTimeout(resolve, 250))

      // 数据映射
      const processedData = response.map((item) => ({
        name: item.name,
        percentage: Number(item.percentage) || 0,
        count: Number(item.count) || 0,
      }))

      tabDataMap.value[tabId] = processedData
      status.loaded = true
    } catch (error) {
      console.error(`加载 ${tabId} 数据时出错:`, error)
      // 使用默认数据
      tabDataMap.value[tabId] = createDefaultData()
      status.loaded = true
    } finally {
      status.loading = false
    }
  }

  // 获取标签数据
  const getTabData = (tabId) => {
    return tabDataMap.value[tabId].length > 0
      ? tabDataMap.value[tabId]
      : createDefaultData()
  }

  // 检查标签是否正在加载
  const isTabLoading = (tabId) => {
    return (
      tabDataStatus.value[tabId].loading && !tabDataStatus.value[tabId].loaded
    )
  }

  // 色卡颜色
  const getProgressColor = (index) => {
    const colors = [
      '#5893DB',
      '#7EAFEA',
      '#7EC8EA',
      '#7EDFEA',
      '#58DBA0',
      '#7EEABD',
      '#CFEA7E',
      '#EAE57E',
      '#EAC27E',
      '#EA857E',
      '#EA7EBF',
      '#EA636C',
      '#C27EEA',
      '#DF63EA',
      '#877EEA',
      '#8063EA',
    ]
    return colors[index] || '#4a90e2'
  }

  // 数值格式化
  const formatNumber = (num) => {
    if (num === undefined || num === null || num === 0) {
      return '0'
    }
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  // 预加载相邻标签的数据
  const preloadAdjacentTabs = (currentTabId) => {
    const currentIndex = tabs.findIndex((tab) => tab.id === currentTabId)
    const preloadTabs = []

    if (currentIndex > 0) preloadTabs.push(tabs[currentIndex - 1].id)
    if (currentIndex < tabs.length - 1)
      preloadTabs.push(tabs[currentIndex + 1].id)

    preloadTabs.forEach((tabId) => {
      if (
        !tabDataStatus.value[tabId].loaded &&
        !tabDataStatus.value[tabId].loading
      ) {
        loadTabData(tabId)
      }
    })
  }

  // 监听活跃标签变化
  watch(activeTab, (newTabId) => {
    setTimeout(() => {
      preloadAdjacentTabs(newTabId)
    }, 0)
  })

  onMounted(async () => {
    // 加载初始数据
    await loadTabData('policy')
    // 预加载下一个标签
    preloadAdjacentTabs('policy')
  })
</script>

<style lang="scss" scoped>
  @keyframes loading {
    0% {
      background-position: 200% 0;
    }

    100% {
      background-position: -200% 0;
    }
  }

  .statistics-wrapper {
    overflow-x: hidden;
  }

  .tabs {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    overflow-x: auto;
    width: 100%;
    height: 44px;
    padding-right: 16px;
    background-color: #fff;
    font-size: 14px;
    white-space: nowrap;

    &::-webkit-scrollbar {
      display: none;
    }

    .tab {
      position: relative;
      width: 56px;
      height: 20px;
      margin: 0 12px;
      color: #969799;
      font-weight: 500;
      line-height: 20px;
      cursor: pointer;

      &.active {
        color: #323232;

        &::after {
          content: '';
          position: absolute;
          bottom: -12px;
          left: 0;
          width: 40px;
          height: 3px;
          border-radius: 1px;
          background-color: #007bff;
          transform: translateX(25%);
        }
      }
    }
  }

  .content-wrapper {
    position: relative;
    width: 100%;
    min-height: 600px;
  }

  .content {
    display: none;
    flex-direction: column;
    align-items: center;
    width: 375px;
    padding: 40px 0 10px;
    background-color: #fff;

    &.active {
      display: flex;
    }
  }

  .data-container {
    width: 100%;
  }

  .information {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 343px;
    height: 56px;
    margin: 6px;
    border-radius: 10px;
    background-color: #f4f4f4;
  }

  .information-content {
    width: 100%;

    .information-title {
      display: flex;
      align-items: center;
      padding: 0 16px;
      color: rgb(0 0 0 / 88%);
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;

      .information-title-count {
        margin-left: auto;
        color: #000;
        font-size: 14px;
      }
    }

    .information-title > span {
      width: 42px;
      height: 20px;
      padding: 0 6px;
      color: rgb(0 0 0 / 50%);
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
    }

    .information-bar {
      overflow: hidden;
      height: 5px;
      margin: 5px 16px 0;
      border-radius: 4px;
      background-color: #e0e0e0;

      .progress {
        height: 100%;
        border-radius: 4px;
      }
    }
  }

  // 骨架屏样式
  .skeleton-container {
    width: 100%;
  }

  .skeleton-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 343px;
    height: 56px;
    margin: 6px auto;
    border-radius: 10px;
    background-color: #f4f4f4;
  }

  .skeleton-content {
    width: 100%;
  }

  .skeleton-title {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 0 16px;
  }

  .skeleton-text {
    border-radius: 4px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;

    &.skeleton-name {
      width: 60px;
      height: 16px;
    }

    &.skeleton-percentage {
      width: 30px;
      height: 14px;
      margin-left: 8px;
    }

    &.skeleton-count {
      width: 50px;
      height: 14px;
      margin-left: auto;
    }
  }

  .skeleton-bar {
    height: 5px;
    margin: 0 16px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }
</style>
