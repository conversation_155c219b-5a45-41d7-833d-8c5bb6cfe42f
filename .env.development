# 测试环境
# VITE_API_BASE_URL = https://tcj-api.youpin-k8s.net
# 线上环境
VITE_API_BASE_URL = https://jzzs.whcftzcj.com/zsys
# VITE_API_BASE_URL = https://whjzzs.gzjp.cn/zsys
# VITE_API_BASE_URL = http://192.168.0.163:8081
VITE_IMAGES_BASE_URL = https://jzzs.whcftzcj.com/zsys/api/v1/file/presigned?url=
# VITE_IMAGES_BASE_URL = https://tcj-api.youpin-k8s.net/api/v1/file/presigned?url=
VITE_API_BASE_PREFIX = /zsys
VITE_API_RECOMMEND_BASE_URL = https://tcj-erecommend-api.youpin-k8s.net
# VITE_API_RECOMMEND_BASE_URL = https://jzzs.whcftzcj.com/zsys/erecommend-api
VITE_API_RECOMMEND_DETAIL_URL = https://jzzs.whcftzcj.com/zsys/data-service/api
#VITE_API_RELATION_BASE_URL = http://*************:5050
VITE_API_RELATION_BASE_URL = https://apigateway.widi.cloud:4431/apigateway/tcj-graph
VITE_CHAT_URL = https://apigateway.widi.cloud:4431/apigateway/zsys/zhinengwenda/answer-m
# 反馈
VITE_FEEDBACK_URL = https://tcj-api.youpin-k8s.net
# 添加企业、方案生成基础地址
VITE_ADD_SCHEME_URL = http://localhost:5174
# 方案生成接口
# VITE_SCHEME_URL = http://************:8787/api/v1
VITE_SCHEME_URL = https://apigateway.widi.cloud:4431/apigateway/investment-report/api/v1
# KKFILE预览
VITE_KKFILE_BASE_URL = https://file-view.youpin-k8s.net/onlinePreview