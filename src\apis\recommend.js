import httpClient from '@/utils/http'

const baseUrl = import.meta.env.VITE_API_RECOMMEND_BASE_URL

// 产业链
export const getIndustry = () =>
  httpClient.get(`${baseUrl}/api/v1/industry-chain/treeList`)

// 获取企业推荐列表（全量）
export const getRecommendAllList = (params) =>
  httpClient.post(`${baseUrl}/api/v1/recommend/company/list`, params)

// 获取企业推荐详情（全量）
export const getRecommendAllDetail = (params) =>
  httpClient.get(`${baseUrl}/api/v1/company/detail`, params)

// 获取用户信息
export const getUserInfo = (params) =>
  httpClient.get(`${baseUrl}/api/v1/userinfo`, params)

// 获取企业推荐列表
export const getRecommendList = (params) =>
  httpClient.post(`${baseUrl}/api/v1/recommend/companies/page?`, params)

// 获取用户列表
export const getUserList = (params) =>
  httpClient.post(`${baseUrl}/api/v1/userinfo/pageList`, params)

// 分配
export const trunover = (params) =>
  httpClient.post(`${baseUrl}/api/v1/recommend/companies/turnOver`, params)
