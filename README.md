# vue-project

This template should help get you started developing with Vue 3 in Vite.

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Customize configuration

See [Vite Configuration Reference](https://vitejs.dev/config/).

## Project Setup

```sh

pnpminstall

```

### Compile and Hot-Reload for Development

```sh

pnpmdev

```

### Compile and Minify for Production

```sh

pnpmbuild

```

### Lint with [ESLint](https://eslint.org/)

```sh

pnpmlint

```
