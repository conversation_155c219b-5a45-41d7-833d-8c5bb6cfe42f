import { getInvestmentCount } from '@/apis/zszyk'
import { useOrgFormStore } from '@/stores/zszyk'
import { isEmpty } from '@/utils/is'
import { useRequest } from 'alova/client'
import { toRef } from 'vue'

export function useOrgTypeOptions() {
  const store = useOrgFormStore()

  const { data, send } = useRequest(getInvestmentCount, {
    initialData: [],
    immediate: false,
  }).onSuccess(({ data }) => {
    store.typeOptions = data
  })

  // 不存在类型列表拉取数据
  if (isEmpty(store.typeOptions)) {
    send()

    return data
  }

  return toRef(store, 'typeOptions')
}
