<template>
  <div class="resource">
    <img
      width="100%"
      height="120px"
      src="@/assets/images/precise/cxzy.png" />
    <div class="content">
      <div
        v-for="(item, index) in buttons"
        :key="index"
        class="content_button">
        <div
          class="flex items-center"
          @click="jumpTo(item.path)">
          <div
            class="flex h-[36px] w-[36px] flex-shrink-0 items-center justify-center rounded-[8px] bg-white">
            <i
              class="iconfont text-[20px]"
              :style="item.style"
              :class="[item.icon]"></i>
          </div>
          <span class="ml-[8px] flex-shrink-0">{{ item.title }}</span>
        </div>
        <div class="number">{{ item.number || 0 }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getTextGradient } from '@/utils'
  import { useCount } from '@/utils/precise'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const router = useRouter()
  const route = useRoute()

  const countObj = ref({})

  const countAll = ['innovate', 'research', 'colleges']

  onMounted(() => {
    Promise.all(
      countAll.map((item) => {
        useCount(item, {
          onSuccess({ data: { data: res } }) {
            countObj.value[item] = res
          },
        })
      }),
    )
  })

  const buttons = computed(() => [
    {
      icon: 'icon-industry',
      title: '高等院校',
      style: getTextGradient(['#69D9F4', '#54B5F6'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'colleges',
          key: '高等院校',
          noFooter: route.query.noFooter,
        },
      },
      number: countObj.value?.colleges,
    },
    {
      icon: 'icon-policy',
      title: '科研院所',
      style: getTextGradient(['#74A8FE', '#547AF7'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'research',
          key: '科研院所',
          noFooter: route.query.noFooter,
        },
      },
      number: countObj.value?.research,
    },
    {
      icon: 'icon-cy',
      title: '功能平台',
      style: getTextGradient(['#FFCA65', '#F6AB52'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'innovate',
          key: '功能平台',
          noFooter: route.query.noFooter,
        },
      },
      number: countObj.value?.innovate,
    },
  ])

  function jumpTo(path) {
    router.push(typeof path === 'string' ? { path } : path)
  }
</script>

<style lang="scss" scoped>
  .resource {
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    .content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      padding: 0 16px;
      font-size: 14px;

      .content_button {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 12px;
        border: 1px solid #fff;
        border-radius: 10px;
        background: linear-gradient(135deg, #eef8ff 0%, #fff 100%);

        .number {
          color: rgb(0 0 0 / 60%);
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
</style>
