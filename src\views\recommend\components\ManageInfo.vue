<template>
  <div class="manage-info">
    <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">对外投资</div>
      </div>
      <div class="right">
        <div
          class="count"
          @click="
            onItemClick({
              label: '对外投资',
              value: info.companyBusinessInfoVo?.investinfoCount,
            })
          "
          >{{ info.companyBusinessInfoVo?.investinfoCount || 0 }}</div
        >
        <div class="unit">起</div>
      </div>
    </div>
    <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">融资事件</div>
      </div>
      <div class="right">
        <div
          class="count"
          @click="
            onItemClick({
              label: '融资事件',
              value: info.companyBusinessInfoVo?.financingCount,
            })
          "
          >{{ info.companyBusinessInfoVo?.financingCount || 0 }}</div
        >
        <div class="unit">起</div>
      </div>
    </div>
    <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">资质证书</div>
      </div>
      <div class="right">
        <div
          class="count"
          @click="
            onItemClick({
              label: '资质证书',
              value: info.companyBusinessInfoVo?.certificateCount,
            })
          "
          >{{ info.companyBusinessInfoVo?.certificateCount || 0 }}</div
        >
        <div class="unit">项</div>
      </div>
    </div>
    <!-- <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">招聘信息</div>
      </div>
      <div class="right">
        <div class="count">3</div>
        <div class="unit">条</div>
      </div>
    </div>
    <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">供应商</div>
      </div>
      <div class="right">
        <div class="count">0</div>
        <div class="unit">家</div>
      </div>
    </div>
    <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">客户</div>
      </div>
      <div class="right">
        <div class="count">0</div>
        <div class="unit">家</div>
      </div>
    </div>
    <div class="item">
      <div class="title">
        <div class="deco"></div>
        <div class="text">招投标</div>
      </div>
      <div class="right">
        <div class="count">0</div>
        <div class="unit">家</div>
      </div>
    </div> -->
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  function onItemClick(item) {
    console.log(item)
    if (item.value && item.value != 0) {
      router.push({
        path: '/company-detail',
        query: {
          key: item.label,
          companyId: info.companyBasicInfoVo.companyId,
        },
      })
    }
  }
</script>

<style lang="scss" scoped>
  .manage-info {
    display: flex;
    flex-wrap: wrap;
    gap: 13px;
    margin-top: 16px;

    .item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 165px;
      height: 38px;
      padding: 8px;
      border-radius: 6px;
      background: rgb(18 85 228 / 6%);

      &:nth-child(3) {
        background: rgb(18 190 228 / 6%);
      }

      &:nth-child(4) {
        background: rgb(18 190 228 / 6%);
      }

      &:nth-child(7) {
        background: rgb(18 190 228 / 6%);
      }

      .title {
        display: flex;
        align-items: center;

        .deco {
          width: 4px;
          height: 4px;
          margin-right: 4px;
          border-radius: 50%;
          background: rgb(0 0 0 / 90%);
        }

        .text {
          color: rgb(0 0 0 / 90%);
          font-size: 14px;
        }
      }

      .right {
        display: flex;
        align-items: center;

        .count {
          margin-right: 3px;
          color: #1255e4;
          font-weight: 600;
          font-size: 14px;
        }

        .unit {
          color: rgb(0 0 0 / 60%);
          font-size: 14px;
        }
      }
    }
  }
</style>
