@use './custom-var.scss' as *;

@import '//at.alicdn.com/t/c/font_4810611_whw937sw0nj.css';
@import '//at.alicdn.com/t/c/font_4769267_kjggmn49ia.css';
// 主要的图标库
@import '//at.alicdn.com/t/c/font_4691501_duie7m4rxg.css';

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
}

html,
body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Helvetica,
    Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑',
    sans-serif;
}

// 图标样式适配 有安全边距
.iconfont-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1em;
  height: 1em;
}

button.active-button {
  transition: opacity 0.1s ease-in-out;
  &:active {
    opacity: 0.6;
  }
}
