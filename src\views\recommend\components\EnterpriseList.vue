<template>
  <div
    ref="listRef"
    class="list">
    <div
      v-for="companyItem in list"
      :key="companyItem.companyId"
      class="list_item"
      @click="
        (e) => {
          e.stopPropagation()
          emits('itemClick', companyItem)
        }
      ">
      <div class="list_item_header">
        <div class="list_item_title">
          <div class="pt-[6px] text-[16px] font-[600]"
            >{{ companyItem.companyName }}
          </div>
          <!-- <i
            class="iconfont icon-RightCircle text-[12px] text-[rgba(0,0,0,0.4)]" /> -->
          <!-- <div class="list_item_header_score">
            评分：{{ (+companyItem.score).toFixed(2) }}
          </div> -->
        </div>
        <div
          v-if="!isAll"
          class="flex items-center text-[12px] leading-[20px] text-[rgba(0,0,0,0.4)]">
          <div>{{ companyItem.region }}</div>
          <div class="mx-[8px] h-[10px] w-[1px] bg-[rgba(0,0,0,0.15)]" />
          <div>{{ companyItem.sxlNickname }}</div>
          <div class="mx-[8px] h-[10px] w-[1px] bg-[rgba(0,0,0,0.15)]" />
          <div>{{ companyItem.createdAt }}</div>
        </div>
        <div
          v-else
          class="text-[rgba(0,0,0,0.15) text-[12px] leading-[20px]">
          {{ companyItem.province }}
          {{
            `${companyItem.province === companyItem.city || !companyItem.city ? '' : `/ ${companyItem.city}`}`
          }}
          {{ ' / ' + companyItem.area }}
        </div>
      </div>
      <div class="list_item_content">
        <div
          class="items-center break-all rounded-[4px] bg-[rgba(0,0,0,0.02)] p-[4px] text-[12px] text-[rgba(0,0,0,0.45)]">
          <div class="flex justify-between">
            <img
              :src="tjlyImg"
              width="72"
              height="20"
              alt=""
              class="mr-[8px] inline-block" />
            <div
              v-if="isAssociation"
              class="local_button"
              @click="emits('association', companyItem)">
              本地关联
              <i class="iconfont icon-RightCircle text-[10px]"></i>
            </div>
          </div>
          <div class="mt-[4px] flex flex-wrap gap-[6px]">
            <div
              v-if="companyItem.financingCount"
              class="rounded-[3px] bg-[#FFF3E1] px-[8px] py-[2px] text-[12px] text-[#8C3C00]">
              融资{{ companyItem.financingCount }}次
            </div>
            <!-- <div
              v-if="+companyItem.wuhanNewsCountRecommend > 0"
              class="rounded-[3px] bg-[#FFF3E1] px-[8px] py-[2px] text-[12px] text-[#8C3C00]">
              近一年有与武汉相关的新闻
            </div> -->
            <div
              v-if="+companyItem.wuhanRecruitmentRecommend > 0"
              class="rounded-[3px] bg-[#FFF3E1] px-[8px] py-[2px] text-[12px] text-[#8C3C00]">
              武汉人才招聘信息{{ companyItem.wuhanRecruitmentRecommend }}条
            </div>
            <div
              v-if="+companyItem.wuhanBiddingCountRecommend > 0"
              class="rounded-[3px] bg-[#FFF3E1] px-[8px] py-[2px] text-[12px] text-[#8C3C00]">
              武汉招投标{{ companyItem.wuhanBiddingCountRecommend }}次
            </div>
          </div>

          <!--<span
            v-if="
              companyItem.reason?.investinfoCount &&
              +companyItem.reason?.investinfoCount != 0
            "
            class="reason_item break-all">
            对外投资{{ companyItem.reason?.investinfoCount }}次
            <span class="reason_split"> 、 </span>
          </span>
          <span
            v-if="
              companyItem.reason?.investinfoTotalAmount &&
              +companyItem.reason?.investinfoTotalAmount != 0
            "
            class="reason_item break-all">
            对外投资{{
              transformNumber(companyItem.reason?.investinfoTotalAmount, '万')
            }}元
            <span class="reason_split"> 、 </span>
          </span>
          <span
            v-if="
              companyItem.reason?.localWtbCount &&
              +companyItem.reason?.localWtbCount != 0
            "
            class="reason_item break-all">
            本地中标{{ companyItem.reason?.localWtbCount }}次
            <span class="reason_split"> 、 </span>
          </span>
          <span
            v-if="
              companyItem.reason?.localWtbTotalAmount &&
              +companyItem.reason?.localWtbTotalAmount != 0
            "
            class="reason_item break-all">
            中标金额达{{
              transformNumber(companyItem.reason?.localWtbTotalAmount)
            }}元
          </span> -->
          <!-- <span
              v-for="(tg, tgIdx) in companyItem.reason?.financingInfo"
              :key="tgIdx">
              {{ tg }}
              <span
                v-show="tgIdx !== companyItem.reason?.financingInfo.length - 1">
                、
              </span>
            </span> -->
        </div>
        <div
          v-show="companyItem.companyTags?.length"
          class="tag_box mt-[8px] flex items-center gap-x-[6px] overflow-x-auto">
          <div
            v-for="(tag, tagIdx) in companyItem.companyTags"
            :key="tagIdx"
            :style="{
              backgroundColor: colorList[tagIdx]?.bg || colorList.at(-1).bg,
              color: colorList[tagIdx]?.text || colorList.at(-1).text,
            }"
            class="flex-shrink-0 rounded-[3px] px-[8px] py-[2px] text-[12px]">
            {{ tag }}
          </div>
        </div>

        <div class="list_item_footer">
          <!-- 产业 -->
          <div class="types">
            <div
              v-for="(type, typeIdx) in handleIndustryList(
                companyItem.industrychainNameList,
              )"
              :key="typeIdx"
              class="flex-shrink-0">
              {{ type }}
            </div>
          </div>
          <div class="ml-[8px] flex flex-shrink-0 gap-x-[8px]">
            <template v-if="isEdit">
              <div
                class="item_handle"
                @click="
                  () => {
                    showPicker = true
                    _compony = companyItem
                  }
                ">
                转办
              </div>
              <Popup
                v-model:show="showPicker"
                position="bottom">
                <Cascader
                  v-model="areaPerson"
                  :options="areaOptionsList"
                  title="转办推送区域"
                  @change="onEditPickerChange"
                  @close="showPicker = false"
                  @finish="
                    ({ selectedOptions }) => {
                      onEdit(selectedOptions)
                    }
                  " />
              </Popup>
            </template>
            <div
              v-if="isAccept"
              class="item_handle"
              @click="emits('accept', companyItem)">
              承接
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-show="loading"
      class="mt-[16px] flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
      <Loading v-show="loading">加载中...</Loading>
    </div>
    <div
      v-show="!loading && list.length === 0"
      class="flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
      暂无数据
    </div>
  </div>
</template>

<script setup>
  import { getUserList } from '@/apis/recommend'
  import tjlyImg from '@/assets/images/recommend/tjly.png'
  import { useCloned, useInfiniteScroll, useScroll } from '@vueuse/core'
  import { Cascader, Loading, Popup } from 'vant'
  import { nextTick, onMounted, ref, watch } from 'vue'
  import { areaOptions, colorList } from '../options'

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    canLoad: {
      type: Boolean,
      default: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isAccept: {
      type: Boolean,
      default: false,
    },
    isAssociation: {
      type: Boolean,
      default: false,
    },
    isAll: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    scrollKey: {
      type: String,
      default: '',
    },
    defaultScroll: {
      type: Number,
      default: 0,
    },
    handleIndustryList: {
      type: Function,
      default: (list) => list,
    },
  })

  const emits = defineEmits([
    'edit',
    'accept',
    'down',
    'itemClick',
    'association',
    'scroll-y',
  ])
  const listRef = ref(null)
  const { cloned: areaOptionsList } = useCloned(areaOptions)
  const { y } = useScroll(listRef)

  watch(y, (nv) => {
    emits('scroll-y', nv)
  })

  onMounted(() => {
    nextTick(() => {
      listRef.value?.scrollTo({
        top: props.defaultScroll,
      })
    })
  })

  const areaPerson = ref('')
  const _compony = ref({})

  useInfiniteScroll(
    listRef,
    () => {
      emits('down')
    },
    {
      distance: 10,
      interval: 100,
      canLoadMore: () => props.canLoad,
    },
  )

  const showPicker = ref(false)

  async function onEditPickerChange({ value, tabIndex }) {
    if (tabIndex === 1) {
      showPicker.value = false
      return
    }
    const idx = areaOptionsList.value.findIndex((item) => item.value === value)
    const children = await getUserListByArea(areaOptionsList.value[idx].value)
    areaOptionsList.value[idx].children = children
  }

  function onEdit(value) {
    console.log('确认', value)

    if (value[1]) {
      showPicker.value = false
      emits('edit', {
        value: [value[0].value, value[1].value],
        item: _compony.value,
      })
      areaPerson.value = ''
    }
  }

  async function getUserListByArea(area) {
    try {
      const {
        data: { data: res },
      } = await getUserList({
        page: {
          pageNum: 1,
          pageSize: 9999999,
        },
        params: {
          bindCity: area,
          // operationPermissions: {
          //   type: 'merchants',
          // },
        },
      })
      return res.list.map((item) => {
        return {
          text: item.sxlNickname,
          value: item.bid,
          ...item,
        }
      })
    } catch (error) {
      console.log(error)
    }
  }
</script>

<style lang="scss" scoped>
  .item_handle {
    display: flex;
    align-items: center;
    padding: 2px 16px;
    border-radius: 9999px;
    background: linear-gradient(to left, #6093ff 0%, #1255e4 99%);
    color: #fff;
    font-size: 12px;
  }

  .list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #f7f8fa;

    .tag_box {
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .reason_item:last-child {
    .reason_split {
      display: none;
    }
  }

  .list_item {
    border-radius: 12px;
    background-color: #fff;

    &_header {
      position: relative;
      padding: 16px 16px 10px;
      border-bottom: 1px solid rgb(0 0 0 / 6%);

      .list_item_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &_score {
        position: absolute;
        top: 0;
        right: 0;
        padding: 4px 8px;
        border-radius: 0 12px;
        background-color: #1677ff;
        color: #e6f4ff;
        font-size: 12px;
      }
    }

    &_content {
      padding: 10px 16px 0;

      .local_button {
        color: #1677ff;
        font-size: 12px;
      }
    }

    &_footer {
      display: flex;
      align-items: center;
      height: 36px;
      margin: 10px -16px 0;
      padding: 0 16px;
      border-radius: 0 0 12px 12px;
      background: rgb(0 0 0 / 6%);
      font-size: 12px;

      .types {
        display: flex;
        flex: 1;
        gap: 0 8px;
        overflow-x: auto;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    & + .list_item {
      margin-top: 16px;
    }
  }
</style>
