<template>
  <div class="intellectual-property">
    <div class="item">
      <img
        alt=""
        src="../../../assets/images/recommend/intellectual-property/1.png" />
      <div class="text">
        <span>专利</span>
        <span
          class="count"
          @click="
            onItemClick({
              label: '专利',
              value: info.companyIntellectualPropertyVo?.patentCount,
            })
          "
          >({{ info.companyIntellectualPropertyVo?.patentCount || 0 }})</span
        >
      </div>
    </div>
    <div class="item">
      <img
        alt=""
        src="../../../assets/images/recommend/intellectual-property/2.png" />
      <div class="text">
        <span>软著</span>
        <span
          class="count"
          @click="
            onItemClick({
              label: '软著',
              value: info.companyIntellectualPropertyVo?.softwarecopyrightCount,
            })
          "
          >({{
            info.companyIntellectualPropertyVo?.softwarecopyrightCount || 0
          }})</span
        >
      </div>
    </div>
    <div class="item">
      <img
        alt=""
        src="../../../assets/images/recommend/intellectual-property/3.png" />
      <div class="text">
        <span>商标</span>
        <span
          class="count"
          @click="
            onItemClick({
              label: '商标',
              value: info.companyIntellectualPropertyVo?.tmCount,
            })
          "
          >({{ info.companyIntellectualPropertyVo?.tmCount || 0 }})</span
        >
      </div>
    </div>
    <!-- <div class="item">
      <img
        alt=""
        src="../../../assets/images/recommend/intellectual-property/4.png" />
      <div class="text">
        <span>备案网站</span>
        <span class="count">(0)</span>
      </div>
    </div>
    <div class="item">
      <img
        alt=""
        src="../../../assets/images/recommend/intellectual-property/5.png" />
      <div class="text">
        <span>公众号/小程序</span>
        <span class="count">(0)</span>
      </div>
    </div> -->
  </div>
</template>

<script setup>
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  function onItemClick(item) {
    console.log(item)
    if (item.value && item.value != 0) {
      router.push({
        path: '/company-detail',
        query: {
          key: item.label,
          companyId: info.companyBasicInfoVo.companyId,
        },
      })
    }
  }
</script>

<style lang="scss" scoped>
  .intellectual-property {
    display: flex;
    flex-wrap: wrap;

    .item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 114px;
      height: 61px;
      margin-top: 24px;

      img {
        margin-bottom: 6px;
      }

      .text {
        color: rgb(0 0 0 / 90%);
        font-size: 12px;

        .count {
          color: #1255e4;
          font-size: 14px;
        }
      }
    }
  }
</style>
