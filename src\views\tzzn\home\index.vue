<template>
  <div class="main_page">
    <div class="list">
      <div
        v-for="item in list"
        :key="item.path"
        class="item"
        @click="navigate(item.path)">
        <img
          :src="item.img"
          alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import fzys from '@/assets/images/tzzn/home/<USER>'
import lxwm from '@/assets/images/tzzn/home/<USER>'
import tzpt from '@/assets/images/tzzn/home/<USER>'
import xfyj from '@/assets/images/tzzn/home/<USER>'
import yxwh from '@/assets/images/tzzn/home/<USER>'
import zdcy from '@/assets/images/tzzn/home/<USER>'
import { useRouter } from 'vue-router'

const router = useRouter()

const list = [
  { path: 'yxwh', img: yxwh },
  { path: 'fzys', img: fzys },
  { path: 'zdcy', img: zdcy },
  { path: 'tzpt', img: tzpt },
  { path: 'xfyj', img: xfyj },
  { path: 'lxwm', img: lxwm },
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    gap: 16px;

    .item {
      width: 163px;
      height: 190px;
    }
  }
}
</style>
