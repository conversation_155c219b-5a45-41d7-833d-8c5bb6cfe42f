<!-- 产业链链长制 -->
<template>
  <div class="container">
    <div class="h-[330px] w-full">
      <img
        class="h-full w-full"
        :src="currentImage"
        @click="show = true" />
    </div>
    <div class="bottom">
      <div class="text">
        <div class="region_introduce">
          <div class="title">牵头单位：</div>
          <div class="introduce">{{
            currentData.data.cyllzz.danwei.split('：')[1] || '暂无数据'
          }}</div>
        </div>
        <div class="region_introduce">
          <div class="title">重点任务：</div>
          <div class="introduce">{{
            currentData.data.cyllzz.content || '暂无数据'
          }}</div>
        </div>
        <div class="region_introduce">
          <div class="title">联系方式：</div>
          <div class="introduce">{{
            currentData.data.cyllzz.dianhua.split('：')[1] || '暂无数据'
          }}</div>
        </div>
      </div>
    </div>
  </div>
  <ImagePreview
    v-model:show="show"
    :images="[currentImage]"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import allData from '@/assets/js/cyjs'
  import { extractFileName } from '@/utils/index'
  import { ImagePreview } from 'vant'
  import { computed, inject, ref } from 'vue'
  const currentType = inject('currentType')
  const currentData = computed(() => {
    return Object.values(allData).find((item) => {
      return item.data.cyfl.includes(currentType.value)
    })
  })
  const currentImage = computed(() => {
    const imagesPath = currentData.value.data['ghtimg']
    if (!imagesPath) return undefined
    const _thisImagesName = extractFileName(imagesPath)

    return new URL(`./images/${_thisImagesName}`, import.meta.url).href
  })
  const show = ref(false)
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 10px;
    background-color: #f7f8fa;

    .bottom {
      width: 100%;
      padding: 16px;

      .box {
        width: 100%;
        height: 226px;
        background-color: #fff;
      }

      .text {
        padding: 16px;
        background-color: #fff;

        .region_introduce {
          display: flex;
          gap: 10px;
          margin-top: 8px;
          font-size: 14px;

          .title {
            min-width: 80px;
            color: rgb(0 0 0 / 60%);
          }

          .introduce {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            color: rgb(0 0 0 / 40%);
          }
        }
      }
    }
  }
</style>
