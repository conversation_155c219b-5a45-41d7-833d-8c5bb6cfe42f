/**
 * 从URL中提取文件名
 * @param {URL} url 
 * @returns {string} 文件名
 * @example const url = 'https://oss.youpin-k8s.net/shared-space/2025-06-05/fed167d79e1b48fdb432dbc6f5829301/面试题.pdf';
    console.log(getFileNameFromUrl(url)); // 输出："面试题.pdf"
 */
export function getFileNameFromUrl(url) {
  // 处理URL中的路径部分
  let pathname
  try {
    // 如果是完整URL，使用URL对象解析
    const urlObj = new URL(url)
    pathname = urlObj.pathname
  } catch {
    // 如果不是完整URL，直接使用原始字符串
    pathname = url.split('?')[0].split('#')[0]
  }

  const filename = pathname.split('/').pop()

  return filename ? decodeURIComponent(filename) : '文件'
}
