<!-- 产业空间布局 -->
<template>
  <div class="container">
    <div class="h-[330px] w-full">
      <img
        class="h-full w-full"
        :src="currentImage"
        @click="show = true" />
    </div>
    <div
      v-if="currentData.data_cykjbj"
      class="bottom">
      <div class="table">
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th
                  v-for="item in currentData.data_cykjbj.headers"
                  :key="item"
                  class="min-w-[100px]"
                  >{{ item }}</th
                >
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in currentData.data_cykjbj.rows"
                :key="index">
                <td
                  v-for="text in Object.values(item)"
                  :key="text">
                  {{ text }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <ImagePreview
    v-model:show="show"
    :images="[currentImage]"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import allData from '@/assets/js/cyjs'
  import { extractFileName } from '@/utils/index'
  import { ImagePreview } from 'vant'
  import { computed, inject, ref } from 'vue'
  import 'vue3-h5-table/dist/style.css'
  const currentType = inject('currentType')
  const currentData = computed(() => {
    return Object.values(allData).find((item) => {
      return item.data.cyfl.includes(currentType.value)
    })
  })
  const currentImage = computed(() => {
    const imagesPath = currentData.value.data['ghtimg']
    if (!imagesPath) return undefined
    const _thisImagesName = extractFileName(imagesPath)

    return new URL(`./images/${_thisImagesName}`, import.meta.url).href
  })
  const show = ref(false)
  // const datas = computed(() => {
  //   return []
  // })
  // const column = computed(() => {
  //   return []
  // })
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 10px;
    background-color: #f7f8fa;

    .bottom {
      overflow-x: scroll;
      width: 100%;
      padding: 16px;

      .table {
        width: 100%;
        height: 200px;
        background-color: #fff;
      }

      .table-container {
        overflow-x: auto; /* 允许横向滚动 */
      }

      table {
        width: 800px; /* 表格宽度设置为100%，使其充满容器 */
        border-collapse: collapse;
        table-layout: auto; /* 自动布局，允许内容决定列宽 */
      }

      th,
      td {
        padding: 8px;
        border: 1px solid #ddd;
        text-align: left;
        white-space: normal; /* 允许内容换行 */
      }

      th {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f2f2f2;
      }
    }
  }
</style>
