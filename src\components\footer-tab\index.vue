<template>
  <div class="footer_tab">
    <div
      class="item"
      @click="jumpTo('首页')">
      <img
        class="image"
        src="@/assets/images/footer_tab/home.png"
        alt="" />
      <p>首页</p>
    </div>
    <div
      class="item"
      @click="jumpTo('消息')">
      <img
        class="image"
        src="@/assets/images/footer_tab/message.png"
        alt="" />
      <p>消息</p></div
    >

    <div
      class="item"
      @click="jumpTo('事项')">
      <Badge
        :content="total || 0"
        :show-zero="false">
        <img
          class="image"
          src="@/assets/images/footer_tab/do.png"
          alt="" />
        <p>事项</p>
      </Badge>
    </div>

    <div
      class="item"
      @click="jumpTo('我的')">
      <img
        class="image"
        src="@/assets/images/footer_tab/me.png"
        alt="" />
      <p>我的</p>
    </div>
  </div>
</template>

<script setup>
  import axios from 'axios'
  import { Badge } from 'vant'
  import { ref, watchEffect } from 'vue'
  import { useRoute } from 'vue-router'
  import wx from 'weixin-js-sdk'

  const route = useRoute()

  const pageRouterUrl = {
    首页: '/pages/home/<USER>',
    消息: '/pages/message/message',
    事项: '/pages/to-do-list/index/index',
    我的: '/pages/my/my',
  }

  function jumpTo(key) {
    try {
      wx.miniProgram.switchTab({
        url: pageRouterUrl[key],
      })
    } catch (error) {
      console.log(error)
    }
  }

  const total = ref(0)

  async function getPoint() {
    try {
      const {
        data: { data: res },
      } = await axios.post(
        'https://xmqlc.whcftzcj.com:8001/admin-api/project/matter/page',
        {
          pageOn: 1,
          pageSize: 10,
        },
        {
          headers: {
            Authorization: `Bearer ${route.query.token}`,
          },
        },
      )
      total.value = res.total || 0
    } catch (error) {
      console.log(error)
    }
  }

  watchEffect(() => {
    if (route.query.token) {
      getPoint()
    }
  })
</script>

<style lang="scss" scoped>
  .footer_tab {
    // position: fixed;
    // bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 56px;
    padding: 8px;
    background-color: #fff;

    .item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 84px;
      height: 100%;
      color: rgb(0 0 0 / 60%);
      font-weight: 400;
      font-size: 10px;

      .image {
        width: 20px;
        height: 20px;
      }
    }
  }
</style>
