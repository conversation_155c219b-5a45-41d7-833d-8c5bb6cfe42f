<template>
  <div
    ref="listRef"
    class="list">
    <div
      v-for="companyItem in list"
      :key="companyItem.companyId"
      class="list_item"
      @click="
        (e) => {
          e.stopPropagation()
          emits('itemClick', companyItem)
        }
      ">
      <div class="list_item_header">
        <div class="list_item_title">
          <div class="text-[16px] font-[600]">
            {{ companyItem.companyName }}
          </div>
        </div>
        <div class="mt-[8px] flex items-center">
          <div class="text-[rgba(0,0,0,0.15) text-[12px] leading-[20px]">
            {{ companyItem.districtAdd }}
          </div>
          <div
            v-show="companyItem.companyTags.length"
            class="line" />
          <div
            v-show="companyItem.companyTags.length"
            class="tag_box flex flex-1 items-center gap-x-[6px] overflow-x-auto">
            <div
              v-for="(tag, tagIdx) in companyItem.companyTags"
              :key="tagIdx"
              :style="{
                backgroundColor: colorList[tagIdx]?.bg || colorList.at(-1).bg,
                color: colorList[tagIdx]?.text || colorList.at(-1).text,
              }"
              class="flex-shrink-0 rounded-[3px] px-[8px] py-[2px] text-[12px]">
              {{ tag }}
            </div>
          </div>
        </div>
      </div>
      <div class="list_item_content">
        <div class="list_item_footer">
          <!-- 产业 -->
          <div class="types">
            <div
              v-for="(type, typeIdx) in handleIndustryList(
                companyItem.industrychainNameList,
              )"
              :key="typeIdx"
              class="flex-shrink-0">
              {{ type }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      v-show="loading"
      class="mt-[16px] flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
      <Loading v-show="loading">加载中...</Loading>
    </div>
    <div
      v-show="!loading && list.length === 0"
      class="flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
      暂无数据
    </div>
  </div>
</template>

<script setup>
  import { useInfiniteScroll, useScroll } from '@vueuse/core'
  import { Loading } from 'vant'
  import { nextTick, onMounted, ref, watch } from 'vue'
  import { colorList } from '../options'

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    canLoad: {
      type: Boolean,
      default: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isAccept: {
      type: Boolean,
      default: false,
    },
    isAssociation: {
      type: Boolean,
      default: false,
    },
    isAll: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    scrollKey: {
      type: String,
      default: '',
    },
    defaultScroll: {
      type: Number,
      default: 0,
    },
    handleIndustryList: {
      type: Function,
      default: (list) => list,
    },
  })

  const emits = defineEmits([
    'edit',
    'accept',
    'down',
    'itemClick',
    'association',
    'scroll-y',
  ])
  const listRef = ref(null)

  const { y } = useScroll(listRef)

  watch(y, (nv) => {
    emits('scroll-y', nv)
  })

  onMounted(() => {
    nextTick(() => {
      listRef.value?.scrollTo({
        top: props.defaultScroll,
      })
    })
  })

  useInfiniteScroll(
    listRef,
    () => {
      emits('down')
    },
    {
      distance: 10,
      interval: 100,
      canLoadMore: () => props.canLoad,
    },
  )
</script>

<style lang="scss" scoped>
  .item_handle {
    display: flex;
    align-items: center;
    padding: 2px 16px;
    border-radius: 9999px;
    background: linear-gradient(to left, #6093ff 0%, #1255e4 99%);
    color: #fff;
    font-size: 12px;
  }

  .list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    background-color: #f7f8fa;

    .line {
      width: 1px;
      height: 10px;
      margin: 0 8px;
      border-radius: 1px;
      background: rgb(0 0 0 / 15%);
    }

    .tag_box {
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .reason_item:last-child {
    .reason_split {
      display: none;
    }
  }

  .list_item {
    border-radius: 12px;
    background-color: #fff;

    &_header {
      position: relative;
      padding: 10px 16px;
      border-bottom: 1px solid rgb(0 0 0 / 6%);

      .list_item_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      &_score {
        position: absolute;
        top: 0;
        right: 0;
        padding: 4px 8px;
        border-radius: 0 12px;
        background-color: #1677ff;
        color: #e6f4ff;
        font-size: 12px;
      }
    }

    &_content {
      padding: 0 16px;

      .local_button {
        color: #1677ff;
        font-size: 12px;
      }
    }

    &_footer {
      display: flex;
      align-items: center;
      height: 36px;
      margin: 0 -16px;
      padding: 0 16px;
      border-radius: 0 0 12px 12px;
      background: rgb(0 0 0 / 6%);
      font-size: 12px;

      .types {
        display: flex;
        flex: 1;
        gap: 0 8px;
        overflow-x: auto;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    & + .list_item {
      margin-top: 16px;
    }
  }
</style>
