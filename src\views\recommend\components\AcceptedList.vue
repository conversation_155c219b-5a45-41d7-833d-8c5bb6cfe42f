<template>
  <div class="container">
    <FilterSelect
      :show-area="auth === 'coordination'"
      :init-data="recommendStore.acceptInfo.filterData"
      @search="onFilter" />
    <EnterpriseList
      :list="recommendStore.acceptInfo.list"
      :loading
      :default-scroll="recommendStore.acceptInfo.scroll"
      :can-load="
        !loading &&
        recommendStore.acceptInfo.list.length <
          recommendStore.acceptInfo.pagination.total
      "
      @down="onLoadData"
      @item-click="onItemClick"
      @scroll-y="onListScroll" />
  </div>
</template>

<script setup>
  import { getRecommendList } from '@/apis/recommend'
  import { useRecommendStore } from '@/stores/recommend'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { initParams, listInfoHandle } from '../options'
  import EnterpriseList from './EnterpriseList.vue'
  import FilterSelect from './FilterSelect/index.vue'

  const router = useRouter()
  const route = useRoute()
  const recommendStore = useRecommendStore()

  const loading = ref(false)
  const userInfo = ref({})

  const auth = computed(() => {
    return userInfo.value?.operationPermissions?.type
  })

  function onListScroll(y) {
    recommendStore.acceptInfo.scroll = y
  }

  async function getEnterpriseList() {
    if (!auth.value) {
      return
    }
    try {
      loading.value = true
      // 判断用户是否为招商员
      const isMerchants = auth.value === 'merchants'
      const params = initParams(
        recommendStore.acceptInfo.filterData,
        isMerchants ? userInfo.value.bid : undefined,
        true,
        {
          pageNum: recommendStore.acceptInfo.pagination.pageNum,
          pageSize: recommendStore.acceptInfo.pagination.pageSize,
        },
      )
      const {
        data: { data: res },
      } = await getRecommendList(params)
      recommendStore.acceptInfo.list.push(...listInfoHandle(res.list))
      recommendStore.acceptInfo.pagination.total = res.total
      recommendStore.acceptInfo.pagination.pageNum = res.page + 1
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    userInfo.value = await recommendStore.getRecommendUserInfo(
      route.query.userName,
    )
    if (recommendStore.acceptInfo.list.length === 0) {
      getEnterpriseList()
    }
  })

  function onLoadData() {
    getEnterpriseList()
  }

  function onFilter(data) {
    recommendStore.acceptInfo.list = []
    recommendStore.acceptInfo.pagination.pageNum = 1
    recommendStore.acceptInfo.pagination.total = 0
    recommendStore.acceptInfo.filterData = {
      similarCompanyName: data.keyword.value,
      region: data.area.value,
      before: data.times.value.end.join('-'),
      after: data.times.value.start.join('-'),
    }
    recommendStore.acceptInfo.scroll = 0
    getEnterpriseList()
  }

  function onItemClick(item) {
    console.log('跳转', item)
    router.push({
      path: '/recommend-detail',
      query: {
        companyId: item.companyId,
        token: route.query.token,
        userName: route.query.userName,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px - 44px - 44px);

    .filter_select {
      display: flex;
      align-items: center;
      height: 48px;

      .filter_item {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        width: 180px;
        height: 100%;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
