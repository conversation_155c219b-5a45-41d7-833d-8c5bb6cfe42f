<template>
  <div class="project_detail">
    <Loading v-show="loading">加载中...</Loading>
    <!-- banner -->
    <div class="banner">
      <img
        src="@/assets/images/cygk/project-detail.png"
        alt=""
        class="banner_image" />
    </div>
    <div class="content">
      <div class="park_title card">
        <div class="park_title_text">{{ pageData.name }}</div>
        <div class="park_title_tag">{{ pageData.district }}</div>
      </div>

      <div class="park_info card">
        <div class="park_info_item">
          <div class="park_info_item_label">具体地点：</div>
          <div class="park_info_item_text">
            {{ pageData.address }}
          </div>
        </div>
        <div class="park_info_item">
          <div class="park_info_item_label">投资金额：</div>
          <div class="park_info_item_text"> {{ pageData.amount }} </div>
        </div>
        <div class="park_info_item">
          <div class="park_info_item_label">投资方式：</div>
          <div class="park_info_item_text"> {{ pageData.method }} </div>
        </div>
        <div class="park_info_item">
          <div class="park_info_item_label">联系方式：</div>
          <div class="park_info_item_text">
            {{ pageData.phonename }} {{ pageData.phone }}
          </div>
        </div>
      </div>

      <div class="park_info card">
        <div class="park_info_item">
          <div class="park_info_item_label">产业方向：</div>
          <div class="park_info_item_text">
            {{ pageData.positioning }}
          </div>
        </div>
        <div class="park_info_item">
          <div class="park_info_item_label">投资方向：</div>
          <div class="park_info_item_text">
            {{ pageData.synopsis }}
          </div>
        </div>
        <div class="park_info_item">
          <div class="park_info_item_label">配套条件：</div>
          <div class="park_info_item_text">
            <p
              v-for="(item, index) in pageData.conditions?.split('\n') || []"
              :key="index">
              {{ item }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Loading } from 'vant'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'

import { getKeyDetail } from '@/apis'

const route = useRoute()
const { id } = route.query

const pageData = ref({})
const loading = ref(false)

async function getPageData() {
  try {
    loading.value = true
    const {
      data: { data: res },
    } = await getKeyDetail('projects', {
      id,
    })
    pageData.value = res
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getPageData()
})
</script>

<style lang="scss" scoped>
.project_detail {
  background: #f7f8fa;

  .content {
    padding: 0 16px 8px;

    .card {
      box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.05);
      border-radius: 12px;
      padding: 16px;
    }

    .park_title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #1255e4;
      color: #fff;

      .park_title_text {
        font-weight: 600;
        font-size: 18px;
        line-height: 26px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }

      .park_title_tag {
        font-size: 12px;
        color: #ffffff;
        line-height: 16px;
        padding: 1px 4px;
        border-radius: 2px;
        background: rgba(255, 255, 255, 0.1);
        flex-shrink: 0;
      }
    }

    .park_info {
      margin-top: 16px;
      background: #ffffff;

      .park_info_item {
        display: flex;

        .park_info_item_label {
          flex-shrink: 0;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
          line-height: 22px;
        }

        .park_info_item_text {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          line-height: 22px;
        }

        & + .park_info_item {
          margin-top: 6px;
        }
      }
    }
  }
}
</style>
