import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_API_BASE_PREFIX),
  routes: [
    {
      path: '/',
      component: () => import('../views/index/index.vue'),
      meta: {
        name: '产业基础',
      },
    },
    {
      path: '/cygk',
      name: 'cygk',
      redirect: '/cygk/home',
      children: [
        // 产业概况-首页
        {
          path: 'home',
          component: () => import('../views/cygk/home/<USER>'),
          meta: {
            name: '产业概况',
          },
        },
        /**
         * 策划项目
         */
        {
          path: 'project-list',
          component: () =>
            import('@/views/cygk/attraction-project/list/index.vue'),
          meta: {
            name: '策划项目',
          },
        },
        {
          path: 'project-detail',
          component: () =>
            import('@/views/cygk/attraction-project/detail/index.vue'),
          meta: {
            name: '项目详情',
          },
        },
        /**
         * 产业资源
         */
        {
          path: 'resource-list',
          component: () =>
            import('@/views/cygk/industry-resource/list/index.vue'),
          meta: {
            name: '产业资源',
          },
        },
        {
          path: 'resource-detail',
          component: () =>
            import('@/views/cygk/industry-resource/detail/index.vue'),
          meta: {
            name: '资源详情',
          },
        },
        /**
         * 产业主体
         */
        {
          path: 'main-body',
          component: () => import('@/views/cygk/main-body/list/index.vue'),
          meta: {
            name: '产业主体',
          },
        },
        {
          name: 'main-body-detail',
          path: 'main-body/detail',
          component: () => import('@/views/cygk/main-body/detail/index.vue'),
          meta: {
            name: '企业详情',
          },
        },
        /**
         * 产业政策
         */
        {
          path: 'policy-list',
          component: () =>
            import('@/views/cygk/industrial-policy/list/index.vue'),
          meta: {
            name: '产业政策',
          },
        },
        {
          path: 'policy-detail',
          component: () =>
            import('@/views/cygk/industrial-policy/detail/index.vue'),
          meta: {
            name: '政策详情',
          },
        },
        {
          path: 'policy-full-text',
          component: () =>
            import('@/views/cygk/industrial-policy/full-text/index.vue'),
          meta: {
            name: '政策全文',
          },
        },
        // 产业概况
        {
          path: 'industry',
          component: () => import('../views/cygk/industry/index.vue'),
          meta: {
            name: '产业概况',
          },
        },
        // 重点区域
        {
          path: 'key-areas',
          component: () => import('../views/cygk/industry/key-areas/index.vue'),
        },
        {
          path: 'qyfx',
          component: () => import('../views/cygk/industry/qyfx/index.vue'),
          meta: {
            name: '区域分析',
          },
        },
        {
          path: 'industry-introduction',
          component: () =>
            import('../views/cygk/industry/industry-introduction/index.vue'),
          meta: {
            name: '重点产业',
          },
        },
        {
          path: 'introduce',
          component: () => import('../views/cygk/introduce/introduce-home.vue'),
          meta: {
            name: '产业载体',
          },
        },
        // 园区介绍
        {
          path: 'introduce-info',
          component: () => import('../views/cygk/introduce/index.vue'),
        },
        // 园区介绍-详情
        {
          path: 'introduce-detail',
          component: () => import('../views/cygk/introduce/detail/index.vue'),
        },
        // 创新资源
        {
          path: 'innovation-resources',
          component: () =>
            import('@/views/cygk/innovation-resources/index.vue'),
          meta: {
            name: '创新资源',
          },
        },
      ],
    },
    {
      path: '/tzzn',
      name: 'tzzn',
      redirect: '/tzzn/home',
      children: [
        {
          path: 'home',
          component: () => import('../views/tzzn/home/<USER>'),
          meta: {
            name: '投资指南',
          },
        },
        {
          path: 'yxwh',
          component: () => import('../views/tzzn/home/<USER>/index.vue'),
          meta: {
            name: '印象武汉',
          },
        },
        {
          path: 'fzys',
          component: () => import('../views/tzzn/home/<USER>/index.vue'),
          meta: {
            name: '发展优势',
          },
        },
        {
          path: 'zdcy',
          component: () => import('../views/tzzn/home/<USER>/index.vue'),
          meta: {
            name: '重点产业',
          },
        },
        {
          path: 'tzpt',
          component: () => import('../views/tzzn/home/<USER>/index.vue'),
          meta: {
            name: '投资平台',
          },
        },
        {
          path: 'xfyj',
          component: () => import('../views/tzzn/home/<USER>/index.vue'),
          meta: {
            name: '幸福宜居',
          },
        },
        {
          path: 'lxwm',
          component: () => import('../views/tzzn/home/<USER>/index.vue'),
          meta: {
            name: '联系我们',
          },
        },
        {
          path: 'jydj',
          component: () => import('../views/tzzn/home/<USER>/jydj/index.vue'),
          meta: {
            name: '机遇叠加',
          },
        },
        {
          path: 'hlbf',
          component: () => import('../views/tzzn/home/<USER>/hlbf/index.vue'),
          meta: {
            name: '活力迸发',
          },
        },
        {
          path: 'cxyd',
          component: () => import('../views/tzzn/home/<USER>/cxyd/index.vue'),
          meta: {
            name: '创新涌动',
          },
        },
        {
          path: 'zzgd',
          component: () => import('../views/tzzn/home/<USER>/zzgd/index.vue'),
          meta: {
            name: '智创高地',
          },
        },
        {
          path: 'cdqq',
          component: () => import('../views/tzzn/home/<USER>/cdqq/index.vue'),
          meta: {
            name: '畅达全球',
          },
        },
        {
          path: 'jjgx',
          component: () => import('../views/tzzn/home/<USER>/jjgx/index.vue'),
          meta: {
            name: '经济高效',
          },
        },
        {
          path: 'qymp',
          component: () => import('../views/tzzn/home/<USER>/qymp/index.vue'),
          meta: {
            name: '企业名片',
          },
        },
        {
          path: 'cytx',
          component: () => import('../views/tzzn/home/<USER>/cytx/index.vue'),
          meta: {
            name: '产业体系',
          },
        },
        {
          path: 'cygj',
          component: () => import('../views/tzzn/home/<USER>/cygj/index.vue'),
          meta: {
            name: '产业格局',
          },
        },
        {
          path: 'cydt',
          component: () => import('../views/tzzn/home/<USER>/cydt/index.vue'),
          meta: {
            name: '产业地图',
          },
        },
        {
          path: 'whxc',
          component: () => import('../views/tzzn/home/<USER>/whxc/index.vue'),
          meta: {
            name: '武汉新城',
          },
        },
        {
          path: 'cjxq',
          component: () => import('../views/tzzn/home/<USER>/cjxq/index.vue'),
          meta: {
            name: '长江新区',
          },
        },
        {
          path: 'jsxc',
          component: () => import('../views/tzzn/home/<USER>/jsxc/index.vue'),
          meta: {
            name: '军山新城',
          },
        },
        {
          path: 'zmq',
          component: () => import('../views/tzzn/home/<USER>/zmq/index.vue'),
          meta: {
            name: '自贸区',
          },
        },
        {
          path: 'zbq',
          component: () => import('../views/tzzn/home/<USER>/zbq/index.vue'),
          meta: {
            name: '综保区',
          },
        },
        {
          path: 'tscyyq',
          component: () => import('../views/tzzn/home/<USER>/tscyyq/index.vue'),
          meta: {
            name: '特色产业园区',
          },
        },
        {
          path: 'dh',
          component: () => import('../views/tzzn/home/<USER>/zbq/dh/index.vue'),
          meta: {
            name: '东湖综合保税区',
          },
        },
        {
          path: 'whxg',
          component: () => import('../views/tzzn/home/<USER>/zbq/whxg/index.vue'),
          meta: {
            name: '武汉新港空港综合保税区',
          },
        },
        {
          path: 'whjk',
          component: () => import('../views/tzzn/home/<USER>/zbq/whjk/index.vue'),
          meta: {
            name: '武汉经开综合保税区',
          },
        },

        {
          path: 'zdgj',
          component: () =>
            import('../views/tzzn/home/<USER>/tscyyq/zdgj/index.vue'),
          meta: {
            name: '中德国际产业园',
          },
        },
        {
          path: 'zfwh',
          component: () =>
            import('../views/tzzn/home/<USER>/tscyyq/zfwhst/index.vue'),
          meta: {
            name: '中法武汉生态示范城',
          },
        },
        {
          path: 'gjwl',
          component: () =>
            import('../views/tzzn/home/<USER>/tscyyq/gjwlaq/index.vue'),
          meta: {
            name: '国家网络安全人才与创新基地',
          },
        },
        {
          path: 'whwl',
          component: () =>
            import('../views/tzzn/home/<USER>/tscyyq/whwlc/index.vue'),
          meta: {
            name: '武汉未来科技城',
          },
        },
        {
          path: 'ggdz',
          component: () =>
            import('../views/tzzn/home/<USER>/tscyyq/gggdz/index.vue'),
          meta: {
            name: '光谷光电子信息产业园',
          },
        },
        {
          path: 'sdlc',
          component: () => import('../views/tzzn/home/<USER>/sdlc/index.vue'),
          meta: {
            name: '千万级人口湿地绿城',
          },
        },
        {
          path: 'zjxfg',
          component: () => import('../views/tzzn/home/<USER>/zjxfg/index.vue'),
          meta: {
            name: '最具幸福感都市',
          },
        },
      ],
    },
    {
      path: '/recommend',
      component: () => import('@/views/recommend/index.vue'),
      meta: {
        name: '优企推荐',
      },
    },
    {
      path: '/recommend-all',
      component: () => import('@/views/recommend/all-list/index.vue'),
      meta: {
        name: '优企推荐',
      },
    },
    {
      path: '/recommend-detail',
      component: () => import('@/views/recommend/recommend-detail.vue'),
      meta: {
        name: '企业详情',
      },
    },
    {
      path: '/company-detail',
      component: () => import('@/views/recommend/company-detail/index.vue'),
      // meta: {
      //   name: '企业画像详情',
      // },
    },
    {
      path: '/connection',
      component: () => import('@/views/connection/LocalConnection.vue'),
      meta: {
        name: '关联信息',
      },
    },
    {
      path: '/local-connection',
      component: () => import('@/views/connection/index.vue'),
      meta: {
        name: '本地关联',
      },
    },
    // 政务端-精准招商
    {
      path: '/precise',
      component: () => import('@/views/precise/index.vue'),
      redirect: '/precise/recommend',
      children: [
        {
          // 目标企业
          path: 'target',
          component: () => import('@/views/precise/target-enp/index.vue'),
          meta: {
            name: '我的收藏',
          },
        },
        {
          // 优企推荐
          path: 'recommend',
          component: () => import('@/views/precise/recommend/index.vue'),
          meta: {
            name: '目标企业',
          },
        },
        {
          // 推送企业
          path: 'push',
          component: () => import('@/views/precise/push/index.vue'),
          meta: {
            name: '推送企业',
          },
        },
        {
          // 企业查询
          path: 'search',
          component: () =>
            import('@/views/precise/enterprise-search/index.vue'),
          meta: {
            name: '企业查询',
          },
        },
        {
          // 招商图谱
          path: 'investment-map',
          component: () => import('@/views/precise/investment-map/index.vue'),
          meta: {
            name: '招商图谱',
          },
        },
        {
          // 落地方案
          path: 'scheme',
          component: () => import('@/views/precise/scheme/index.vue'),
          meta: {
            name: '招商方案',
          },
        },
        {
          path: 'scheme-preview',
          component: () => import('@/views/precise/scheme/scheme-detail.vue'),
          meta: {
            name: '方案预览',
          },
        },
        // 招商资源
        {
          path: 'resource',
          component: () => import('@/views/precise/resource/index.vue'),
          meta: {
            name: '招商资源',
          },
        },
        // 创新资源
        {
          path: 'innovate',
          component: () =>
            import('@/views/precise/resource/InnovateResource.vue'),
          meta: {
            name: '创新资源',
          },
        },
        // 分区统计
        {
          path: 'statistics',
          component: () =>
            import('@/views/precise/resource/RegionalStatistics.vue'),
          meta: {
            name: '分区统计',
          },
        },
        {
          // 添加、编辑企业
          path: 'addOrEdit',
          component: () => import('@/views/precise/add/index.vue'),
        },
        {
          path: 'addEnt',
          component: () => import('@/views/precise/add/index_.vue'),
        },
        {
          path: 'history/:id',
          component: () => import('@/views/precise/add/HistoryList.vue'),
          mate: {
            name: '操作记录',
          },
        },
        {
          path: 'record',
          component: () => import('@/views/precise/add/RecordInfo.vue'),
        },
        {
          // 专家库
          path: 'except',
          component: () => import('@/views/precise/lib/expert/index.vue'),
          meta: {
            name: '研判专家库',
          },
        },
        {
          // 专家库
          path: 'except-detail',
          component: () =>
            import('@/views/precise/lib/expert/expert-detail.vue'),
          meta: {
            name: '研判专家详情',
          },
        },
        {
          // 机构联络库
          path: 'institution',
          component: () => import('@/views/precise/lib/institution/index.vue'),
          meta: {
            name: '机构联络库',
          },
        },
        {
          // 机构联络库详情
          path: 'institution-detail',
          component: () =>
            import('@/views/precise/lib/institution/institution-detail.vue'),
          meta: {
            name: '机构联络库详情',
          },
        },
      ],
    },
    // 新企业端
    {
      path: '/ent-side',
      component: () => import('@/views/enterprise-side-new/index.vue'),
      redirect: '/ent-side/home',
      children: [
        {
          path: 'home',
          component: () => import('@/views/enterprise-side-new/home/<USER>'),
          meta: {
            name: '武汉投资促进平台（企业端）',
          },
        },
        {
          // 印象武汉
          path: 'impression',
          component: () =>
            import('@/views/enterprise-side-new/wuhanImpression/index.vue'),
          meta: {
            name: '印象武汉',
          },
        },
        {
          // 武汉概况01
          path: 'overview_1',
          component: () =>
            import('@/views/enterprise-side-new/wuhanOverview/index_1.vue'),
          meta: {
            name: '武汉概况',
          },
        },
        {
          // 区位交通
          path: 'overview_2',
          component: () =>
            import('@/views/enterprise-side-new/wuhanOverview/index_2.vue'),
          meta: {
            name: '区位交通',
          },
        },
        {
          // 操作指引
          path: 'guide',
          component: () =>
            import('@/views/enterprise-side-new/guide/index.vue'),
          meta: {
            name: '操作指引',
          },
        },
        {
          // 投资机会
          path: 'opportunity',
          component: () =>
            import('@/views/enterprise-side-new/opportunity/index.vue'),
          meta: {
            name: '投资机会',
          },
        },
        {
          // 咨询
          path: 'consulting',
          component: () =>
            import('@/views/enterprise-side-new/consulting/index.vue'),
          meta: {
            name: '我要咨询',
          },
        },
      ],
    },
    // 企业端
    {
      path: '/enterprise-side',
      component: () => import('@/views/enterprise-side/index.vue'),
      redirect: '/enterprise-side/home',
      children: [
        {
          path: 'home',
          component: () => import('@/views/enterprise-side/home/<USER>'),
          meta: {
            name: '首页',
          },
        },
        {
          path: 'policy',
          component: () =>
            import('@/views/enterprise-side/policy/list/index.vue'),
          meta: {
            name: '查政策',
          },
        },
        {
          path: 'ply',
          component: () =>
            import('@/views/enterprise-side/policy/list/index.vue'),
          meta: {
            name: '投资政策',
          },
        },
        {
          path: 'policy-detail',
          component: () =>
            import('@/views/enterprise-side/policy/detail/index.vue'),
          meta: {
            name: '政策详情',
          },
        },
        {
          path: 'introduce',
          component: () =>
            import('@/views/enterprise-side/introduce/index.vue'),
        },
        {
          path: 'introduce-detail',
          name: 'ent-int-dtl',
          component: () =>
            import('@/views/enterprise-side/introduce/detail/index.vue'),
        },
        {
          path: 'area',
          component: () => import('@/views/enterprise-side/area/index.vue'),
          meta: {
            name: '区级专题',
          },
        },
        {
          path: 'feedback',
          name: 'feedback',
          component: () => import('@/views/enterprise-side/feedback/index.vue'),
          meta: {
            name: '意见反馈',
          },
        },
        {
          path: 'platform',
          redirect: '/enterprise-side/platform/home',
          meta: {
            name: '平台载体',
          },
          children: [
            {
              path: 'home',
              component: () =>
                import('@/views/enterprise-side/platform/home/<USER>'),
              meta: {
                name: '平台载体',
              },
            },
            {
              path: 'lead-industry',
              component: () =>
                import(
                  '@/views/enterprise-side/platform/lead-industry/index.vue'
                ),
              meta: {
                name: '主导产业',
              },
            },
            {
              // 主导产业详情
              path: 'lead-industry-detail',
              component: () =>
                import(
                  '@/views/enterprise-side/platform/lead-industry-detail/index.vue'
                ),
              meta: {
                name: '详情',
              },
            },
            {
              // 创新资源
              path: 'innovation-resource',
              component: () =>
                import(
                  '@/views/enterprise-side/platform/innovation-resource/index.vue'
                ),
              meta: {
                name: '创新资源',
              },
            },
          ],
        },
      ],
    },
    // 招商资源库
    {
      path: '/zszyk',
      component: () => import('@/views/zszyk/index.vue'),
      redirect: '/zszyk/home',
      meta: {
        name: '招商资源库',
      },
      children: [
        {
          path: 'home',
          component: () => import('@/views/zszyk/home/<USER>'),
          meta: {
            name: '招商资源库',
          },
        },
        {
          path: 'orgs/:type',
          component: () => import('@/views/zszyk/organs/index.vue'),
          meta: {
            name: '机构列表',
          },
        },
        {
          path: 'org-form/:type/:id?',
          component: () => import('@/views/zszyk/organs/form/index.vue'),
          meta: {
            name: '编辑机构',
          },
        },
        {
          path: 'concat-form/:index?',
          component: () => import('@/views/zszyk/organs/form/concat-form.vue'),
          meta: {
            name: '编辑联系人',
          },
        },
        {
          path: 'org-detail/:type/:id',
          component: () => import('@/views/zszyk/organs/form/index.vue'),
          meta: {
            name: '机构详情',
            status: 'detail',
          },
        },
        {
          path: 'concat-detail/:index',
          component: () => import('@/views/zszyk/organs/form/concat-form.vue'),
          meta: {
            name: '联系人信息',
            status: 'detail',
          },
        },
      ],
    },
    // 走访企业库
    {
      path: '/visit',
      component: () => import('@/views/visit/index.vue'),
      redirect: '/visit/orgs',
      meta: {
        name: '走访企业库',
      },
      children: [
        {
          path: 'orgs',
          component: () => import('@/views/visit/orgs/index.vue'),
          meta: {
            name: '企业列表',
          },
        },
        {
          path: 'org-form/:id?',
          component: () => import('@/views/visit/orgs/form/index.vue'),
          meta: {
            name: '编辑企业',
          },
        },
        {
          path: 'visit-form/:index?',
          component: () =>
            import('@/views/visit/orgs/form/visit-info-form.vue'),
          meta: {
            name: '编辑走访信息',
          },
        },
        {
          path: 'org-detail/:id',
          component: () => import('@/views/visit/orgs/form/index.vue'),
          meta: {
            name: '企业详情',
            status: 'detail',
          },
        },
        {
          path: 'visit-detail/:index',
          component: () =>
            import('@/views/visit/orgs/form/visit-info-form.vue'),
          meta: {
            name: '走访信息',
            status: 'detail',
          },
        },
        {
          path: 'logs/:id',
          component: () => import('@/views/visit/orgs/form/logs.vue'),
          meta: {
            name: '操作记录',
          },
        },
        {
          path: 'file-view/:url',
          component: () => import('@/views/file-view/index.vue'),
          meta: {
            name: '文件预览',
          },
        },
      ],
    },
    // 预览
    {
      path: '/file-view',
      component: () => import('@/views/file-view/index.vue'),
      meta: {
        name: '文件预览',
      },
    },
    {
      path: '/test-auto',
      component: () => import('@/components/auto-complete/example.vue'),
    },
  ],
  scrollBehavior() {
    // 始终滚动到顶部
    return { top: 0 }
  },
})

export default router
