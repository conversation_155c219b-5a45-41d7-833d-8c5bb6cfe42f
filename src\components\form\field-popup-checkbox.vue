<template>
  <div class="w-full">
    <Field
      :model-value="checkboxLabes"
      :is-link="!readonly"
      :readonly="readonly"
      :name="name"
      :label="label"
      placeholder="点击选择"
      :rules="rules"
      @click="visible = true" />
    <Popup
      v-if="!readonly"
      v-model:show="visible"
      round
      destroy-on-close
      :style="{ height: '60%' }"
      :position="position">
      <div class="popup_main">
        <header>
          <span
            class="cancel"
            @click="onCancel">
            取消
          </span>
          <span
            class="confirm"
            @click="onConfirm">
            确认
          </span>
        </header>
        <main>
          <CheckboxGroup v-model="checkboxValues">
            <Checkbox
              v-for="(c, i) in options"
              :key="i"
              class="mt-[12px]"
              :name="c.value">
              {{ c.label }}
            </Checkbox>
          </CheckboxGroup>
        </main>
      </div>
    </Popup>
  </div>
</template>

<script setup>
  import { isArray, isEmpty, isString } from '@/utils/is'
  import { Field, Popup, CheckboxGroup, Checkbox } from 'vant'
  import { computed, ref } from 'vue'

  // 多项选择器
  defineOptions({
    name: 'FieldPopupCheckbox',
  })

  const props = defineProps({
    name: String,
    label: String,
    options: {
      type: Array,
      default: () => [],
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    position: {
      type: String,
      default: 'bottom',
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    rules: {
      type: Array,
      default: () => [],
    },
  })

  // 控制选择器显示
  const visible = ref(false)

  // 所选中的选项的值
  const selectedValue = defineModel({
    type: [String, Array],
    required: true,
  })

  const checkboxValues = computed({
    get() {
      if (isEmpty(selectedValue.value)) {
        return []
      } else if (isArray(selectedValue.value)) {
        return selectedValue.value
      } else if (isString(selectedValue.value)) {
        return selectedValue.value.split(',')
      }
      return []
    },
    set(value) {
      if (isArray(selectedValue.value)) {
        selectedValue.value = value
      } else if (isString(selectedValue.value)) {
        selectedValue.value = value.join(',')
      }
    },
  })
  const checkboxLabes = computed(() => {
    return props.options
      .filter((item) => checkboxValues.value.includes(item.value))
      .map((item) => item.label)
      .join(',')
  })

  function onCancel() {
    visible.value = false
  }
  function onConfirm() {
    visible.value = false
  }
</script>

<style lang="scss" scoped>
  .popup_main {
    height: 100%;
    padding: 5px;
    & > header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 46px;
      & > span {
        height: 100%;
        padding: var(--van-picker-action-padding);
        border: none;
        background-color: transparent;
        font-size: var(--van-picker-action-font-size);
        line-height: 46px;
        cursor: pointer;

        &.cancel {
          color: var(--van-picker-cancel-action-color);
        }
        &.confirm {
          color: var(--van-picker-confirm-action-color);
        }
        &:active {
          opacity: 0.6;
        }
      }
    }
    & > main {
      height: calc(100% - 46px);
      padding: 4px 15px;
      overflow: auto;
      scrollbar-width: thin;
    }
  }
</style>
