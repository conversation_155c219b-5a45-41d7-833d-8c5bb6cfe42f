<template>
  <div class="pane">
    <Skeleton
      v-if="loading"
      title
      :row="12" />
    <template v-else>
      <div
        class="banner"
        @click="toCompanyPane('', true)">
        <span>全部数据</span>
        <div>
          <h1>{{ numberKilobit(allCount, 0) }}</h1>
          <span>条</span>
        </div>
      </div>

      <div
        v-for="(item, index) in data"
        :key="item.label"
        class="card"
        @click="toCompanyPane(item.label)">
        <header>
          <div class="title">
            <h1>{{ item.label }}</h1>
            <span>{{ calculatePercentage(item.value, allCount) }}%</span>
          </div>
          <h1>{{ numberKilobit(item.value, 0) }} 条</h1>
        </header>
        <Progress
          :color="getColorByIndex(index, colors)"
          :percentage="calculatePercentage(item.value, allCount)"
          :show-pivot="false"
          stroke-width="6" />
      </div>
    </template>
  </div>
</template>

<script setup>
  import { getVisitDataBoardData } from '@/apis/visit'
  import {
    useVisitIndexStore,
    useVisitOrgFormStore,
    useVisitUserStore,
  } from '@/stores/visit'
  import { getColorByIndex } from '@/utils'
  import { calculatePercentage, numberKilobit } from '@/utils/number'
  import { useRequest } from 'alova/client'
  import { Progress, Skeleton } from 'vant'
  import { computed, onActivated } from 'vue'

  const colors = [
    '#267DE8',
    '#5893DB',
    '#7EAFEA',
    '#7EC8EA',
    '#7EDFEA',
    '#58DBA0',
    '#7EEABD',
    '#CFEA7E',
    '#EAE57E',
    '#EAC27E',
    '#EA857E',
    '#EA7EBF',
    '#EA636C',
    '#C27EEA',
    '#DF63EA',
    '#877EEA',
    '#8063EA',
  ]

  const { loading, data, send } = useRequest(getVisitDataBoardData, {
    initialData: [],
  })

  // 全部数据
  const allCount = computed(() =>
    data.value.reduce((accumulator, current) => {
      return accumulator + current.value
    }, 0),
  )

  const store = useVisitIndexStore()
  const userStore = useVisitUserStore()
  function toCompanyPane(label, isAll) {
    if (!userStore.isZwb) return
    if (isAll) {
      store.$patch({
        activeTabIndex: 1,
        region: '',
        industry: '',
      })
    } else {
      store.$patch({
        activeTabIndex: 1,
        region: label,
      })
    }
  }

  const formStore = useVisitOrgFormStore()
  onActivated(() => {
    // 有过更新操作，重新加载数据列表。
    if (formStore.formActionType) {
      send()
      formStore.formActionType = ''
    }
  })
</script>

<style lang="scss" scoped>
  .pane {
    display: flex;
    flex-direction: column;
    padding: 16px;
    gap: 16px;

    .banner {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 64px;

      padding: 16px;
      overflow: hidden;
      border-radius: 10px 10px 10px 10px;
      background-image: url('../../../../../assets/images/visit/data-pane-banner.png');
      // background-size: 100% 100%;
      background-size: cover;
      background-repeat: no-repeat;
      background-color: #3fbafe;
      background-position-y: center;
      box-shadow: 0px 2px 6px 0px rgba(63, 186, 254, 0.5);

      & > span {
        color: #ffffff;
        font-size: 14px;
        line-height: 32px;
      }
      & > div {
        display: flex;
        align-items: baseline;
        gap: 4px;
        & > h1 {
          color: #ffffff;
          font-weight: 600;
          font-size: 24px;
          line-height: 32px;
        }
        & > span {
          color: #ffffff;
          font-weight: 600;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }

    .card {
      padding: 12px 16px;
      border-radius: 10px 10px 10px 10px;
      background-color: #ffffff;
      box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.06);
      & > header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 5px;
        .title {
          display: flex;
          align-items: center;
          gap: 6px;
          & > h1 {
            color: rgba(0, 0, 0, 0.88);
            font-weight: 600;
            font-size: 14px;
            line-height: 22px;
          }
          & > span {
            color: rgba(0, 0, 0, 0.5);
            font-size: 12px;
            line-height: 20px;
          }
        }
        & > h1 {
          color: rgba(0, 0, 0, 0.88);
          font-weight: 600;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
</style>
