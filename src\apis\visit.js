// 招商资源库接口
import { get, post } from '@/utils/alova/index'

const baseUrl = import.meta.env.VITE_API_RECOMMEND_BASE_URL

/**
 * 获取招商资源库列表
 * @param {*} data
 * @returns
 */
export function getOrgs(data) {
  return post(`${baseUrl}/visitEnterpriseDatabase/list`, data)
}

/**
 * 获取企业推荐列表前100条
 * @param {*} data
 * @returns
 */
export const getRecommendAllList = (data) =>
  post(`${baseUrl}/api/v1/recommend/company/list/byOnly`, data, {
    cacheFor: 10 * 60 * 1000, // 缓存时间（毫秒） 10分钟
  })

/**
 * 获取企业类别
 */
export const getEntTypeList = () =>
  get(`${baseUrl}/api/v1/company/card/companyTypes`)

/**
 * 根据id获取用户信息
 * @returns
 */
export const getVisitUserInfoById = (data) =>
  get(`${baseUrl}/visitEnterpriseDatabase/userInfo`, data)

/**
 * 添加走访企业信息
 * @param {*} data
 * @returns
 */
export const addOrg = (data) =>
  post(`${baseUrl}/visitEnterpriseDatabase/saveEnterprise`, data)

/**
 * 删除企业信息
 * @param {*} data
 * @returns
 */
export function deleteOrg(data) {
  return post(`${baseUrl}/visitEnterpriseDatabase/delete`, data)
}

/**
 * 获取企业走访详情信息
 * @param {*} data
 * @returns
 */
export function getOrgDetail(data) {
  return get(`${baseUrl}/visitEnterpriseDatabase/getEnterprise`, data)
}

/**
 * 修改企业走访信息
 * @param {*} data
 * @returns
 */
export function updateOrgInfo(data) {
  return post(`${baseUrl}/visitEnterpriseDatabase/update`, data)
}

/**
 * 获取共享用户列表
 * @param {*} data
 * @returns
 */
export function getShearUsers(data) {
  return get(`${baseUrl}/visitEnterpriseDatabase/getShareUserList`, data)
}

/**
 * 共享给用户列表
 * @param {*} data
 * @returns
 */
export function shareUser(data) {
  return post(`${baseUrl}/visitEnterpriseDatabase/share`, data)
}

/**
 * 获取操作日志列表
 * @param {*} data
 * @returns
 */
export function getLogs(bid) {
  return get(`${baseUrl}/updateLog/VISIT_ENTERPRISE_BASIC_INFORMATION/${bid}`)
}

/**
 * 获取数据看板
 * @param {*} data
 * @returns
 */
export function getVisitDataBoardData(data) {
  return get(`${baseUrl}/visitEnterpriseDatabase/dataBoard`, data)
}

/**
 * 企业看板
 * @param {*} data
 * @returns
 */
export function getVisitCompanyBoard(data) {
  return post(`${baseUrl}/visitEnterpriseDatabase/enterpriseBoard`, data)
}
