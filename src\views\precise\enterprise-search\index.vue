<template>
  <div class="enterprise_search">
    <img
      src="@/assets/images/precise/qycx.png"
      width="100%"
      height="120px"
      alt="" />
    <div class="list">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="item"
        @click="jumpTo(item.src)">
        <img
          class="item_bg"
          style="object-fit: contain"
          src="@/assets/images/precise/search.png"
          alt="" />
        <div class="icon">
          <i
            class="iconfont"
            :style="item.style"
            style="font-size: 20px"
            :class="[item.icon]"></i>
        </div>
        <div class="item_title">
          <div class="title">
            {{ item.title }}
            <i
              style="font-size: 12px"
              class="iconfont icon-RightCircle ml-[6px] text-[rgba(0,0,0,0.6)]"></i>
          </div>
          <div class="content">{{ item.text }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getTextGradient } from '@/utils'

  const list = [
    {
      icon: 'icon-enterprise',
      style: getTextGradient(['#69D9F4', '#54B5F6'], '135deg'),
      title: '一企一档',
      text: '全面的企业信息档案系统，包含企业基本信息、经营状况等多维度数据，助您深入了解企业发展现状。',
      src: 'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2FenterpriseSearch%2FenpSearch',
    },
    {
      icon: 'icon-kzqy',
      style: getTextGradient(['#FFCA65', '#F6AB52'], '135deg'),
      title: '扩张企业',
      text: '发现快速成长的明星企业，追踪企业扩张轨迹，掌握最新商业动态，为您提供精准的招商决策支持。',
      // src: 'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2FenterpriseSearch%2FexcellentEnp',
      src: 'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2FenterpriseSearch%2FexcellentEnp',
    },
    {
      icon: 'icon-Park',
      style: getTextGradient(['#74A8FE', '#547AF7'], '135deg'),
      title: '产业园区',
      text: '汇集武汉重点产业园区信息，展示园区特色产业布局，助力企业选址决策。',
      src: 'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&extendInfo=445,262&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2Findustrialpark%2Findex',
    },
  ]

  function jumpTo(url) {
    window.open(url)
  }
</script>

<style lang="scss" scoped>
  .enterprise_search {
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    .list {
      padding: 0 16px;

      .item {
        display: flex;
        position: relative;
        padding: 30px 16px;
        border: 1px solid #fff;
        border-radius: 10px;
        background: linear-gradient(135deg, #eef8ff 0%, #fff 100%);
        box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.06);

        .icon {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 36px;
          border-radius: 8px;
          background: #fff;
        }

        .item_title {
          margin-left: 8px;

          .title {
            color: rgba(0, 0, 0, 0.9);

            font-weight: 600;
            font-size: 16px;
          }

          .content {
            margin-top: 4px;
            color: rgba(0, 0, 0, 0.6);
            font-size: 13px;
          }
        }

        .item_bg {
          position: absolute;
          right: 3px;
          bottom: 0px;
          width: 75px;
        }

        & + .item {
          margin-top: 12px;
        }
      }
    }
  }
</style>
