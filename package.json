{"name": "tcjzs-h5", "version": "1.0.340", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:ol": "vite build --mode online", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "prepare": "husky", "v:p": "pnpm version patch --commit '更新版本号: %s' --push", "v:mnr": "pnpm version minor --commit '更新版本号: %s' --push", "v:mor": "pnpm version major --commit '更新版本号: %s' --push"}, "dependencies": {"@alova/adapter-axios": "^2.0.13", "@alova/mock": "^2.0.14", "@amap/amap-jsapi-loader": "^1.0.1", "@antv/g6": "^5.0.45", "@tailwindcss/vite": "^4.1.7", "@vueuse/core": "^12.3.0", "alova": "^3.2.11", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.6.0", "js-sha1": "^0.7.0", "md-editor-v3": "^5.5.0", "nanoid": "^5.1.5", "pinia": "^3.0.2", "vant": "^4.9.21", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-page-stack": "^3.2.0", "vue-router": "^4.5.0", "vue3-h5-table": "^0.3.1", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/amap-js-api": "^1.4.16", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.20.10", "eslint": "^9.27.0", "eslint-plugin-vue": "^10.1.0", "globals": "^16.1.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "postcss": "^8.5.3", "postcss-100vh-fix": "^2.0.0", "postcss-html": "^1.7.0", "postcss-px2vp": "^1.1.4", "prettier": "^3.5.3", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "^1.89.0", "sharp": "^0.34.2", "svgo": "^3.3.2", "tailwindcss": "^4.1.7", "vite": "^6.3.5", "vite-plugin-image-optimizer": "^1.1.8", "vite-plugin-vue-devtools": "^7.7.6"}}