<template>
  <div class="pt-[24px]">
    <div
      v-for="(_, index) in Math.ceil(items.length / 2)"
      :key="index"
      class="risk_line">
      <div
        v-if="index * 2 <= items.length"
        class="risk_item">
        <div class="h-[4px] w-[4px] bg-[rgba(0,0,0,0.9)]"></div>
        <div class="label">{{ items[index * 2].label }}</div>
        <div
          class="value"
          @click="onItemClick(items[index * 2])"
          >{{ items[index * 2].value }}</div
        >
        <div class="unit">{{ items[index * 2].unit }}</div>
      </div>
      <div
        v-if="index * 2 + 1 < items.length"
        class="risk_item">
        <div class="h-[4px] w-[4px] bg-[rgba(0,0,0,0.9)]"></div>
        <div class="label">{{ items[index * 2 + 1].label }}</div>
        <div
          class="value"
          @click="onItemClick(items[index * 2 + 1])"
          >{{ items[index * 2 + 1].value }}</div
        >
        <div class="unit">{{ items[index * 2 + 1].unit }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'

  const { info } = defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const router = useRouter()

  const items = computed(() => [
    {
      label: '投资线索',
      value: info.expansionIntentionVo?.investinfoCount || 0,
      unit: '条',
    },
    {
      label: '融资线索',
      value: info.expansionIntentionVo?.financingCount || 0,
      unit: '条',
    },
    {
      label: '新闻线索',
      value: info.expansionIntentionVo?.newsTotalCount || 0,
      // value: info.recommendReasonVo?.wuhanNewsCountRecommend || 0,
      unit: '条',
    },
    {
      label: '招投标',
      value: info.recommendReasonVo?.localWtbCount || 0,
      unit: '条',
    },
    {
      label: '招聘信息',
      value: info.recommendReasonVo?.wuhanRecruitmentRecommend || 0,
      unit: '条',
    },
  ])

  function onItemClick(item) {
    console.log(item)
    if (item.value && item.value != 0) {
      router.push({
        path: '/company-detail',
        query: {
          key: item.label,
          companyId: info.companyBasicInfoVo.companyId,
        },
      })
    }
  }
</script>

<style lang="scss" scoped>
  .risk_line {
    display: flex;

    .risk_item {
      display: flex;
      align-items: center;
      width: 165px;
      height: 38px;
      padding: 0 8px;
      border-radius: 6px;

      .label {
        flex: 1;
        margin-left: 4px;
        color: rgb(0 0 0 / 90%);
        font-size: 14px;
      }

      .value {
        color: #1255e4;
        font-weight: 600;
        font-size: 14px;
      }

      .unit {
        color: rgb(0 0 0 / 60%);
        font-size: 14px;
      }

      & + .risk_item {
        margin-left: 13px;
      }
    }

    &:nth-child(odd) {
      .risk_item {
        background: rgb(18 85 228 / 6%);
      }
    }

    &:nth-child(even) {
      .risk_item {
        background: rgb(18 190 228 / 6%);
      }
    }

    & + .risk_line {
      margin-top: 13px;
    }
  }
</style>
