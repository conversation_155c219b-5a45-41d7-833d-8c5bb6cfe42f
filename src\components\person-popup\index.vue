<template>
  <Popup
    v-model:show="show"
    position="bottom"
    round
    closeable
    style="height: 90%"
    @close="emits('close')">
    <div class="flex h-full flex-col overflow-hidden pt-[14px]">
      <div class="w-full text-center text-[16px] leading-[22px]">
        {{ title }}
      </div>
      <Search
        v-model="keyword"
        shape="round"
        clearable
        placeholder="请输入搜索关键词"
        @clear="emits('clear')" />
      <div
        ref="shareListRef"
        v-bind="containerProps"
        class="relative flex-1 overflow-y-auto overflow-x-hidden px-[16px]">
        <CheckboxGroup
          v-model="selectOptions"
          v-bind="wrapperProps">
          <div
            v-for="(item, index) in showList"
            :key="index"
            :hidden="item.data.hidden"
            class="share_item">
            <Checkbox
              :model-value="!!item.data[checkKey]"
              :disabled="!!item.data[checkKey]"
              :name="item.data[valueKey]">
              {{ item.data[labelKey] }}
            </Checkbox>
          </div>
        </CheckboxGroup>
        <div
          v-if="!loading && filterList.length === 0"
          class="flex h-full w-full items-center justify-center text-gray-400">
          {{ emptyText || '暂无数据' }}
        </div>
        <Loading
          v-if="loading"
          type="spinner"
          class="absolute! w-full! h-full! flex! items-center! justify-center!"></Loading>
      </div>
      <Button
        block
        type="primary"
        @click="onConfirm">
        确定
      </Button>
    </div>
  </Popup>
</template>

<script setup>
  import { useVirtualList } from '@vueuse/core'
  import { Button, Checkbox, CheckboxGroup, Loading, Popup, Search } from 'vant'
  import { computed, ref, useTemplateRef, watch } from 'vue'

  const show = defineModel('show', {
    type: Boolean,
    required: true,
    default: false,
  })
  const { list, defaultValue, labelKey } = defineProps({
    title: {
      type: String,
      default: '请选择',
    },
    list: Array,
    loading: Boolean,
    defaultValue: Array,
    checkKey: {
      type: String,
      default: 'selected',
    },
    valueKey: {
      type: String,
      default: 'userid',
    },
    labelKey: {
      type: String,
      default: 'nickname',
    },
    emptyText: String,
  })

  const emits = defineEmits(['clear', 'close', 'confirm'])

  const shareListRef = useTemplateRef('shareListRef')
  const selectOptions = ref([])
  const keyword = ref('')

  watch(show, (val) => {
    if (val) {
      selectOptions.value = defaultValue || []
    }
  })

  const filterList = computed(() => {
    if (!keyword.value.trim()) return list

    // return list.map((item) => {
    //   const show =
    //     item[labelKey].toLowerCase().indexOf(keyword.value.toLowerCase()) === 0
    //   return {
    //     ...item,
    //     hidden: !show,
    //   }
    // })
    return list.filter((item) => {
      const show =
        item[labelKey].toLowerCase().indexOf(keyword.value.toLowerCase()) === 0
      return show
    })
  })

  const {
    list: showList,
    containerProps,
    wrapperProps,
    scrollTo,
  } = useVirtualList(filterList, {
    itemHeight: 47,
  })

  watch(keyword, () => {
    // shareListRef.value?.scrollTo({ top: 0 })
    scrollTo(0)
  })

  // function onSearch(val) {
  //   emits('search', val)
  //   nextTick(() => {
  //     shareListRef.value?.scrollTo({ top: 0 })
  //   })
  // }

  function onConfirm() {
    emits('confirm', selectOptions.value)
    show.value = false
  }
</script>

<style lang="scss" scoped>
  .share_item {
    padding: 10px 0;
    border-bottom: 1px solid #ebedf0;
  }
</style>
