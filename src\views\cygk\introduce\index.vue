<template>
  <div
    ref="pageRef"
    class="h-[100%] w-[100%] overflow-y-auto bg-[#F7F8FA]">
    <img
      width="100%"
      height="120px"
      src="@/assets/images/precise/zszy_.png"
      alt="" />
    <!-- 产业链筛选 -->
    <div
      v-if="type !== '应用场景' && type !== '高等院校'"
      class="content_filter">
      <div class="flex h-[24px] w-full items-center justify-between">
        <!-- <div
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showTypeMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ type || '应用场景' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div> -->
        <div
          v-if="interfacePath === 'park' || interfacePath === 'building'"
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showAreaMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ area || '行政区' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div>
        <div
          v-if="
            interfacePath !== 'building' &&
            interfacePath !== 'park' &&
            interfacePath !== 'colleges'
          "
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ industry || '产业链' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div>
        <!-- <div
          v-if="interfacePath === 'building'"
          class="flex flex-1 items-center justify-center gap-[6px]"
          @click="showBuildMenu = true">
          <span class="currentTitle leading-[24px] text-[#000]">
            {{ level || '等级' }}
          </span>
          <i class="iconfont icon-caret-down-small text-[10px]"></i>
        </div> -->
      </div>
    </div>

    <div class="w-full px-[16px] py-[12px]">
      <!-- 标题 总数 -->
      <div class="flex w-full items-center justify-between">
        <Title>{{ title }}</Title>
        <!-- <div class="flex gap-[5px]">
          <span
            v-if="totalNumber && interfacePath !== 'scene'"
            class="mj"
            >总面积：<span class="text-[#1255E4]">{{
              formatLargeNumber(totalNumber)
            }}</span>
            <span v-if="interfacePath === 'park'">亩</span>
            <span v-if="interfacePath === 'building'">平方米</span>
          </span>
          <span class="total"
            >共<span class="text-[#1255E4]">{{ totalLength }}</span
            >个</span
          >
        </div> -->
        <span class="total">
          共计
          <span class="text-[#1255E4]">{{ totalLength }}</span>
          条
        </span>
      </div>
      <div
        v-if="!loading"
        class="list mt-[16px]">
        <!-- 列表 -->
        <div
          v-for="(item, index) in filteredTableData"
          :key="index"
          class="card relative"
          @click="handlerToDetail(item.id)">
          <!-- 列表标题 -->
          <div class="title relative">
            <div class="flex w-full justify-between gap-[10px]">
              <span
                v-if="interfacePath !== 'park' && interfacePath !== 'building'"
                class="flex-1">
                {{ item.name }}
              </span>
              <span
                v-else
                class="flex-1 pr-[85px]">
                {{ item.name }}
              </span>

              <!-- <Tag
                v-if="interfacePath === 'building' && item?.dj"
                type="primary"
                >{{ item?.dj }}</Tag
              >
              <Tag
                v-if="
                  interfacePath === 'building' &&
                  (item?.szxzq || item?.dataInfo?.szxzq)
                "
                type="primary"
                >{{ item?.szxzq || item?.dataInfo?.szxzq || '暂无数据' }}</Tag
              > -->
              <!-- <Tag
                v-if="interfacePath === 'park' && item.district"
                type="primary">
            
                <span class="text-ellipsis">
                  {{ item.district }}
                </span>
              </Tag> -->
              <Image
                v-if="interfacePath === 'park' || interfacePath === 'building'"
                :src="item?.imgs"
                width="80px"
                height="50px"
                lazy-load
                class="absolute! right-0 top-[0px]">
                <template #loading>
                  <Loading
                    type="spinner"
                    size="20">
                    加载中
                  </Loading>
                </template>
                <template #error>加载失败</template>
              </Image>
              <Tag
                v-if="interfacePath === 'association' && item.ssxzq"
                type="primary">
                <span class="text-ellipsis">
                  {{ item.ssxzq }}
                </span>
              </Tag>
            </div>
          </div>
          <!-- 联系人 -->
          <div
            v-if="interfacePath === 'scene' && item.cjly"
            class="line_text">
            {{ item.cjly }}
          </div>
          <div
            v-if="
              interfacePath === 'park' && (item?.lxr || item?.dataInfo?.lxr)
            "
            class="line_text mt-[6px]">
            <p
              v-if="interfacePath !== 'park'"
              class="text">
              <i class="iconfont icon-peace text-[12px]" />
              {{ item?.lxr || item?.dataInfo?.lxr || '暂无联系人' }}
            </p>
            <p
              v-else
              class="text">
              <i class="iconfont icon-peace text-[12px]" />
              {{ item?.phonename }}
            </p>
          </div>
          <!-- 联系方式 -->
          <div
            v-if="
              interfacePath === 'park' &&
              (item?.lxdh?.replaceAll('\\n', '') ||
                item.dataInfo?.lxdh?.replaceAll('\\n', ''))
            "
            class="line_text mt-[6px]">
            <p
              v-if="interfacePath !== 'park'"
              class="text">
              <i class="iconfont icon-xt_PhoneOutlined text-[12px]" />
              {{
                item?.lxdh?.replaceAll('\\n', '') ||
                item.dataInfo?.lxdh?.replaceAll('\\n', '') ||
                '暂无联系方式'
              }}
            </p>
            <p
              v-else-if="item.phone?.replaceAll('\\n', '')"
              class="text">
              <i class="iconfont icon-xt_PhoneOutlined text-[12px]" />
              {{ item.phone?.replaceAll('\\n', '') }}
            </p>
          </div>

          <!-- 学院 -->
          <!-- <div
            v-if="interfacePath === 'colleges'"
            class="line_text mt-[6px]">
            <p class="text">
              {{ item?.xy || '暂无学院' }}
            </p>
          </div> -->

          <!-- 地址 -->
          <div
            v-if="type !== '应用场景'"
            class="line_text mt-[6px]">
            <p
              v-if="interfacePath !== 'park' && interfacePath !== 'building'"
              class="text">
              {{ item.dz?.replaceAll('\\n', '') || '暂无地址' }}
            </p>
            <p
              v-else-if="interfacePath === 'building'"
              class="text pr-[59px]">
              {{ item.dz?.replaceAll('\\n', '') || '暂无地址' }}
            </p>
            <p
              v-else
              class="text pr-[59px]">
              {{ item.address?.replaceAll('\\n', '') || '暂无地址' }}
            </p>
          </div>
          <!-- 产业链 -->
          <div
            v-if="
              interfacePath === 'innovate' || interfacePath === 'association'
            "
            class="line_text mt-[6px]">
            <p class="text">
              {{ item?.industrychain || '暂无产业链' }}
            </p>
          </div>
          <!-- 类型 -->
          <div
            v-if="
              (interfacePath === 'research' || interfacePath === 'innovate') &&
              item?.lx
            "
            class="line_text mt-[6px]">
            <p class="text">
              {{ item?.lx }}
            </p>
          </div>

          <!-- 箭头icon -->
          <!-- <i
            v-if="type !== '产业园区' && type !== '商务楼宇'"
            class="iconfont icon-RightCircle absolute bottom-[10px] right-[16px] flex text-[12px] text-[#000]/[.26]"></i> -->
        </div>
      </div>
      <div
        v-else
        class="flex justify-center">
        <Loading></Loading>
      </div>
    </div>
  </div>

  <ActionSheet
    v-model:show="showMenu"
    :actions="industryOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onIndustryOptionsChange"></ActionSheet>
  <ActionSheet
    v-model:show="showAreaMenu"
    :actions="areaOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onAreaOptionsChange"></ActionSheet>
  <ActionSheet
    v-model:show="showBuildMenu"
    :actions="levelOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onLevelOptionsChange"></ActionSheet>
  <!-- <ActionSheet
    v-model:show="showTypeMenu"
    :actions="typeOptions"
    cancel-text="取消"
    close-on-click-action
    @select="onTypeOptionsChange"></ActionSheet> -->
</template>

<script setup>
  import * as apis from '@/apis/index'
  import { getBuildAreaList, getParkAreaList } from '@/apis/precise.js'
  import Title from '@/components/title/index.vue'
  import { useResourceStore } from '@/stores/resource.js'
  import { areaOptionsDefault } from '@/views/recommend/options.js'
  import { useScroll } from '@vueuse/core'
  import { ActionSheet, Image, Loading, Tag } from 'vant'
  import {
    computed,
    nextTick,
    onBeforeUnmount,
    onMounted,
    ref,
    useTemplateRef,
  } from 'vue'
  import { useRoute, useRouter } from 'vue-router/dist/vue-router'
  import { INDUSTRY_OPTIONS } from '../options.js'

  const router = useRouter()
  const route = useRoute()
  const resourceStore = useResourceStore()
  const loading = ref(false)
  const showMenu = ref(false)
  const showAreaMenu = ref(false)
  const showBuildMenu = ref(false)
  // const showTypeMenu = ref(false)
  const industry = ref('产业链')
  const area = ref('行政区')
  const type = ref('应用场景')
  const level = ref('等级')
  const pageRef = useTemplateRef('pageRef')

  const { y } = useScroll(pageRef)

  async function getAreaList() {
    try {
      const {
        data: { data: res },
      } = await (route.query.key === '产业园区'
        ? getParkAreaList()
        : getBuildAreaList())
      areaOptions.value = res.map((item) => ({ name: item, value: item }))
    } catch (e) {
      console.log(e)
    }
  }

  const industryOptions = computed(() => {
    return [].concat(INDUSTRY_OPTIONS.map((m) => ({ name: m.value })))
  })

  const areaOptions = ref(areaOptionsDefault)

  onMounted(() => {
    if (route.query.key === '产业园区' || route.query.key === '商务楼宇') {
      getAreaList()
    }
  })

  const levelOptions = [
    { name: '等级' },
    { name: '甲级' },
    { name: '超甲级' },
    { name: '亿元' },
  ]
  // const typeOptions = computed(() => {
  //   return TYPE_OPTIONS.map((m) => ({ ...m, name: m.value }))
  // })

  // function formatLargeNumber(num) {
  //   if (num >= 100000000) {
  //     // 亿
  //     let numInYi = num / 100000000
  //     let formattedNum = numInYi.toFixed(2)
  //     return formattedNum + ' 亿'
  //   } else if (num >= 10000) {
  //     // 万
  //     let numInWan = num / 10000
  //     let formattedNum = numInWan.toFixed(2)
  //     return formattedNum + ' 万'
  //   } else {
  //     // 小于万
  //     return num.toString()
  //   }
  // }

  const title = computed(() => {
    return {
      scene: '应用场景',
      park: '产业园区',
      building: '商务楼宇',
      colleges: '高等院校',
      research: '科研院所',
      innovate: '功能平台',
      association: '协会联盟',
    }[interfacePath.value]
  })
  // scene
  const interfacePath = ref('scene')
  const handlerToDetail = (id) => {
    router.push({
      path: '/cygk/introduce-detail',
      query: {
        noFooter: route.query.noFooter,
        id,
        type: interfacePath.value,
        key: {
          building: '楼宇详情',
          park: '园区详情',
          colleges: '高等院校详情',
          research: '科研院所详情',
          association: '协会联盟详情',
          innovate: '功能平台详情',
          scene: '应用场景详情',
        }[interfacePath.value],
        // interfacePath.value === 'park'
        //   ? '园区详情'
        //   : interfacePath.value === 'building'
        //     ? '楼宇详情'
        //     : '应用场景详情',
      },
    })
  }
  const tableData = ref([])
  const totalNumber = ref(0)

  const filteredTableData = computed(() => {
    // 只有当接口路径为'scene'（应用场景）时才进行特殊处理
    if (interfacePath.value === 'scene') {
      return tableData.value.map((item) => {
        // 使用 map 而不是 filter
        // 创建一个新的对象，避免直接修改原始 item
        const newItem = { ...item }

        // 如果 cjly 是 '其他' 或 '相关职能部门'，则替换为空白字符串
        if (newItem?.cjly === '其他' || newItem?.cjly === '相关职能部门') {
          newItem.cjly = '' // 替换为空白字符串
        }
        return newItem
      })
    }
    // 其他情况直接返回原始数据
    return tableData.value
  })

  const totalLength = computed(() => {
    return tableData.value.length
  })
  const onIndustryOptionsChange = (option) => {
    industry.value = option.name
    onSearch()
  }
  const onAreaOptionsChange = (option) => {
    area.value = option.name
    onSearch()
  }
  const onLevelOptionsChange = (option) => {
    level.value = option.name
    onSearch()
  }

  //
  // const onTypeOptionsChange = (option) => {
  //   type.value = option.name
  //   interfacePath.value = option.text
  //   industry.value = industryOptions.value[0].name
  //   onSearch()
  // }
  const onSearch = async () => {
    try {
      loading.value = true
      const data = {
        industrychain: industry.value === '产业链' ? '' : industry.value,
      }
      if (interfacePath.value === 'park') {
        data.district = area.value === '行政区' ? undefined : area.value
      } else if (interfacePath.value === 'building') {
        data.szxzq = area.value === '行政区' ? undefined : area.value
        data.dj = level.value === '等级' ? undefined : level.value
      }

      if (!industry.value) delete data.industrychain
      if (type.value === '应用场景') delete data.industrychain
      const res = await apis.getAllPageList(interfacePath.value, data)
      tableData.value = res.data.data.map((item) => {
        let data
        let image = null
        const obj = { ...item }
        try {
          if (item.data_info && typeof item.data_info === 'string') {
            data = JSON.parse(item?.data_info)
            obj.dataInfo = data
          }
          if (item.images && typeof item.images === 'string') {
            image = JSON.parse(item.images)
            obj.imgs = image[0]?.value || ''
          }
        } catch {
          data = {}
        }
        return obj
      })
      resourceStore.storeMap[interfacePath.value].list = tableData.value
      resourceStore.storeMap[interfacePath.value].filter = data

      // 楼宇
      if (interfacePath.value === 'building') {
        const arr = tableData.value
          .map((item) => item.tl)
          .map((item) => {
            if (item) {
              // 是否有万
              if (item.includes('万')) {
                const num = item.split('平方米')[0]
                return parseFloat(num.split('万')[0]) * 10000
              }
              return parseFloat(item.split('平方米')[0])
            } else {
              return 0
            }
          })

        const total = arr.reduce((a, b) => a + b, 0)
        totalNumber.value = total
      }
      // 园区
      if (interfacePath.value === 'park') {
        const arr = tableData.value
          .map((item) => item.grossarea)
          .map((item) => {
            if (item) {
              if (item.includes('亩')) {
                const num = item.split('亩')[0]
                if (!isNaN(parseFloat(num))) {
                  return parseFloat(num)
                }
              } else {
                return 0
              }
            } else {
              return 0
            }
          })

        const total = arr.filter((item) => item).reduce((a, b) => a + b, 0)
        totalNumber.value = total
      }
      // 记录当前的类型已经模块
      localStorage.setItem('type', type.value)
      localStorage.setItem('industry', industry.value)
      localStorage.setItem('interfacePath', interfacePath.value)
      resourceStore.storeMap[interfacePath.value].total = totalNumber.value
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    type.value = route.query.key
    interfacePath.value = route.query.type
    const localIndustry = localStorage.getItem('industry')
    if (route.query.type === 'park' && localIndustry) {
      industry.value = localIndustry
    }

    /** 缓存策略 */
    if (!resourceStore.storeMap[interfacePath.value]) {
      resourceStore.addKey(interfacePath.value)
      console.log('初始化', interfacePath.value, resourceStore.storeMap)
      onSearch()
    } else {
      console.log('获取缓存', resourceStore.storeMap)
      const cache = resourceStore.storeMap[interfacePath.value]
      tableData.value = cache?.list ?? []
      totalNumber.value = cache?.total ?? 0
      const params = cache?.filter
      switch (interfacePath.value) {
        case 'park':
          area.value = params.district
          break
        case 'building':
          area.value = params.szxzq
          break
        case 'scene':
          break
      }
      nextTick(() => {
        pageRef.value.scrollTo({ top: cache.y })
      })
    }
  })

  onBeforeUnmount(() => {
    resourceStore.storeMap[interfacePath.value].y = y.value
    console.log(resourceStore.storeMap)
  })
</script>

<style lang="scss" scoped>
  .content_filter {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    background: #fff;
  }

  .card {
    display: flex;
    flex-direction: column;
    width: 100%;
    min-height: 80px;
    margin-top: 16px;
    padding: 16px;
    border-radius: 12px;
    background-color: #fff;

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text-ellipsis {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .line_text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: rgb(0 0 0 / 40%);

      .text {
        width: 283px;
        height: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .total {
    font-size: 12px;
  }

  .mj {
    position: relative;
    font-size: 12px;

    &::after {
      position: absolute;
      top: 30%;
      right: -3px;
      width: 1px;
      height: 50%;
      background-color: rgb(0 0 0 / 6%);
      content: '';
    }
  }
</style>
