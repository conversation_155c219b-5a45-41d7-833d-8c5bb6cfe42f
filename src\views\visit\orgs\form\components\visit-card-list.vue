<template>
  <div class="visit_cards">
    <Space
      v-if="list.length > 0"
      direction="vertical"
      :size="16"
      fill>
      <CellGroup
        v-for="(item, index) in list"
        :key="index"
        inset
        @click="$emit('itemClick', index)">
        <Cell>
          <template #title>
            <h1>{{ item.visitDate }}</h1>
          </template>
          <template #value>
            <span class="record_add_name">
              记录添加人：{{ item.sourceNickname }}
            </span>
          </template>
        </Cell>
        <Field
          :model-value="item.visitPerson"
          readonly
          name="name"
          label="走访人" />
        <Field
          :model-value="dockingNames(item)"
          readonly
          name="contactInformation"
          label="对接人" />
        <Cell v-if="isEdit && hasEdit(item)">
          <template #value>
            <div class="actions">
              <span
                class="primary active-button"
                @click="onEdit(index)">
                编辑
              </span>
              <span
                class="danger active-button"
                @click="onDelete(index)">
                删除
              </span>
            </div>
          </template>
        </Cell>
      </CellGroup>
    </Space>
    <!-- 指定单位，支持 rem, vh, vw -->
    <div
      v-else
      class="pl-[16px] pr-[16px]">
      <Empty
        image-size="8rem"
        description="暂无走访信息记录" />
    </div>
  </div>
</template>

<script setup>
  import { useVisitUserStore } from '@/stores/visit'
  import { Cell, CellGroup, Empty, Field, showConfirmDialog, Space } from 'vant'
  import { useRouter } from 'vue-router'

  defineEmits(['itemClick'])
  defineProps({
    // 是否是编辑状态
    isEdit: {
      type: Boolean,
      default: false,
    },
  })

  const list = defineModel({
    type: Array,
    required: true,
  })

  // 对接人姓名列表
  const dockingNames = (item) => {
    const names = item.dockingPeopleList.map((item) => item.name)
    return names.join('、')
  }

  const router = useRouter()
  const onEdit = (index) => {
    router.push(`/visit/visit-form/${index}`)
  }

  const userStore = useVisitUserStore()
  function hasEdit(item) {
    return userStore.uid === item.sourceUid
  }

  // 删除
  const onDelete = (index) => {
    showConfirmDialog({
      title: '确认删除',
      message: '确认删除该走访信息？',
    }).then(() => {
      list.value.splice(index, 1)
    })
  }
</script>

<style lang="scss" scoped>
  .visit_cards {
    h1 {
      color: #1255e4;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }

    .record_add_name {
      color: #969799;
      color: #969799;
      font-size: 14px;
      line-height: 20px;
    }
    .actions {
      display: flex;
      justify-content: flex-end;
      // padding: 16px 0;
      // border-top: 1px solid rgba(0, 0, 0, 0.06);

      .active-button {
        padding: 3px 16px;
        border-radius: 26px 26px 26px 26px;
        font-size: 14px;
        & ~ .active-button {
          margin-left: 12px;
        }
        &:active {
          opacity: 0.8;
        }
      }
      .primary {
        background: rgba(18, 85, 228, 0.06);
        color: #1255e4;
      }
      .danger {
        background: rgba(255, 77, 79, 0.06);
        color: #ff4d4f;
      }
    }
  }
</style>
