<template>
  <MyList
    ref="listComRef"
    title="研判专家库"
    :default-search-value="keyword"
    :filters
    :loading
    :total="paginate.total"
    :has-next="paginate.hasNext"
    :list-arr="list"
    :show-filter-key="showFilter<PERSON>ey"
    @down="onDown"
    @search="onSearch"
    @card-click="onItemClick">
    <template #card="{ item }">
      <div class="flex items-center">
        <i
          v-if="item.gender === '男'"
          class="iconfont icon-nan text-[#4096FF]" />
        <i
          v-else
          class="iconfont icon-nv text-[#F759AB]" />
        <span class="ml-[6px] font-[600]">{{ item.name }}</span>
      </div>
      <div class="mt-[8px]">
        <div
          v-if="item.job"
          class="flex">
          <i
            class="iconfont icon-zw-1 mt-[-4px] text-[12px] text-[rgba(0,0,0,0.26)]"></i>
          <span class="ml-[4px] text-[12px] text-[rgba(0,0,0,0.6)]">
            {{ item.job }}
          </span>
        </div>
      </div>
    </template>
  </MyList>
</template>

<script setup>
  import { expertListApi } from '@/apis/precise'
  import { useResourceStore } from '@/stores/resource'
  import { INDUSTRY_OPTIONS } from '@/views/cygk/options'
  import {
    computed,
    nextTick,
    onBeforeUnmount,
    onMounted,
    ref,
    useTemplateRef,
  } from 'vue'
  import { useRouter } from 'vue-router'
  import MyList from '../components/MyList/index.vue'

  const router = useRouter()
  const listComRef = useTemplateRef('listComRef')
  const resourceStore = useResourceStore()

  const loading = ref(false)
  const paginate = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    hasNext: true,
  })
  const industry = ref('')
  const list = ref([])
  const keyword = ref('')

  function showFilterKey() {
    return industry.value.length > 0 ? industry.value.join(',') : '全部产业链'
  }

  const filters = computed(() => {
    return [
      {
        key: 'industry',
        value: industry.value,
        emptyText: '产业链',
        options: INDUSTRY_OPTIONS.map((item) => ({
          name: item.text,
          value: item.value,
        })),
        onChange(v) {
          industry.value = v
          list.value = []
          expertList({
            page: {
              pageNum: 1,
              pageSize: 10,
            },
            params: {
              keyword: keyword.value,
              industryChains: [v.value],
            },
          })
        },
        showTitle() {
          return industry.value === '' ? '产业链' : industry.value.value
        },
        onClear() {
          industry.value = []
        },
      },
    ]
  })

  async function expertList(
    params = {
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      params: {},
    },
  ) {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await expertListApi(params)
      list.value.push(...res.list)
      paginate.value.total = res.total
      paginate.value.page = res.page
      paginate.value.hasNext = res.hasNext

      resourceStore.storeMap.expert.list = list.value
      resourceStore.storeMap.expert.paginate = paginate.value
      resourceStore.storeMap.expert.industry = industry.value
      resourceStore.storeMap.expert.keyword = keyword.value
      console.log('存入数据', resourceStore.storeMap)
    } catch (e) {
      console.log(e)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    if (!resourceStore.storeMap.expert) {
      resourceStore.addKey('expert')
      expertList()
    } else {
      list.value = resourceStore.storeMap.expert.list
      paginate.value = resourceStore.storeMap.expert.paginate
      industry.value = resourceStore.storeMap.expert.industry
      keyword.value = resourceStore.storeMap.expert.keyword
      nextTick(() => {
        listComRef.value.listRef.scrollTo({
          top: resourceStore.storeMap.expert.y,
        })
      })
    }
  })

  onBeforeUnmount(() => {
    resourceStore.storeMap.expert.y = listComRef.value.y
  })

  function onSearch(val) {
    keyword.value = val
    list.value = []
    expertList({
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      params: {
        keyword: val,
        industryChains: industry.value.value ? [industry.value.value] : [],
      },
    })
  }

  function onDown() {
    expertList({
      page: {
        pageNum: paginate.value.page + 1,
        pageSize: 10,
      },
      params: {
        keyword: keyword.value,
        industryChains: industry.value.value ? [industry.value.value] : [],
      },
    })
  }

  function onItemClick(item) {
    router.push({
      path: '/precise/except-detail',
      query: {
        id: item.bid,
      },
    })
  }
</script>
