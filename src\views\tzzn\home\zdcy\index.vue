<template>
  <div class="main_page">
    <div class="list">
      <div
        v-for="item in list"
        :key="item.path"
        class="item"
        @click="navigate(item.path)">
        <img
          :src="item.img"
          alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import cydt from '@/assets/images/tzzn/zdcy/cydt.png'
import cygj from '@/assets/images/tzzn/zdcy/cygj.png'
import cytx from '@/assets/images/tzzn/zdcy/cytx.png'
import qymp from '@/assets/images/tzzn/zdcy/qymp.png'
import { useRouter } from 'vue-router'

const router = useRouter()

const list = [
  { path: 'qymp', img: qymp },
  { path: 'cytx', img: cytx },
  { path: 'cygj', img: cygj },
  { path: 'cydt', img: cydt },
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    gap: 16px;

    .item {
      width: 163px;
      height: 190px;
    }
  }
}
</style>
