// 招商资源库接口
import { get, post, put, remove } from '@/utils/alova/index'

const baseUrl = import.meta.env.VITE_API_RECOMMEND_BASE_URL

/**
 * 获取招商资源库列表
 * @param {*} data
 * @returns
 */
export function getInvestmentCount(data) {
  return get(`${baseUrl}/investmentDatabase/count`, data, {
    cacheFor: 8000, // 缓存时间（毫秒） 10分钟
  })
}

/**
 * 获取机构列表
 * @param {*} data
 * @returns
 */
export function getOrganList(data) {
  return get(`${baseUrl}/investmentDatabase/list`, data)
}

/**
 * 招商资源库增加机构信息
 * @param {*} data
 * @returns
 */
export function addOrg(data) {
  return post(`${baseUrl}/investmentDatabase/save`, data)
}

/**
 * 删除机构信息
 * @param {*} data
 * @returns
 */
export function deleteOrg(data) {
  return remove(`${baseUrl}/investmentDatabase/delete`, data)
}

/**
 * 获取机构详情信息
 * @param {*} data
 * @returns
 */
export function getOrgDetail(data) {
  return get(`${baseUrl}/investmentDatabase/detail`, data)
}

/**
 * 修改机构信息
 * @param {*} data
 * @returns
 */
export function updateOrgInfo(data) {
  return put(`${baseUrl}/investmentDatabase/update`, data)
}
