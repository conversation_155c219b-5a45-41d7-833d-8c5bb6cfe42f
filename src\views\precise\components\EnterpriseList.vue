<template>
  <div
    ref="listRef"
    class="list">
    <div
      v-for="companyItem in list"
      :key="companyItem.companyId"
      class="list_item"
      @click.stop="
        () => {
          emits('itemClick', companyItem)
        }
      ">
      <div class="list_item_header">
        <div class="list_item_title">
          <div class="pr-[20px] text-[16px] font-[600]">
            {{ companyItem.companyName }}
          </div>
        </div>
        <div class="text-[rgba(0,0,0,0.6) text-[12px] leading-[20px]">
          {{ companyItem.province }}
          {{
            `${companyItem.province === companyItem.city || !companyItem.city ? '' : `/ ${companyItem.city}`}`
          }}
          {{
            companyItem?.area || companyItem?.district
              ? ' / ' + (companyItem?.area || companyItem?.district)
              : ''
          }}
        </div>
      </div>
      <div class="list_item_content">
        <div
          v-show="companyItem.companyTags?.length"
          class="tag_box mt-[8px] flex items-center gap-x-[6px] overflow-x-auto">
          <div
            v-for="(tag, tagIdx) in companyItem.companyTags"
            :key="tagIdx"
            :style="{
              backgroundColor: colorList[tagIdx]?.bg || colorList.at(-1).bg,
              color: colorList[tagIdx]?.text || colorList.at(-1).text,
            }"
            class="flex-shrink-0 rounded-[3px] px-[8px] py-[2px] text-[12px]">
            {{ tag }}
          </div>
        </div>
      </div>
      <div
        class="fav"
        @click.stop="
          () => {
            onFav(companyItem)
          }
        ">
        <i
          class="iconfont text-[18px]"
          :class="companyItem?.stared ? 'icon-StarFilled' : 'icon-StarOutlined'"
          :style="
            companyItem?.stared
              ? getTextGradient(['#FFE292', '#FF9C4A'], 'top')
              : { color: 'rgba(0, 0, 0, 0.1)' }
          "></i>
        <div class="fav_text">{{ companyItem?.stared ? '取消' : '收藏' }}</div>
      </div>
    </div>
    <div
      v-show="loading"
      class="mt-[16px] flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
      <Loading v-show="loading">加载中...</Loading>
    </div>
    <div
      v-show="!loading && list.length === 0"
      class="flex justify-center rounded-[8px] bg-[#fff] px-[16px] py-[8px]">
      暂无数据
    </div>
  </div>
</template>

<script setup>
  import { getTextGradient } from '@/utils'
  import { colorList } from '@/views/recommend/options'
  import { useInfiniteScroll, useScroll } from '@vueuse/core'
  import { Loading } from 'vant'
  import { nextTick, onMounted, ref, watch } from 'vue'

  const props = defineProps({
    list: {
      type: Array,
      default: () => [],
    },
    canLoad: {
      type: Boolean,
      default: true,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    isAccept: {
      type: Boolean,
      default: false,
    },
    isAssociation: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    scrollKey: {
      type: String,
      default: '',
    },
    defaultScroll: {
      type: Number,
      default: 0,
    },
    handleIndustryList: {
      type: Function,
      default: (list) => list,
    },
  })

  const emits = defineEmits([
    'edit',
    'accept',
    'down',
    'itemClick',
    'association',
    'scroll-y',
    'handle-fav',
  ])
  const listRef = ref(null)
  const { y } = useScroll(listRef)

  watch(y, (nv) => {
    emits('scroll-y', nv)
  })

  onMounted(() => {
    nextTick(() => {
      listRef.value?.scrollTo({
        top: props.defaultScroll,
      })
    })
  })

  useInfiniteScroll(
    listRef,
    () => {
      emits('down')
    },
    {
      distance: 10,
      interval: 100,
      canLoadMore: () => props.canLoad,
    },
  )

  const cancelItem = ref(null)

  function onConfirm() {
    emits('handle-fav', cancelItem.value, '')
  }

  function onFav(item) {
    if (item?.stared) {
      cancelItem.value = item
      onConfirm()
    } else {
      cancelItem.value = item
      emits('handle-fav', item)
    }
  }
</script>

<style lang="scss" scoped>
  .item_handle {
    display: flex;
    align-items: center;
    padding: 2px 16px;
    border-radius: 9999px;
    background: linear-gradient(to left, #6093ff 0%, #1255e4 99%);
    color: #fff;
    font-size: 12px;
  }

  .list {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    background-color: #f7f8fa;

    .tag_box {
      &::-webkit-scrollbar {
        display: none;
      }
    }
  }

  .reason_item:last-child {
    .reason_split {
      display: none;
    }
  }

  .list_item {
    position: relative;
    border-radius: 12px;
    background-color: #fff;

    .fav {
      display: flex;
      position: absolute;
      top: 8px;
      right: 12px;
      flex-direction: column;
      align-items: center;

      .fav_text {
        color: #9e9e9e;
        font-size: 12px;
      }
    }

    &_header {
      position: relative;
      padding: 12px 16px 0;

      .list_item_title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 8px;
      }

      &_score {
        position: absolute;
        top: 0;
        right: 0;
        padding: 4px 8px;
        border-radius: 0 12px;
        background-color: #1677ff;
        color: #e6f4ff;
        font-size: 12px;
      }
    }

    &_content {
      padding: 8px 12px 12px;

      .local_button {
        color: #1677ff;
        font-size: 12px;
      }
    }

    &_footer {
      display: flex;
      align-items: center;
      height: 36px;
      margin: 10px -16px 0;
      padding: 0 16px;
      border-radius: 0 0 12px 12px;
      background: rgb(0 0 0 / 6%);
      font-size: 12px;

      .types {
        display: flex;
        flex: 1;
        overflow-x: auto;
        gap: 0 8px;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    & + .list_item {
      margin-top: 16px;
    }
  }
</style>
