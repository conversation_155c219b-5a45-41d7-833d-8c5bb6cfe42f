<template>
  <div class="recommend_box">
    <Tabs
      v-model:active="isActive"
      lazy-render>
      <Tab title="未承接">
        <NotAcceptedList v-if="isActive === 0" />
      </Tab>
      <Tab title="已承接">
        <AcceptedList v-if="isActive === 1" />
      </Tab>
    </Tabs>
  </div>
</template>

<script setup>
  import { Tab, Tabs } from 'vant'
  import { onMounted, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import AcceptedList from './components/AcceptedList.vue'
  import NotAcceptedList from './components/NotAcceptedList.vue'

  const router = useRouter()
  const route = useRoute()

  const isActive = ref(0)

  watch(isActive, () => {
    router.replace(
      `/recommend?tab=${isActive.value}&userName=${route.query.userName}&token=${route.query.token}`,
    )
  })

  onMounted(() => {
    isActive.value = Number(route.query.tab || 0)
  })
</script>
