<template>
  <div class="main_page">
    <div class="list">
      <div
        v-for="item in list"
        :key="item.path"
        class="item"
        @click="navigate(item.path)">
        <img
          :src="item.img"
          alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import cdqq from '@/assets/images/tzzn/fzys/cdqq.png'
import cxyd from '@/assets/images/tzzn/fzys/cxyd.png'
import hlbf from '@/assets/images/tzzn/fzys/hlbf.png'
import jjgx from '@/assets/images/tzzn/fzys/jjgx.png'
import jydj from '@/assets/images/tzzn/fzys/jydj.png'
import zzgd from '@/assets/images/tzzn/fzys/zzgd.png'
import { useRouter } from 'vue-router'

const router = useRouter()

const list = [
  { path: 'jydj', img: jydj },
  { path: 'hlbf', img: hlbf },
  { path: 'cxyd', img: cxyd },
  { path: 'zzgd', img: zzgd },
  { path: 'cdqq', img: cdqq },
  { path: 'jjgx', img: jjgx },
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    gap: 16px;

    .item {
      width: 163px;
      height: 190px;
    }
  }
}
</style>
