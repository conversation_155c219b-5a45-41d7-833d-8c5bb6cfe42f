// 政务端接口
import { get, post, put, remove } from '@/utils/alova'
import httpClient from '@/utils/http'
const baseUrl = import.meta.env.VITE_API_RECOMMEND_BASE_URL
const schemeDetailBaseUrl = import.meta.env.VITE_SCHEME_URL
// 目标企业

/**
 * 收藏
 */
export const starEnt = (param) =>
  httpClient.post(`${baseUrl}/api/v1/company/card/star`, param)

/**
 * 取消收藏
 */
export const unStarEnt = (param) =>
  httpClient.post(`${baseUrl}/api/v1/company/card/unstar`, param)

/**
 * 分页列表
 */
export const targetPage = (param) =>
  httpClient.post(`${baseUrl}/api/v1/company/card/page`, param)

/**
 * 专家库
 */
export const expertListApi = (params) =>
  httpClient.post(`/api/v1/normal/expert/list`, params)

/**
 * 专家详情
 */
export const expertDetail = (id) =>
  httpClient.get(`/api/v1/normal/expert/${id}`)

/**
 * 机构库
 */
export const institutionListApi = (params) =>
  httpClient.post(`/api/v1/normal/investment`, params)
/**
 * 机构库 区
 */
export const citiesOption = (params) =>
  httpClient.get(`/api/v1/normal/investment/cities`, params)

/**
 * 机构详情
 */
export const institutionDetail = (id) =>
  httpClient.get(`/api/v1/normal/investment/${id}`)

/**
 * 获取收藏列表
 */
export const getFavListByUid = (uid) =>
  httpClient.get(`${baseUrl}/api/v1/company/card/companyIds`, { userBid: uid })

/*
 * 获取产业链树列表数据
 * @param {*} param
 * @returns
 */
export const getChainTreeList = (param) =>
  httpClient.get(`${baseUrl}/api/v1/atlas/treeList`, param)

/**
 * 根据节点获取公司信息列表
 */
export const getCompanyListByNode = (param) =>
  httpClient.post(`${baseUrl}/api/v1/atlas/companies`, param)

/**
 * 获取统计数据
 */
export const getCountByKey = (key) =>
  httpClient.post(`/api/v1/${key}/query/count`)

/**
 * 绑定用户
 */
export const bindUserByUid = (uid) =>
  httpClient.post(`/api/v1/userinfo/bindOrUpdate`, { userBid: uid })

/**
 * 电话计数
 */
export const phoneClickCount = (params) =>
  httpClient.post(`/api/open/updatePhone`, params)

/**
 * 方案详情
 */
export const getSchemeDetail = (params) =>
  httpClient.get(`${schemeDetailBaseUrl}/view`, params)

/**
 * 产业政策
 */
export const getIndustryPolicy = () => httpClient.get('/api/open/board/policy')

/**
 * 创新资源
 */
export const getInnovationResource = () =>
  httpClient.get('/api/open/board/innovationResource')

/**
 * 产业园区
 */
export const getindustrialPark = () => httpClient.get('/api/open/board/park')

/**
 * 商务楼宇
 */
export const getCommercialBuilding = () =>
  httpClient.get('/api/open/board/building')

/**
 * 应用场景
 */
export const getApplicationScenario = () =>
  httpClient.get('/api/open/board/scene')

/**
 * 园区行政区筛选
 */
export const getParkAreaList = () =>
  httpClient.get('/api/v1/normal/park/districts')

/**
 * 楼宇行政区筛选
 */
export const getBuildAreaList = () =>
  httpClient.get('/api/v1/normal/building/districts')

// 机构类型
export const getTypeOfOrganization = () =>
  httpClient.get('/api/v1/normal/investment/types')

/** 获取共享人列表 */
export const getShareTargetList = (params) =>
  get(`${baseUrl}/api/v1/company/card/shareCandidates`, params)

/** 共享 */
export const onShareSubmit = (params) =>
  post(`${baseUrl}/api/v1/company/card/share`, params)

/**
 * 获取企业类别
 */
export const getEntTypeList = () =>
  httpClient.get(`${baseUrl}/api/v1/company/card/companyTypes`)

/** 查看收藏的企业信息 */
export const getCompanyDetailByFav = (bid) =>
  get(`${baseUrl}/api/v1/company/card/${bid}`)

/** 更新企业信息 */
export const updateFavInfo = (params) =>
  post(`${baseUrl}/api/v1/company/card/update`, params)

/** 新增企业信息 */
export const addFavInfo = (params) =>
  post(`${baseUrl}/api/v1/company/card`, params)

/** 获取跟进记录 */
export const getRecords = (params) =>
  post(`${baseUrl}/api/v1/company/card/records/page`, params)

/** 新增跟进记录 https://tcj-erecommend-api.youpin-k8s.net/doc.html#/default/%E8%87%AA%E5%AE%9A%E4%B9%89%E4%BC%81%E4%B8%9A%E5%8D%A1%E7%89%87(%E7%9B%AE%E6%A0%87%E4%BC%81%E4%B8%9A)/addRecord*/
export const addRecord = (params) =>
  post(`${baseUrl}/api/v1/company/card/records`, params)

/** 删除跟进记录 */
export const deleteRecord = (bid) =>
  remove(`${baseUrl}/api/v1/company/card/records/${bid}`)

/** 查看跟进记录详情 */
export const getRecordDetail = (bid) =>
  get(`${baseUrl}/api/v1/company/card/records/${bid}`)

/** 编辑跟进记录 */
export const editRecord = (params) =>
  put(`${baseUrl}/api/v1/company/card/records`, params)

/** 操作记录 */
export const getHistory = (params) =>
  post(`${baseUrl}/api/v1/company/card/operations/page`, params)
