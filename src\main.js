import '@/assets/style/index.scss'
import '@/assets/style/tailwindecss.css'
import { createPinia } from 'pinia'
import { Lazyload } from 'vant'
import 'vant/lib/index.css'
import { createApp } from 'vue'
import { VuePageStackPlugin } from 'vue-page-stack'
import App from './App.vue'
import vChart from './plugins/v-charts'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Lazyload)
app.use(vChart)
app.use(VuePageStackPlugin, { router })
app.mount('#app')
