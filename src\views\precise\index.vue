<template>
  <RouterView />
</template>

<script setup>
  import { onBeforeMount } from 'vue'
  import { useRoute } from 'vue-router'

  import { usePreciseStore } from '@/stores/percise'
  const route = useRoute()

  const preciseStore = usePreciseStore()
  const localUid = localStorage.getItem('userBid')
  const localToken = localStorage.getItem('userToken')

  const queryUid = route.query.uid
  const queryToken = route.query.token

  // 获取uid并存入缓存
  onBeforeMount(() => {
    // document.title = '投资武汉'
    if (queryUid) {
      localStorage.setItem('userBid', queryUid)
      localStorage.setItem('userToken', queryToken)
      preciseStore.uid = queryUid
      preciseStore.token = queryToken || ''
    } else {
      preciseStore.uid = localUid
      preciseStore.token = localToken || ''
    }
  })
</script>
