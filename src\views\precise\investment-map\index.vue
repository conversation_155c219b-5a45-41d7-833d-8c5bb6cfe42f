<template>
  <div class="investment_map">
    <header>
      <DropdownMenu>
        <DropdownItem
          v-model="industryCategory"
          :options="industryOptions" />
      </DropdownMenu>
    </header>
    <div class="main">
      <TreeMap
        :selected-code="industryCategory"
        :selected-chain-data="selectedIndustryTree"
        @handle-node-click="handleNodeClick" />
    </div>

    <!-- 底部弹出 -->
    <CompanyListPopup
      v-model="popupShow"
      :cur-node="selectedNode"
      @on-popup-closed="selectedNode = {}" />
  </div>
</template>

<script setup>
  import { getChainTreeList } from '@/apis/precise'
  import { isEmpty } from '@/utils/is'
  import {
    closeToast,
    DropdownItem,
    DropdownMenu,
    showLoadingToast,
  } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import CompanyListPopup from './company-list-popup.vue'
  import TreeMap from './tree-map.vue'

  // 选择的产业类别
  const industryCategory = ref('')

  // 产业链分类列表
  const industryTreeList = ref([])
  async function getIndustryOptions() {
    try {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
      })
      const {
        data: { data: res },
      } = await getChainTreeList()
      industryTreeList.value = res

      if (res && res.length > 0) {
        industryCategory.value = res[0].id
      }
    } catch (error) {
      console.log(error)
    }
    closeToast()
  }

  onMounted(() => {
    // 获取产业链分类列表数据
    getIndustryOptions()
  })

  // 产业链分类列表选项
  const industryOptions = computed(() => {
    if (!industryTreeList.value && industryTreeList.value.length === 0)
      return []

    return industryTreeList.value.map((item) => ({
      value: item.id,
      text: item.chainName,
      ...item,
    }))
  })

  // 当前选中产业链的树的前四层
  const selectedIndustryTree = computed(() => {
    if (!industryTreeList.value || industryTreeList.value.length === 0)
      return []

    let curTree = industryTreeList.value.find(
      (item) => item.id === industryCategory.value,
    )

    if (!curTree) return {}

    // 递归处理每个节点，保留前四层
    const processNode = (node, currentLevel) => {
      if (currentLevel > 5) {
        return null // 超过四层的节点不保留
      }
      // 创建新节点，复制原属性
      const newNode = { ...node }
      // 处理子节点，确保children是数组
      const children = node.children || []
      newNode.children = children.map((child) =>
        processNode(child, currentLevel + 1),
      ) // 递归处理子节点

      if (newNode.children.every((child) => child === null)) {
        // 如果子节点中有null，则删除该节点的children属性
        delete newNode.children
      }

      // 当前节点下有子节点，且子节点没有children，则在右侧显示label
      if (
        !isEmpty(newNode.children) &&
        newNode.children.every((child) => isEmpty(child.children))
      ) {
        newNode.children.forEach((child) => {
          child.labelPlacementRight = true
        })
      }

      return newNode
    }

    // 处理根节点列表（根节点层级为1）
    curTree = processNode(curTree, 1)
    curTree.isRoot = true

    // 更新节点Id, 防止重复
    // curTree = TreeUtils.updateNode(
    //   curTree,
    //   () => true,
    //   (pNode, node) => {
    //     return {
    //       ...node,
    //       // id: nanoid(),
    //     }
    //   },
    // )

    return curTree
  })

  // 显示企业列表弹窗
  const popupShow = ref(false)
  // 当前选中节点
  const selectedNode = ref({})

  // 节点点击
  async function handleNodeClick(node) {
    await new Promise((resolve) => setTimeout(resolve, 0))
    popupShow.value = true
    selectedNode.value = node
  }
</script>

<style lang="scss" scoped>
  .investment_map {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;

    & > header {
      flex: 0 0 55px;
    }

    .main {
      flex: 1;
      padding: 5px;
    }
  }
</style>
