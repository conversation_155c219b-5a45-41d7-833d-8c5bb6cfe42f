<template>
  <div class="resource">
    <img
      width="100%"
      height="120px"
      src="@/assets/images/enterprise-side/opportunity.png" />
    <div class="content">
      <div
        v-for="(item, index) in buttons"
        :key="index"
        class="content_button">
        <div
          class="flex items-center"
          @click="jumpTo(item.path)">
          <div
            class="flex h-[36px] w-[36px] flex-shrink-0 items-center justify-center rounded-[8px] bg-white">
            <i
              class="iconfont text-[20px]"
              :style="item.style"
              :class="[item.icon]"></i>
          </div>
          <span class="ml-[8px] flex-shrink-0">{{ item.title }}</span>
        </div>
        <div
          v-show="item.hasNumber"
          class="number">
          {{ item.number || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getTextGradient } from '@/utils'
  import { useCount } from '@/utils/precise'
  import { computed, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const countObj = ref({})

  const countAll = [
    'expert',
    'policy',
    'park',
    'building',
    'scene',
    'investment',
    'innovate',
    'research',
    'colleges',
  ]

  onMounted(() => {
    Promise.all(
      countAll.map((item) => {
        useCount(item, {
          onSuccess({ data: { data: res } }) {
            countObj.value[item] = res
          },
        })
      }),
    )
  })

  const buttons = computed(() => [
    {
      icon: 'icon-policy',
      title: '投资政策',
      style: getTextGradient(['#74A8FE', '#547AF7'], '135deg'),
      path: {
        path: '/enterprise-side/policy',
        query: {
          key: '投资政策',
          noFooter: 1,
        },
      },
      hasNumber: true,
      number: countObj.value?.policy,
    },
    {
      icon: 'icon-Park',
      title: '产业园区',
      style: getTextGradient(['#FFCA65', '#F6AB52'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'park',
          key: '产业园区',
          noFooter: 1,
        },
      },
      hasNumber: true,
      number: countObj.value?.park,
    },
    {
      icon: 'icon-enterprise',
      title: '商务楼宇',
      style: getTextGradient(['#78F1D9', '#2BF4C3'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'building',

          noFooter: 1,
        },
      },
      hasNumber: true,
      number: countObj.value?.building,
    },
    {
      icon: 'icon-resource',
      title: '应用场景',
      style: getTextGradient(['#D8B0FF', '#A953FF'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'scene',
          noFooter: 1,
          key: '应用场景',
        },
      },
      hasNumber: true,
      number: countObj.value?.scene,
    },
    {
      icon: 'icon-cxzy',
      title: '创新资源',
      style: getTextGradient(['#8396FF', '#634EEA'], '135deg'),
      path: {
        path: '/precise/innovate',
        query: {
          noFooter: 1,
        },
      },
      hasNumber: true,
      number:
        (+countObj.value?.innovate || 0) +
        (+countObj.value?.research || 0) +
        (+countObj.value?.colleges || 0),
    },
  ])

  function jumpTo(path) {
    router.push(typeof path === 'string' ? { path } : path)
  }
</script>

<style lang="scss" scoped>
  .resource {
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    .content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      padding: 0 16px;
      gap: 12px;
      font-size: 14px;

      .content_button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 12px;
        border: 1px solid #fff;
        border-radius: 10px;
        background: linear-gradient(135deg, #eef8ff 0%, #fff 100%);
      }
    }
  }
</style>
