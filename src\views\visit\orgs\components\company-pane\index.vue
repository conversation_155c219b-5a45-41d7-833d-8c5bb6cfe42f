<template>
  <div class="pane">
    <header>
      <Search
        v-model="searchForm.keyword"
        shape="round"
        placeholder="请输入搜索关键词"
        @search="onSearch"
        @clear="onSearch" />
      <DropdownMenu>
        <DropdownItem
          v-if="visitUserStore.isZwb"
          v-model="visitIndexStore.region"
          :title="visitIndexStore.region ? visitIndexStore.region : '区'"
          :options="allRegionOptions" />
        <DropdownItem
          v-model="visitIndexStore.industry"
          :title="visitIndexStore.industry ? visitIndexStore.industry : '产业'"
          :options="allIndustryOptions" />
      </DropdownMenu>
    </header>
    <List
      v-model:loading="loading"
      :finished="isLastPage && !loading"
      finished-text="没有更多了"
      :immediate-check="false"
      @load="onLoad">
      <div class="pl-[16px] pr-[16px] pt-[16px]">
        <CompanyCard
          v-for="item in data"
          :key="item.bid"
          :item="item"
          :has-action="false"
          :show-share-person="false"
          :show-area="true"
          @on-detail="handleDetail" />
      </div>
    </List>
  </div>
</template>

<script setup>
  import { getVisitCompanyBoard } from '@/apis/visit'
  import { useDict } from '@/stores/dict'
  import { useVisitIndexStore, useVisitUserStore } from '@/stores/visit'
  import { usePagination } from 'alova/client'
  import { DropdownItem, DropdownMenu, List, Search } from 'vant'
  import { computed, reactive, toRef } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePickerOptions } from '../../hook'
  import CompanyCard from '../company-card.vue'

  const { industryOptions } = usePickerOptions()
  const { areaOptions } = useDict('areaOptions')
  // 产业链
  const allIndustryOptions = computed(() => {
    return [{ text: '全部', value: '' }].concat(industryOptions)
  })
  const allRegionOptions = computed(() => {
    const options = areaOptions.value.map((item) => ({
      ...item,
      text: item.label,
      value: item.value,
    }))
    return [{ text: '全部', value: '' }].concat(options)
  })

  const visitUserStore = useVisitUserStore()
  const visitIndexStore = useVisitIndexStore()
  // 搜索表单
  const searchForm = reactive({
    keyword: '',
  })

  // 组件加载时 如果当前用户不是驻外办 则为自己的区域
  if (!visitUserStore.isZwb) {
    visitIndexStore.region = visitUserStore.userInfo.areaName || ''
  }

  // 点击搜索
  const onSearch = () => {
    reload()
  }

  const {
    // 加载状态
    loading,

    // 列表数据
    data,

    // 是否为最后一页
    // 下拉加载时可通过此参数判断是否还需要加载
    isLastPage,

    // 当前页码，改变此页码将自动触发请求
    page,

    // 重载
    reload,
  } = usePagination(
    // Method实例获取函数，它将接收page和pageSize，并返回一个Method实例
    (page, pageSize) =>
      getVisitCompanyBoard({
        uid: visitUserStore.uid,
        pageNum: page,
        pageSize,
        industryCategory: visitIndexStore.industry,
        areaName: visitIndexStore.region,
        keyword: searchForm.keyword,
      }),
    {
      // 请求前的初始数据（接口返回的数据格式）
      initialData: {
        total: 0,
        data: [],
      },
      initialPage: 1, // 初始页码，默认为1
      initialPageSize: 10, // 初始每页数据条数，默认为10
      total: (response) => Number(response.total),
      data: (response) => response.records,
      // 追加模式
      append: true,
      // 监听
      watchingStates: [
        toRef(visitIndexStore, 'industry'),
        toRef(visitIndexStore, 'region'),
      ],
    },
  )
  // 加一页
  function onLoad() {
    page.value = page.value + 1
  }

  const router = useRouter()
  // 查看
  const handleDetail = (id) => {
    router.push(`/visit/org-detail/${id}`)
  }

  // const formStore = useVisitOrgFormStore()
  // onActivated(() => {
  //   // 有过更新操作，重新加载数据列表。
  //   if (formStore.formActionType) {
  //     refresh(1)
  //     formStore.formActionType = ''
  //   }
  // })
</script>

<style lang="scss" scoped>
  .pane {
    --van-dropdown-menu-shadow: none;
  }
</style>
