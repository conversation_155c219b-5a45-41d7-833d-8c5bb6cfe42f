<template>
  <div class="h-full w-full bg-[#f7f8fa]">
    <img
      src="@/assets/images/enterprise-side/consulting.png"
      width="100%"
      height="120px" />
    <div class="m-[16px]">
      <div class="title">武汉市招商咨询电话</div>
      <div class="mt-[8px] rounded-[8px] bg-white">
        <a
          v-for="(item, index) in phoneList"
          :key="index"
          :href="`tel:${item.phone}`"
          class="item"
          @click="useCall(item.phone)">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div
                class="flex h-[24px] w-[24px] items-center justify-center rounded-[50%] bg-[rgba(76,133,255,0.06)]">
                <i
                  class="iconfont icon-xt_PhoneOutlined text-[12px] text-[#4C85FF]"></i>
              </div>
              <div class="ml-[8px] text-[14px]">
                {{ item.name }}
              </div>
            </div>
            <div class="text-[14px] text-[rgba(0,0,0,0.6)]">
              {{ item.phone }}
            </div>
          </div>
          <div
            v-if="item.content"
            class="text-[14px] leading-[22px] text-[rgba(0,0,0,0.26)]">
            {{ item.content }}
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useCall } from '@/utils/ent'

  const phoneList = [
    {
      name: '市投资促进中心',
      phone: '027-82792795',
    },
    {
      name: '投资促进一部',
      phone: '027-82790861',
      content: '光电子信息、人工智能、大数据与云计算、网络安全',
    },
    {
      name: '投资促进二部',
      phone: '027-82796705',
      content: '汽车产业、装备制造、能源环保、精细化工、新材料、航空航天',
    },
    {
      name: '投资促进三部',
      phone: '027-82221400',
      content:
        '楼宇经济、商贸流通、现代物流、跨境电商、现代都市农业、建筑业、公共基础设施',
    },
    {
      name: '投资促进四部',
      phone: '027-82792059',
      content: '大健康、金融保险、文化旅游、教育体育等现代服务业',
    },
  ]
</script>

<style lang="scss" scoped>
  .title {
    color: rgb(0 0 0 / 40%);
    font-size: 16px;
    line-height: 24px;
  }

  .item {
    display: block;
    padding: 16px;
    border-bottom: 1px solid rgb(0 0 0 / 6%);

    &:last-child {
      border: none;
    }
  }
</style>
