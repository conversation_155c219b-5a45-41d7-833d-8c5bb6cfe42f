import { isEmpty } from '@/utils/is'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'

export const UID_KEY = 'visit_uid'
export const VISIT_USER_KEY = 'visit_user'
export const useVisitUserStore = defineStore('visitUser', () => {
  const defaultUid = localStorage.getItem(UID_KEY) || ''
  const uid = ref(defaultUid)
  const userStr = localStorage.getItem(VISIT_USER_KEY) || '{}'
  const userInfo = ref(JSON.parse(userStr))

  function setUid(newUid) {
    uid.value = newUid
    localStorage.setItem(UID_KEY, newUid)
  }
  function setUserInfo(value) {
    userInfo.value = value
    localStorage.setItem(VISIT_USER_KEY, JSON.stringify(value))
  }

  // 是否存在权限
  const hasPermission = computed(() => {
    if (isEmpty(userInfo.value) || isEmpty(userInfo.value.roles)) {
      return false
    }

    return true
  })

  /**
   * - 对象数组中某一对象的code是zwbzq区，zwbzqsj市(就是isZwb)
   * - 可能存在全有的情况，取zwbzqsj
   */
  const isZwb = computed(() => {
    if (isEmpty(userInfo.value) || isEmpty(userInfo.value.roles)) {
      return false
    }

    // 优先检查是否有市级角色
    const hasCityRole = userInfo.value.roles.some(
      (role) => role.code === 'zwbzqsj',
    )
    if (hasCityRole) {
      return true
    }
    return false
  })

  return {
    uid,
    userInfo,
    isZwb,
    hasPermission,
    setUid,
    setUserInfo,
  }
})

export const useVisitIndexStore = defineStore('visitIndex', () => {
  const activeTabIndex = ref(0)
  const region = ref('')
  const industry = ref('')

  return {
    activeTabIndex,
    region,
    industry,
  }
})

/**
 * 走访企业库添加企业表单
 */
export const useVisitOrgFormStore = defineStore('visitOrgForm', () => {
  function initForm() {
    return {
      companyName: '',
      industryCategory: '',
      companyCategory: '',
      contactPersonName: '',
      contactPersonPost: '',
      contactPersonInformation: '',
      isChushang: '', // 是否楚商_0是1否
      chushangInformation: '', // 楚商信息
      visitBasicInformationDTOList: [], // 走访信息记录
    }
  }

  const formData = ref(initForm())
  // 添加表单和更新表单后的动作类型，added或edited
  const formActionType = ref('')

  function $reset() {
    formData.value = initForm()
    formActionType.value = ''
  }

  return {
    formData,
    formActionType,
    $reset,
  }
})
