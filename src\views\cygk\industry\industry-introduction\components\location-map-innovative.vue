<!-- 创新资源点位图 -->
<template>
  <div class="container">
    <div class="h-[330px] w-full">
      <img
        class="h-full w-full"
        :src="currentImage"
        @click="show = true" />
    </div>
    <div class="bottom">
      <div class="bg-[#fff] pb-[10px]">
        <div class="h-[226px] w-full">
          <VChart
            autoresize
            :option="barOption"
            class="h-full w-full"></VChart>
        </div>
      </div>
    </div>
  </div>
  <ImagePreview
    v-model:show="show"
    :images="[currentImage]"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import allData from '@/assets/js/cyjs'
  import { extractFileName } from '@/utils/index'
  import { ImagePreview } from 'vant'
  import { computed, inject, ref } from 'vue'
  const currentType = inject('currentType')
  const currentData = computed(() => {
    return Object.values(allData).find((item) => {
      return item.data.cyfl.includes(currentType.value)
    })
  })
  const currentImage = computed(() => {
    const imagesPath = currentData.value.data['cxzydwt']
    if (!imagesPath) return undefined
    const _thisImagesName = extractFileName(imagesPath)

    return new URL(`./images/${_thisImagesName}`, import.meta.url).href
  })
  const show = ref(false)
  const barOption = computed(() => ({
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '60%'],
        // adjust the start and end angle
        data: currentData.value.data.cxzy_data.map((item) => ({
          value: item.litres,
          name: item.country,
        })),
      },
    ],
  }))
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 10px;
    background-color: #f7f8fa;

    .bottom {
      width: 100%;
      margin-top: 16px;
      padding: 16px;
      background-color: #f7f8fa;
    }
  }
</style>
