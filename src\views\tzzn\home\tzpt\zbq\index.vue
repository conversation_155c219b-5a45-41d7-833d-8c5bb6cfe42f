<template>
  <div class="main_page">
    <div class="list">
      <div @click="navigate(item.path)" class="item" v-for="item in list" :key="item.path">
        <img style="width: 100%" :src="item.img" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import dh from '@/assets/images/tzzn/zbq/dh.png'
import whxg from '@/assets/images/tzzn/zbq/whxg.png'
import whjk from '@/assets/images/tzzn/zbq/whjk.png'

const router = useRouter()

const list = [
  { path: 'dh', img: dh },
  { path: 'whxg', img: whxg },
  { path: 'whjk', img: whjk }
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    padding: 16px;
    flex-direction: column;
    gap: 16px;
  }
}
</style>
