<template>
  <div class="main_page">
    <div class="list">
      <div @click="navigate(item.path)" class="item" v-for="item in list" :key="item.path">
        <img :src="item.img" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import whxc from '@/assets/images/tzzn/tzpt/whxc.png'
import cjxq from '@/assets/images/tzzn/tzpt/cjxq.png'
import jsxc from '@/assets/images/tzzn/tzpt/jsxc.png'
import zmq from '@/assets/images/tzzn/tzpt/zmq.png'
import zbq from '@/assets/images/tzzn/tzpt/zbq.png'
import tscyyq from '@/assets/images/tzzn/tzpt/tscyy.png'

const router = useRouter()

const list = [
  { path: 'whxc', img: whxc },
  { path: 'cjxq', img: cjxq },
  { path: 'jsxc', img: jsxc },
  { path: 'zmq', img: zmq },
  { path: 'zbq', img: zbq },
  { path: 'tscyyq', img: tscyyq }
]

const navigate = (path) => {
  router.push(`/tzzn/${path}`)
}
</script>

<style lang="scss" scoped>
.main_page {
  width: 100%;
  height: 100%;
  .list {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    gap: 16px;

    .item {
      width: 163px;
      height: 190px;
    }
  }
}
</style>
