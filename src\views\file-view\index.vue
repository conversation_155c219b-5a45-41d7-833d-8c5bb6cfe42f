<template>
  <div class="h-full w-full">
    <iframe
      class="file_view"
      :src="url"
      frameborder="0"></iframe>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const url = ref(
    `${import.meta.env.VITE_KKFILE_BASE_URL}?url=${btoa(encodeURIComponent(route.params.url))}`,
  )
</script>

<style lang="scss" scoped>
  .file_view {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    border: none;
  }
</style>
