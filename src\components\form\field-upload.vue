<template>
  <Field
    :name="name"
    :label="label">
    <template #input>
      <Uploader
        :model-value="files"
        :readonly="readonly"
        :deletable="!readonly"
        result-type="file"
        :multiple="multiple"
        :show-upload="!readonly"
        :accept="accept"
        :max-size="maxSizeMb"
        :max-count="maxCount"
        :after-read="afterFileRead"
        @click-preview="onPreview"
        @delete="onDelete"
        @oversize="onOversize" />
    </template>
  </Field>
</template>

<script setup>
  import {
    Uploader,
    Field,
    showFailToast,
    showConfirmDialog,
    showToast,
  } from 'vant'
  import { uploadFile } from '@/apis'
  import { useRequest } from 'alova/client'
  import { isImageUrl } from '@/utils/is'
  import { useRouter } from 'vue-router'

  // 单列选择器
  defineOptions({
    name: 'FieldUpload',
  })

  const props = defineProps({
    name: String,
    label: String,
    position: {
      type: String,
      default: 'bottom',
    },
    placeholder: {
      type: String,
      default: '请上传',
    },
    accept: {
      type: String,
      default: 'image/*,.pdf,.doc,.docx,.xls,.xlsx',
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false,
    },
    // 是否多选图片上传
    multiple: {
      type: Boolean,
      default: false,
    },
    // 文件大小限制（M）
    maxSize: {
      type: Number,
      default: 10,
    },
    // 文件数量限制
    maxCount: {
      type: Number,
      default: 100,
    },
    // 文件预览路由
    previewPath: {
      type: String,
      default: '',
    },
  })

  const router = useRouter()

  // 所选中的选项的值
  const files = defineModel({
    type: [Array],
    required: true,
  })

  // 转为MB
  const maxSizeMb = props.maxSize * 1024 * 1024 // 转成字节

  const { send: fetchUploadFile } = useRequest(uploadFile, {
    immediate: false,
  })

  async function afterFileRead(file) {
    try {
      const url = await fetchUploadFile(file.file)
      files.value = files.value.concat({
        ...file,
        // 必须需要一个url字段，才能在编辑时反填表单时进行预览
        url: url,
      })
      console.log(file, files, url)
    } catch (error) {
      showFailToast('上传文件失败')
      console.log(error, '上传文件失败')
    }
  }

  // 删除文件事件
  function onDelete(file, { index }) {
    showConfirmDialog({
      title: '确认删除',
      message: '确认删除该文件？',
    }).then(() => {
      console.log('删除', file, index)
      files.value = files.value.filter((_, i) => index !== i)
    })
  }

  // 根据是否kkView 决定其他文件是否预览
  function onPreview(file, { index }) {
    const url = file.url

    const isImage = isImageUrl(url)
    // 如果是图片，则直接预览
    if (isImage) return

    // 其他文件则需要跳转到预览页面进行查看
    if (props.previewPath) {
      router.push({
        path: props.previewPath,
        query: {
          url: file.url,
        },
      })
      console.log('预览', file, index)
      return
    }
  }

  // 文件大小超出限制事件
  function onOversize() {
    showToast(`文件大小不能超过 ${props.maxSize}MB`)
  }
</script>

<style lang="scss" scoped></style>
