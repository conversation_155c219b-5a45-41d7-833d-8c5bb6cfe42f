<template>
  <AutoComplete
    v-model="value"
    :fetch-suggestions="fetchCompanyList"
    label="企业名称"
    name="companyName"
    value-key="companyName"
    key-prop="companyId"
    placeholder="请输入企业名称"
    :rules="[
      {
        required: true,
        message: '请填写企业名称',
      },
    ]" />
</template>

<script setup>
  import AutoComplete from '@/components/auto-complete/index.vue'
  import { getRecommendAllList } from '@/apis/visit'
  import { useRequest } from 'alova/client'

  const value = defineModel({
    type: String,
    required: true,
  })

  // 模糊搜索企业列表
  const { send: fetchCompanyList } = useRequest(
    (keyword) =>
      getRecommendAllList({
        keyword: keyword,
      }),
    {
      initialData: [],
      immediate: false,
    },
  )
</script>

<style lang="scss" scoped></style>
