<template>
  <div
    ref="listRef"
    class="history_list">
    <Skeleton
      :loading
      :row="10">
      <Space
        v-if="list.length > 0"
        direction="vertical"
        :size="16"
        fill>
        <CellGroup
          v-for="(item, index) in list"
          :key="index"
          inset>
          <Cell>
            <template #title>
              <h1>{{ item.updatedAt }}</h1>
            </template>
            <template #value>
              <span class="record_add_name">
                {{ item.operatorName }}
              </span>
            </template>
          </Cell>
          <Field
            :model-value="item.fieldName"
            readonly
            label="更改字段" />
          <Field
            :model-value="item.beforeValue"
            readonly
            label="更改前" />
          <Field
            :model-value="item.afterValue"
            readonly
            label="更改后" />
        </CellGroup>
      </Space>
    </Skeleton>
  </div>
</template>

<script setup>
  import { getHistory } from '@/apis/precise'
  import { useInfiniteScroll } from '@vueuse/core'
  import { useRequest } from 'alova/client'
  import dayjs from 'dayjs'
  import { Cell, CellGroup, Field, Skeleton, Space } from 'vant'
  import { onMounted, ref, useTemplateRef } from 'vue'
  import { useRoute } from 'vue-router'

  const listRef = useTemplateRef('listRef')

  const route = useRoute()

  const list = ref([])
  const paginate = ref({
    pageNum: 1,
    pageSize: 10,
    hasNext: false,
  })

  const { send: fetchList, loading } = useRequest(getHistory, {
    immediate: false,
  }).onSuccess(({ data }) => {
    paginate.value = {
      pageNum: data.page,
      hasNext: data.hasNext,
      pageSize: 10,
    }
    const result = []
    data.list.forEach((i) => {
      if (i.operations.length > 0) {
        i.operations.forEach((o) => {
          result.push({
            operatorName: i.operatorName,
            updateByName: i.updateByName,
            updatedAt: dayjs(i.updatedAt).format('YYYY-MM-DD HH:mm'),
            ...o,
          })
        })
      }
    })
    list.value = list.value.concat(result)
  })

  onMounted(() => {
    fetchList({
      page: {
        pageNum: 1,
        pageSize: paginate.value.pageSize,
      },
      params: {
        bid: route.params.id,
      },
    })
  })

  useInfiniteScroll(
    listRef,
    () => {
      fetchList({
        page: {
          pageNum: paginate.value.pageNum + 1,
          pageSize: paginate.value.pageSize,
        },
        params: {
          bid: route.params.id,
        },
      })
    },
    {
      distance: 20,
      canLoadMore: () => paginate.value.hasNext,
    },
  )
</script>

<style lang="scss" scoped>
  .history_list {
    height: 100%;
    padding: 16px 0;
    overflow-y: auto;
    background-color: #f7f8fa;

    h1 {
      color: #1255e4;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }

    .record_add_name {
      color: #969799;
      font-size: 14px;
      line-height: 20px;
    }
  }
</style>
