import {
  administrativePenaltyApi,
  announcementApi,
  biddingApi,
  financingEventApi,
  financingLeadsApi,
  investmentLeadsApi,
  newsCluesApi,
  overseasInvestmentApi,
  patentApi,
  qualificationCertificateApi,
  recruitmentApi,
  restrictingHighConsumptionApi,
  softCopyrightApi,
  taxArrearsApi,
  taxViolationApi,
  trademarkApi,
} from '@/apis/recommend-detail'
import dayjs from 'dayjs'

export const colorList = [
  { bg: '#FFFBE6', text: '#FAAD14' },
  { bg: '#F6FFED', text: '#52C41A' },
  { bg: '#E6F4FF', text: '#1677FF' },
  { bg: '#F0F5FF', text: '#2F54EB' },
]

export const areaOptions = [
  {
    text: '江岸区',
    value: '江岸区',
  },
  {
    text: '江汉区',
    value: '江汉区',
  },
  {
    text: '硚口区',
    value: '硚口区',
  },
  {
    text: '汉阳区',
    value: '汉阳区',
  },
  {
    text: '武昌区',
    value: '武昌区',
  },
  {
    text: '青山区',
    value: '青山区',
  },
  {
    text: '洪山区',
    value: '洪山区',
  },
  {
    text: '蔡甸区',
    value: '蔡甸区',
  },
  {
    text: '江夏区',
    value: '江夏区',
  },
  {
    text: '黄陂区',
    value: '黄陂区',
  },
  {
    text: '新洲区',
    value: '新洲区',
  },
  {
    text: '东湖高新区',
    value: '东湖高新区',
  },
  {
    text: '武汉经开区',
    value: '武汉经开区',
  },
  {
    text: '东湖风景区',
    value: '东湖风景区',
  },
  {
    text: '武汉临空港',
    value: '武汉临空港',
  },
  {
    text: '长江新区',
    value: '长江新区',
  },
  {
    text: '驻京办',
    value: '驻京办',
  },
  {
    text: '驻广办',
    value: '驻广办',
  },
  {
    text: '驻沪办',
    value: '驻沪办',
  },
]

export const areaOptionsDefault = [
  {
    text: '江岸区',
    value: '江岸区',
  },
  {
    text: '江汉区',
    value: '江汉区',
  },
  {
    text: '硚口区',
    value: '硚口区',
  },
  {
    text: '汉阳区',
    value: '汉阳区',
  },
  {
    text: '武昌区',
    value: '武昌区',
  },
  {
    text: '青山区',
    value: '青山区',
  },
  {
    text: '洪山区',
    value: '洪山区',
  },
  {
    text: '蔡甸区',
    value: '蔡甸区',
  },
  {
    text: '江夏区',
    value: '江夏区',
  },
  {
    text: '黄陂区',
    value: '黄陂区',
  },
  {
    text: '新洲区',
    value: '新洲区',
  },
  {
    text: '东湖高新区',
    value: '东湖高新区',
  },
  {
    text: '武汉经开区',
    value: '武汉经开区',
  },
  {
    text: '武汉临空港',
    value: '武汉临空港',
  },
  {
    text: '东湖风景区',
    value: '东湖风景区',
  },
  {
    text: '长江新区',
    value: '长江新区',
  },
]

class RecommendListParams {
  obj = {
    page: {},
    params: {},
  }

  constructor(
    filterData = {
      similarCompanyName: '',
      region: '',
      before: '',
      after: '',
    },
    userBid,
    undertaken,
    paginate = { pageNum: 1, pageSize: 10 },
  ) {
    this.obj.page = {
      ...paginate,
      orders: [
        {
          field: '',
          asc: true,
        },
      ],
    }
    this.obj.params = {
      ...filterData,
      undertaken,
      userBid,
    }
  }
}

// 初始化请求参数
export function initParams(...args) {
  return new RecommendListParams(...args).obj
}

export const listInfoHandle = (list) => {
  return list.map((item) => {
    return {
      ...item,
      reason: {
        // 对外投资数量
        investinfoCount: item.investinfoCount,
        // 对外投资金额
        investinfoTotalAmount: item.investinfoTotalAmount,
        // 中标数量
        localWtbCount: item.localWtbCount,
        // 中标金额
        localWtbTotalAmount: item.localWtbTotalAmount,
        financingInfo: item.financingInfo?.split(',')?.map((item) => {
          item = item.split('|')
          return `${item[2]}${item[0]}${item[0].includes('轮') ? '' : '轮'}融资${transformNumber(item[1])}`
        }),
      },
    }
  })
}

export const itemInfoHandle = (item) => {
  const reason = {
    // 对外投资数量
    investinfoCount: item.investinfoCount,
    // 对外投资金额
    investinfoTotalAmount: item.investinfoTotalAmount,
    // 中标数量
    localWtbCount: item.localWtbCount,
    // 中标金额
    localWtbTotalAmount: item.localWtbTotalAmount,
    financingInfo: item.financingInfo?.split(',')?.map((item) => {
      item = item.split('|')
      return `${item[2]}${item[0]}${item[0].includes('轮') ? '' : '轮'}融资${transformNumber(item[1])}`
    }),
  }
  return reason
}

export function transformNumber(n, unit = '个') {
  const unitMap = {
    个: 1,
    万: 10000,
    百万: 1000000,
  }
  let number = +n * unitMap[unit]
  let numberUnit = ''
  if (isNaN(number)) {
    number = parseFloat(n) * unitMap[unit]
  }

  if (number / 10000 >= 1) {
    number /= 10000
    numberUnit = '万'
  }
  if (number / 10000 >= 1) {
    number /= 10000
    numberUnit = '亿'
  }

  return `${number.toFixed(2)}${numberUnit}`
}

/**
 * 详情接口映射
 */
export const detailApiMap = {
  投资线索: investmentLeadsApi,
  融资线索: financingLeadsApi,
  新闻线索: newsCluesApi,
  开庭公告: announcementApi,
  行政处罚: administrativePenaltyApi,
  欠税信息: taxArrearsApi,
  限制高消费: restrictingHighConsumptionApi,
  重大税收违法: taxViolationApi,
  对外投资: overseasInvestmentApi,
  融资事件: financingEventApi,
  资质证书: qualificationCertificateApi,
  专利: patentApi,
  软著: softCopyrightApi,
  商标: trademarkApi,
  招投标: biddingApi,
  招聘信息: recruitmentApi,
}

/**
 * 详情字段映射
 */
export const detailLabeKeyMap = {
  投资线索: {
    list: [
      {
        label: '被投资公司名：',
        key: 'invested_company_name',
      },
      {
        label: '出资比例：',
        key: 'stock_percent',
        handle(val) {
          return parseInt(val) + '%'
        },
      },
      {
        label: '认缴出资额：',
        key: 'regist_capi_value',
        handle(val, row) {
          return parseFloat(val).toFixed(2) + row?.regist_capi_unit || ''
        },
      },
      {
        label: '认缴出资日期：',
        key: 'shoud_date',
      },
    ],
  },
  融资线索: {
    list: [
      {
        label: '融资日期：',
        key: 'date',
      },
      {
        label: '金额（百万）：',
        key: 'amount',
        handle(val) {
          return parseFloat(val).toFixed(2)
        },
      },
      {
        label: '融资轮次：',
        key: 'round',
      },
      {
        label: '投资方：',
        key: 'investment',
      },
      {
        label: '投资事件全称：',
        key: 'eventname',
      },
    ],
  },
  新闻线索: {
    actions: {
      copy: {
        key: 'url',
      },
    },
    list: [
      {
        key: 'title',
        type: 'title',
      },
      {
        label: '标签：',
        key: 'tags',
      },
      {
        label: '来源：',
        key: 'source',
      },
      {
        label: '发布日期：',
        key: 'publish_time',
      },
    ],
  },
  开庭公告: {
    list: [
      {
        label: '开庭日期：',
        key: 'lian_date',
      },
      {
        label: '案由：',
        key: 'case_reason',
      },
      {
        label: '案号：',
        key: 'case_no',
      },
      {
        label: '原告：',
        key: 'prosecutor_list',
      },
      {
        label: '被告：',
        key: 'defendant_list',
      },
      {
        label: '法庭：',
        key: 'execute_unite',
      },
    ],
  },
  行政处罚: {
    list: [
      {
        label: '文号：',
        key: 'cf_wsh',
      },
      {
        label: '违法事实：',
        key: 'cf_sy',
      },
      {
        label: '处罚依据：',
        key: 'cf_yj',
      },
      {
        label: '处罚内容：',
        key: 'cf_nr',
      },
      {
        label: '罚款金额（万元）：',
        key: 'cf_nr_fk',
      },
      {
        label: '处罚决定日期：',
        key: 'cf_jdrq',
      },
    ],
  },
  欠税信息: {
    list: [
      {
        label: '欠税时间：',
        key: 'occurdate',
      },
      {
        label: '欠税税种：',
        key: 'tax_category',
      },
      {
        label: '欠税金额(元)：',
        key: 'amount',
      },
      {
        label: '发布单位：',
        key: 'issued_by',
      },
      {
        label: '负责人姓名：',
        key: 'legal_person',
      },
    ],
  },
  限制高消费: {
    list: [
      {
        label: '被执行人名称：',
        key: 'peopleenforced',
      },
      {
        label: '关联企业名称：',
        key: 'company_name',
      },
      {
        label: '立案时间：',
        key: 'filingtime',
      },
      {
        label: '案号：',
        key: 'casenumber',
      },
      {
        label: '执行法院：',
        key: 'courtname',
      },
      {
        label: '案由：',
        key: 'subjectmatter',
      },
      {
        label: '申请执行人：',
        key: 'executeapplyname',
      },
    ],
  },
  重大税收违法: {
    list: [
      {
        label: '纳税人名称：',
        key: 'taxpayer_name',
      },
      {
        label: '项目名发布时间：',
        key: 'illegal_time',
      },
      {
        label: '所属税务机关：',
        key: 'tax_gov',
      },
      {
        label: '违法事实：',
        key: 'illegal_content',
      },
    ],
  },
  对外投资: {
    list: [
      {
        label: '购买股权企业名称：',
        key: 'name',
      },
      {
        label: '注册号：',
        key: 'reg_no',
      },
      {
        label: '认缴出资额（万元）：',
        key: 'should_capi',
      },
      {
        label: '持股比例：',
        key: 'shareholding_ratio',
      },
    ],
  },
  融资事件: {
    list: [
      {
        label: '投资事件全称：',
        key: 'eventname',
      },
      {
        label: '融资日期：',
        key: 'date',
      },

      {
        label: '金额（百万）：',
        key: 'amount',
      },
      {
        label: '融资轮次：',
        key: 'round',
      },
    ],
  },
  资质证书: {
    list: [
      {
        label: '证书名称：',
        key: 'certificate_name',
      },
      {
        label: '证书编号：',
        key: 'certificate_no',
      },
      {
        label: '发放日期：',
        key: 'certificate_date',
      },
      {
        label: '截止时间：',
        key: 'certificate_end_date',
      },
    ],
  },
  专利: {
    list: [
      {
        label: '专利名称：',
        key: 'title',
      },
      {
        label: '公司名称：',
        key: 'company_name',
      },
      {
        label: '申请号：',
        key: 'application_number',
      },

      {
        label: '摘要：',
        key: 'abstracts',
      },
      {
        label: '申请日期：',
        key: 'application_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '生效日期：',
        key: 'legal_status_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '公开（公告）号：',
        key: 'publication_number',
      },
      {
        label: '公开（公告）日期 ：',
        key: 'publication_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
    ],
  },
  软著: {
    list: [
      {
        label: '软件全称：',
        key: 'name',
      },
      {
        label: '登记号：',
        key: 'register_no',
      },
      {
        label: '软件著作权人：',
        key: 'owner',
      },

      {
        label: '批准日期：',
        key: 'register_aper_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
    ],
  },
  商标: {
    list: [
      {
        label: '注册号：',
        key: 'reg_no',
      },
      {
        label: '商标名称：',
        key: 'name',
      },
      {
        label: '申请人：',
        key: 'applicant_cn',
      },
      {
        label: '申请时间：',
        key: 'app_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '初审公告期号：',
        key: 'announcement_issue',
      },
      {
        label: '初审公告日期：',
        key: 'announcement_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '注册公告期号：',
        key: 'reg_issue',
      },
      {
        label: '注册公告日期：',
        key: 'reg_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '使用期限/专用权期限：',
        key: 'valid_period',
      },
      {
        label: '专用权开始日期：',
        key: 'begin_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '专用权结束日期：',
        key: 'end_date',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
    ],
  },
  招投标: {
    list: [
      {
        label: '项目名称：',
        key: 'projectname',
      },
      {
        label: '中标单位：',
        key: 'wintenderer',
      },
      {
        label: '预算金额（元）：',
        key: 'biddingbudget',
      },
      {
        label: '中标金额（元）：',
        key: 'winbidprice',
      },
      {
        label: '项目总预算（元）：',
        key: 'totaltendereemoney',
      },
      {
        label: '公告时间：',
        key: 'pagetime',
      },
    ],
  },
  招聘信息: {
    list: [
      {
        label: '职位：',
        key: 'title',
      },
      {
        label: '招聘来源：',
        key: 'source',
      },
      {
        label: '职位发布日期：',
        key: 'publish_time',
        handle: (val) => dayjs(val).format('YYYY-MM-DD'),
      },
      {
        label: '地区：',
        key: 'area',
      },
      {
        label: '学历：',
        key: 'education',
      },
      {
        label: '工作经验：',
        key: 'experience',
      },
      {
        label: '招聘人数：',
        key: 'number',
      },
      {
        label: '行业：',
        key: 'industry',
      },
    ],
  },
}
