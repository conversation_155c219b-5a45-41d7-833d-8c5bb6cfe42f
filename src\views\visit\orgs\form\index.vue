<template>
  <div class="page">
    <Skeleton
      v-if="detailLoading"
      title
      :row="12" />
    <Form
      v-else
      validate-trigger="onSubmit"
      :readonly="isDetail"
      @submit="onSubmit">
      <div class="group">
        <div class="group_header">
          <h1 class="header_title">企业基本信息</h1>
        </div>
        <CellGroup inset>
          <FieldCompanyName
            v-model="formData.companyName"
            :readonly="isDetail"
            :rules="[
              {
                required: true,
                message: '请输入企业名称',
              },
            ]" />
          <FieldPickerSingle
            v-model="formData.industryCategory"
            label="行业类别"
            name="industryCategory"
            :readonly="isDetail"
            :options="industryOptions"
            :rules="[
              {
                required: true,
                message: '请选择行业类别',
              },
            ]" />
          <FieldPopupCheckbox
            v-model="formData.companyCategory"
            label="企业类别"
            name="companyCategory"
            :readonly="isDetail"
            :options="companyTypeOptions"
            :rules="[
              {
                required: true,
                message: '请选择企业类别',
              },
            ]" />
          <Field
            v-model="formData.contactPersonName"
            name="contactPersonName"
            label="企业联系人"
            placeholder="请输入企业联系人"
            :rules="[
              {
                required: true,
                message: '请输入企业联系人',
              },
            ]" />
          <Field
            v-model="formData.contactPersonPost"
            name="contactPersonPost"
            label="职务"
            placeholder="请输入联系人职务"
            :rules="[
              {
                required: true,
                message: '请输入联系人职务',
              },
            ]" />
          <Field
            v-model="formData.contactPersonInformation"
            name="contactPersonInformation"
            label="企业联系方式"
            placeholder="请输入企业联系方式"
            :rules="[
              {
                required: true,
                message: '请输入企业联系方式',
              },
            ]" />
          <FieldPickerSingle
            v-model="formData.isChushang"
            label="是否楚商"
            name="isChushang"
            :readonly="isDetail"
            :options="isChushangOptions"
            :rules="[
              {
                required: true,
                message: '请选择是否楚商',
              },
            ]" />
          <Field
            v-if="!formData.isChushang"
            v-model="formData.chushangInformation"
            name="chushangInformation"
            rows="2"
            autosize
            label="楚商信息"
            type="textarea"
            placeholder="请输入楚商信息"
            :rules="[
              {
                required: true,
                message: '请输入楚商信息',
              },
            ]" />
        </CellGroup>
      </div>
      <div class="group">
        <div class="group_header">
          <h1 class="header_title">走访信息记录</h1>
          <div
            v-if="!isDetail"
            class="header_action">
            <i class="iconfont icon-PlusCircleOutlined1"></i>
            <span @click="addVisitInfo">添加走访信息</span>
          </div>
        </div>
        <!-- 走访信息列表 -->
        <VisitCardList
          v-model="formData.visitBasicInformationDTOList"
          :is-edit="!isDetail"
          @item-click="onVisitItemClick" />
      </div>
      <div style="margin: 16px">
        <Button
          v-if="isDetail"
          block
          type="primary"
          @click="toLogs">
          操作记录
        </Button>
        <Button
          v-else
          block
          type="primary"
          native-type="submit"
          :loading="loading || updateLoading">
          提交
        </Button>
      </div>
    </Form>
  </div>
</template>

<script setup>
  import { addOrg, getOrgDetail, updateOrgInfo } from '@/apis/visit'
  import FieldPickerSingle from '@/components/form/field-picker-single.vue'
  import FieldPopupCheckbox from '@/components/form/field-popup-checkbox.vue'
  import { useVisitOrgFormStore, useVisitUserStore } from '@/stores/visit'
  import { isEmpty } from '@/utils/is'
  import { useRequest } from 'alova/client'
  import { storeToRefs } from 'pinia'
  import {
    Button,
    CellGroup,
    Field,
    Form,
    // showConfirmDialog,
    Skeleton,
  } from 'vant'
  import { useRoute, useRouter } from 'vue-router'
  import { usePickerOptions } from '../hook'
  import FieldCompanyName from './components/field-company-name.vue'
  import VisitCardList from './components/visit-card-list.vue'

  const { isChushangOptions, companyTypeOptions, industryOptions } =
    usePickerOptions()
  const store = useVisitOrgFormStore()
  const { formData } = storeToRefs(store)

  const route = useRoute()
  const router = useRouter()

  // 是否是详情模式
  const isDetail = route.meta?.status === 'detail'
  // 机构ID 存在代表编辑机构
  const orgId = route.params.id

  // 进入编辑路由
  function toLogs() {
    router.push(`/visit/logs/${orgId}`)
  }
  // 添加走访信息
  const addVisitInfo = () => {
    router.push('/visit/visit-form')
  }
  // 进去走访信息表单详情界面
  const onVisitItemClick = (index) => {
    if (isDetail) {
      router.push(`/visit/visit-detail/${index}`)
    }
  }

  // 提交表单
  const { loading, send: addOrgSend } = useRequest((form) => addOrg(form), {
    immediate: false,
  })
  // 更新表单
  const { loading: updateLoading, send: updateOrgSend } = useRequest(
    (form) => updateOrgInfo(form),
    {
      immediate: false,
    },
  )
  const visitUserStore = useVisitUserStore()
  // 表单提交
  const onSubmit = async (values) => {
    console.log('submit', values, formData.value)
    try {
      const submitData = {
        uid: visitUserStore.uid,
        ...formData.value,
        // 0 是；1 否；
        chushangInformation: formData.value.isChushang
          ? ''
          : formData.value.chushangInformation,
      }

      if (isEmpty(orgId)) {
        await addOrgSend(submitData)
        store.formActionType = 'added'
      } else {
        await updateOrgSend(submitData)
        // 编辑机构
        store.formActionType = 'edited'
      }
      back()
    } catch (error) {
      console.log(error)
    }
  }

  function back() {
    router.back()
  }

  // 每次初始进入重置表单数据
  store.$reset()

  // 获取表单详情
  const { loading: detailLoading } = useRequest(
    getOrgDetail({
      bid: orgId,
    }),
    {
      initialData: {},
      // immediate: false,
      middleware: async (_, next) => {
        if (orgId) {
          return next()
        }
      },
    },
  ).onSuccess(({ data }) => {
    formData.value = {
      ...data.visitEnterpriseBasicInformation,
      visitBasicInformationDTOList: [].concat(data.visitBasicInformationList),
    }
  })
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    padding: 20px 0;
    overflow-y: auto;
    background-color: #f7f8fa;
    .group {
      .group_header {
        display: flex;
        justify-content: space-between;
        padding: 0 16px 16px 16px;
        .header_title {
          color: #969799;
          font-size: 14px;
          line-height: 20px;
          text-align: left;
        }

        .header_action {
          display: flex;
          align-items: center;
          gap: 5px;
          color: #1989fa;
          font-size: 14px;
        }
      }

      & + .group {
        margin-top: 32px;
      }
    }
  }
</style>
