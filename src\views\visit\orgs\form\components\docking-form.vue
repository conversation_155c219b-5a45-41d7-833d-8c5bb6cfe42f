<template>
  <CellGroup inset>
    <Cell>
      <template #title>
        <h1>对接人</h1>
      </template>
      <template #value>
        <div class="flex w-full justify-end">
          <div
            class="delete-btn"
            @click="$emit('onDelete', formData.bid)">
            <i class="iconfont icon-DeleteOutlined"></i>
            <span>删除</span>
          </div>
        </div>
      </template>
    </Cell>
    <Field
      v-model="formData.name"
      name="name"
      label="姓名"
      placeholder="请输入姓名"
      :rules="[
        {
          required: true,
          message: '请输入姓名',
        },
      ]" />
    <Field
      v-model="formData.contactInformation"
      name="contactInformation"
      label="联系方式"
      placeholder="请输入联系方式"
      :rules="[
        {
          required: true,
          message: '请输入联系方式',
        },
      ]" />
    <Field
      v-model="formData.post"
      name="post"
      label="职务"
      placeholder="请输入职务" />
  </CellGroup>
</template>

<script setup>
  import { Cell, CellGroup, Field } from 'vant'
  defineEmits(['onDelete'])
  const formData = defineModel({
    type: Object,
  })
</script>

<style lang="scss" scoped>
  h1 {
    color: #1255e4;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
  }
  .delete-btn {
    display: flex;
    align-items: center;
    height: 100%;
    color: #f05542;
    font-size: 14px;
    & > i {
      margin-right: 5px;
      font-size: 14px;
    }
  }
</style>
