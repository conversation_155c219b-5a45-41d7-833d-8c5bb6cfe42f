<template>
  <div class="hire_card">
    <div class="flex items-center">
      <div class="number">{{ index }}</div>
      <div class="ml-[8px] text-[14px] font-[600]">研发/工艺工程师</div>
    </div>
    <div class="mt-[8px] flex">
      <div class="flex">
        <div class="label">招聘时间：</div>
        <div class="value">2024-07-17</div>
      </div>
      <div class="ml-[12px] flex">
        <div class="label">来源：</div>
        <div class="value">前程无忧</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
      default: 0,
    },
  })
</script>

<style lang="scss" scoped>
  .hire_card {
    padding: 6px;
    border-radius: 8px;
    background: rgb(18 85 228 / 4%);

    .number {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 14px;
      height: 18px;
      border-bottom-right-radius: 8px;
      background-color: #1255e4;
      color: #fff;
      font-size: 10px;
    }

    .label {
      color: rgb(0 0 0 / 60%);
      font-size: 12px;
    }

    .value {
      color: rgb(0 0 0 / 90%);
      font-size: 12px;
    }

    & + .hire_card {
      margin-top: 8px;
    }
  }
</style>
