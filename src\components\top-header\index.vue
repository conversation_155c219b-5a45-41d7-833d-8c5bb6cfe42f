<template>
  <div class="header">
    <div class="container">
      <template v-if="false">
        <Icon
          class="icon"
          name="arrow-left"
          color="#000"
          size="16px"
          @click="onClick" />
        <!-- 开启标题 -->
        <span class="text-[#000]">{{ title }}</span>
      </template>
    </div>
  </div>
</template>

<script setup>
  import { Icon } from 'vant'
  import { computed, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import wx from 'weixin-js-sdk'

  const router = useRouter()
  const route = useRoute()

  const title = computed(() => route.query.key || route.meta.name || '')

  watch(
    title,
    (nv) => {
      document.title = nv
    },
    {
      immediate: true,
    },
  )

  const onClick = () => {
    // 如果是首页，用微信的返回
    if (route.path === '/') {
      wx.miniProgram.navigateBack()
    } else {
      router.back()
    }
  }
</script>

<style lang="scss" scoped>
  .header {
    display: flex;
    // position: fixed;
    // top: 0;
    z-index: 999;
    align-items: center;
    width: 100%;

    // height: 44px;
    padding: 0 20px;
    background-color: white;

    .container {
      position: relative;
      height: 100%;
      color: #fff;
      font-weight: bold;
      font-size: 16px;
      line-height: 44px;
      text-align: center;

      .icon {
        position: absolute;
        top: 50%;
        left: 0;
        transform: translateY(-50%);
      }
    }
  }
</style>
