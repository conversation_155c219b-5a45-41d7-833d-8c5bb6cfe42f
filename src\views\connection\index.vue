<template>
  <VChart
    :option="option"
    autoresize
    class="h-full w-full" />
</template>

<script setup>
  import * as api from '@/apis/relationSearch.js'
  import { onMounted, reactive } from 'vue'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const option = reactive({
    series: [
      {
        type: 'graph',
        layout: 'force',
        symbolSize: 50,
        edgeSymbol: ['none', 'arrow'],
        edgeSymbolSize: 8,
        roam: true,
        draggable: true,
        label: {
          show: true,
        },
        lineStyle: {
          width: 2,
        },
        emphasis: {
          focus: 'adjacency',
        },
        categories: [
          { name: '目标公司' },
          { name: '终止点' },
          { name: '公司' },
          { name: '自然人' },
        ],
        data: [],
        links: [],
      },
    ],
    legend: [
      {
        data: ['目标公司', '公司', '自然人'],
      },
    ],
  })

  const getRelationSearchList = async () => {
    try {
      const res = await api.getRelationSearchList({
        start_name: route.query.companyName,
        start_type: 'Company',
      })
      const {
        data: { data, links },
      } = res.data
      option.series[0].data = data.map((item) => ({
        ...item,
        x: Math.random() * 100,
        y: Math.random() * 100,
      }))
      option.series[0].links = links
        .map((item) => ({
          ...item,
          label: {
            show: true,
            formatter: (params) => params.data.relation,
          },
          lineStyle: {
            // curveness: Math.random(),
            curveness: 0.2,
          },
        }))
        .filter(
          (item, index, self) =>
            index ===
            self.findIndex(
              (obj) => obj.source === item.source && obj.target === item.target,
            ),
        )
    } catch (err) {
      console.log(err)
    }
  }

  onMounted(() => {
    getRelationSearchList()
  })
</script>
