<template>
  <div class="index p-[16px]">
    <div class="items">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="item"
        @click="item.onClick">
        <div
          class="relative flex h-[58px] w-[58px] items-center justify-center rounded-[18px] bg-gradient-to-br text-white after:absolute after:bottom-[-5px] after:left-1/2 after:h-[10px] after:w-[50px] after:translate-x-[-50%] after:rounded-[10px] after:blur-[10px]"
          :class="[item.iconBg, item.afterBg]">
          <i
            class="iconfont text-[24px]"
            :class="item.icon" />
        </div>
        <p class="mb-[6px] mt-auto text-[16px]">
          {{ item.title }}
        </p>
        <i class="iconfont icon-RightCircle text-[#000]/[.26]" />
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()

  const items = computed(() => [
    {
      icon: 'icon-industry',
      iconBg: 'from-[#71DBFD] to-[#41AFFF]',
      afterBg: 'after:bg-[#49B6FF]/[0.4]',
      title: '产业概况',
      onClick: () => {
        router.push('/cygk/industry')
      },
    },
    {
      icon: 'icon-policy',
      iconBg: 'from-[#97BEFF] to-[#547AF7]',
      afterBg: 'after:bg-[#547AF7]/[0.4]',
      title: '产业政策',
      onClick: () => {
        router.push('/cygk/policy-list')
      },
    },
    {
      icon: 'icon-Park',
      iconBg: 'from-[#FFC09E] to-[#F77C54]',
      afterBg: 'after:bg-[#F77C54]/[0.4]',
      title: '产业载体',
      onClick: () => {
        router.push('/cygk/introduce')
      },
    },
    // {
    //   icon: 'icon-enterprise',
    //   iconBg: 'from-[#99FFEB] to-[#1FE4B4]',
    //   afterBg: 'after:bg-[#1FE4B4]/[0.4]',
    //   title: '产业主体',
    //   onClick: () => {
    //     window.open(
    //       // `https://jzzs.whcftzcj.com/uniapp/#/zhaoshang/industryChainAnalysis/industrialList?comp=ChainEnterprise&username=whwd&password=%242a%2404%24ZNq2e0dqWUDOZVchZSWGpOtdyi.9Z0j6vDwN0vr8MmscI42Fv.Z7e`,
    //       `https://jzzs.whcftzcj.com/uniapp/#/zhaoshang/industryChainAnalysis/industrialList?comp=ChainEnterprise&username=${route.query.username}&password=${route.query.password}&token=${route.query.token}`,
    //       // `https://whjzzs.gzjp.cn/uniapp/#/zhaoshang/industryChainAnalysis/industrialList?comp=ChainEnterprise&username=${route.query.username}&password=${route.query.password}`,
    //       // `https://whjzzs.gzjp.cn/uniapp/#/zhaoshang/targetEnterprise/newChainEnterprise?username=${route.query.username}&password=${route.query.password}`,
    //     )
    //   },
    // },
    // {
    //   icon: 'icon-area',
    //   iconBg: 'from-[#C5AEFF] to-[#6F67FF]',
    //   afterBg: 'after:bg-[#6F67FF]/[0.4]',
    //   title: '区域画像',
    //   onClick: () => {
    //     window.open(
    //       'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&extendInfo=445,262&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2Findustrialpark%2Findex',
    //     )
    //   },
    // },
    // {
    //   icon: 'icon-answer',
    //   iconBg: 'from-[#E8AEFF] to-[#C767FF]',
    //   afterBg: 'after:bg-[#C767FF]/[0.4]',
    //   title: '智能问答',
    //   onClick: () => {
    //     window.open(
    //       `${import.meta.env.VITE_CHAT_URL}?uid=${route.query.username}`,
    //     )
    //   },
    // },
    // {
    //   icon: 'icon-trademark',
    //   iconBg: 'from-[#E8AEFF] to-[#C767FF]',
    //   afterBg: 'after:bg-[#C767FF]/[0.4]',
    //   title: '一企一档',
    //   onClick: () => {
    //     window.open(
    //       `https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2FenterpriseSearch%2FenpSearch`,
    //     )
    //   },
    // },
  ])
</script>

<style lang="scss" scoped>
  .index {
    min-height: 100%;
    background-image: url('@/assets/images/index/bg.png');
    background-position: center bottom;
    background-size: 100%;
    background-repeat: no-repeat;

    .items {
      display: flex;
      flex-wrap: wrap;
      gap: 20px 11px;

      .item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 166px;
        height: 165px;
        padding: 24px 0;
        border-radius: 12px;
        background-color: #fff;
        box-shadow: 0 0 20px 0 rgb(0 0 0 / 10%);
      }
    }
  }
</style>
