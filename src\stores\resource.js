import { defineStore } from 'pinia'
import { ref } from 'vue'

/** 招商资源 */
export const useResourceStore = defineStore('resource', () => {
  const storeMap = ref({})

  /** 负责页面挂载时添加对应模块数据 */
  function addKey(key) {
    if (storeMap.value[key]) {
      return
    }
    storeMap.value[key] = {
      list: [],
      paginate: {},
      filter: {},
      y: 0,
      total: 0,
    }
  }

  function clean() {
    console.log('触发清除')
    storeMap.value = {}
  }

  return {
    addKey,
    storeMap,

    clean,
  }
})
