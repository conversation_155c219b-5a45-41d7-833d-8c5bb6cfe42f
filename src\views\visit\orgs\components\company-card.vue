<template>
  <div
    class="list_item"
    @click="jumpToCompanyDetail(item)">
    <div class="item_main">
      <header>
        <h1 class="truncate">{{ item.companyName }}</h1>
        <CompanyTag
          v-if="item.companyCategory && !isEmpty(companyTypes)"
          :key="item.bid"
          :options="companyTypes"
          :tags="item.companyCategory" />
      </header>
      <div class="main_infos">
        <div
          v-if="item.industryCategory"
          class="info">
          <i class="iconfont icon-enterprise"></i>
          <span>{{ item.industryCategory }}</span>
          <Divider
            class="ml-[12px]! mr-[12px]!"
            vertical />
        </div>
        <template v-if="showArea && item.areaName">
          <div class="info">
            <i class="iconfont icon-quyu"></i>
            <span>{{ item.areaName }}</span>
            <Divider
              class="ml-[12px]! mr-[12px]!"
              vertical />
          </div>
        </template>
        <template v-if="item.sharePersonNickname && showSharePerson">
          <div class="info">
            <i class="iconfont icon-Contact"></i>
            <span>共享人：{{ item.sharePersonNickname }}</span>
          </div>
        </template>
      </div>
    </div>
    <footer>
      <Space :size="10">
        <template v-if="hasAction">
          <button @click.stop="$emit('onShare', item.bid)">共享</button>
          <button @click.stop="$emit('onDetail', item.bid)">查看</button>
          <button @click.stop="$emit('onEdit', item.bid)">编辑</button>
          <button
            class="delete"
            @click.stop="$emit('onDelete', item.bid)">
            删除
          </button>
        </template>
        <button
          v-else
          @click.stop="$emit('onDetail', item.bid)">
          查看
        </button>
      </Space>
    </footer>
  </div>
</template>

<script setup>
  import { useDict } from '@/stores/dict'
  import { isEmpty } from '@/utils/is'
  import { Divider, Space } from 'vant'
  import { useRouter } from 'vue-router'
  import CompanyTag from './company-tag.vue'

  defineProps({
    // 企业项
    item: {
      type: Object,
      default: () => ({}),
    },
    // 是否存在操作按钮
    hasAction: {
      type: Boolean,
      default: false,
    },
    // 显示分享人
    showSharePerson: {
      type: Boolean,
      default: false,
    },
    // 显示区
    showArea: {
      type: Boolean,
      default: false,
    },
  })

  defineEmits(['onShare', 'onDetail', 'onEdit', 'onDelete'])

  const { companyTypes } = useDict('companyTypes')
  const router = useRouter()
  /**
   * 不同的企业跳转详情页面
   * - 是库里的企业跳转我们自己的详情页
   * - 非库里的企业跳转第三方详情页
   */
  function jumpToCompanyDetail(info) {
    if (info?.companyId) {
      router.push({
        path: '/recommend-detail',
        query: { companyId: info.companyId },
      })
    } else {
      window.open(
        'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2FenterpriseSearch%2FenpSearch' +
          encodeURIComponent(`?companyName=${info.companyName}`),
      )
    }
  }
</script>

<style lang="scss" scoped>
  .list_item {
    width: 100%;
    border-radius: 12px 12px 12px 12px;
    background: #ffffff;
    box-shadow: 0px 0px 15px 0px rgba(0, 0, 0, 0.06);

    .item_main {
      padding: 12px;

      & > header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 25px;
        & > h1 {
          // max-width: 65%;
          flex: 1;
          color: rgba(0, 0, 0, 0.9);
          font-weight: 600;
          font-size: 17px;
          line-height: 25px;
          text-align: left;
        }
        & > span {
          max-width: 32%;
          padding: 2px 8px;
          border-radius: 3px 3px 3px 3px;
          background: #e6f4ff;
          color: #1677ff;
          font-size: 12px;
          line-height: 20px;
        }
      }

      .main_infos {
        display: flex;
        align-items: center;
        height: 20px;
        margin-top: 8px;
        line-height: 20px;
        .info {
          display: flex;
          align-items: center;
          height: 100%;
          color: rgba(0, 0, 0, 0.4);
          font-size: 12px;
          & > i {
            margin-right: 5px;
            font-size: 12px;
          }
        }
      }
    }

    & > footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 40px;
      padding: 0 12px;
      border-top: 1px solid rgba(0, 0, 0, 0.06);

      button {
        padding: 1px 16px;
        border-radius: 26px 26px 26px 26px;
        background: rgba(18, 85, 228, 0.06);
        color: #1255e4;
        font-size: 14px;
        line-height: 22px;
        &:active {
          opacity: 0.8;
        }
        &.delete {
          background: rgba(255, 77, 79, 0.06);
          color: #ff4d4f;
        }
      }
    }

    & + .list_item {
      margin-top: 16px;
    }
  }
</style>
