<template>
  <div class="wuhan-container">
    <!-- 选项卡导航 -->
    <div class="tabs">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab"
        :class="{ active: activeTab === tab.id }"
        @click="switchTab(tab.id)">
        <img
          v-if="tab.icon"
          :src="activeTab === tab.id ? tab.activeIcon : tab.icon"
          alt=""
          class="tab-icon" />
        {{ tab.name }}
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-container">
      <!-- 只显示当前激活的标签页内容 -->
      <div class="content">
        <!-- 动态渲染图文对 -->
        <div
          v-for="(item, index) in currentTabData.items"
          :key="index"
          class="content-block">
          <img
            v-if="item.image"
            :src="item.image"
            :alt="`${tabs.find((t) => t.id === activeTab).name}图片${index + 1}`"
            class="city-image" />
          <div
            v-if="item.subText"
            class="sub-text"
            >{{ item.subText }}</div
          >
          <div class="city-info">{{ item.text }}</div>
        </div>

        <div
          v-if="currentTabData.stats"
          class="stats-container">
          <div
            v-for="(stat, statIndex) in currentTabData.stats"
            :key="statIndex"
            class="stat-item">
            <img
              :src="stat.icon"
              class="stat-icon"
              alt="icon" />
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>

        <!-- 水域面积比例 (如果有) -->
        <div
          v-if="currentTabData.waterRatio"
          class="water-ratio">
          <img
            :src="currentTabData.waterRatio.icon"
            class="water-icon"
            alt="水域面积图标" />
          <div class="water-text">
            <div style="font-weight: bold">{{
              currentTabData.waterRatio.title
            }}</div>
            <div class="ratio">{{ currentTabData.waterRatio.ratio }}</div>
            <div class="ratioSubtitle">{{
              currentTabData.waterRatio.subtitle
            }}</div>
            <div style="color: #d4a76a">{{
              currentTabData.waterRatio.ratioEn
            }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  // 图标引入
  import parksIcon from '@/assets/images/enterprise-side/gy.png'
  import riverIcon from '@/assets/images/enterprise-side/hl.png'
  import lochsIcon from '@/assets/images/enterprise-side/hp.png'
  import mountainsIcon from '@/assets/images/enterprise-side/sl.png'
  import totalAreaIcon from '@/assets/images/enterprise-side/sy.png'
  import attractionsIcon from '@/assets/images/enterprise-side/whjd.svg'
  import attractionsIcon1 from '@/assets/images/enterprise-side/whjd1.svg'
  import introIcon from '@/assets/images/enterprise-side/whjj.svg'
  import introIcon1 from '@/assets/images/enterprise-side/whjj1.svg'
  import foodIcon from '@/assets/images/enterprise-side/whms.svg'
  import foodIcon1 from '@/assets/images/enterprise-side/whms1.svg'
  import cultureIcon from '@/assets/images/enterprise-side/whwh.svg'
  import cultureIcon1 from '@/assets/images/enterprise-side/whwh1.svg'
  import { computed, onMounted, reactive, ref } from 'vue'
  // 页面图片
  import attractions1 from '@/assets/images/enterprise-side/attractions_1.png'
  import attractions2 from '@/assets/images/enterprise-side/attractions_2.png'
  import attractions3 from '@/assets/images/enterprise-side/attractions_3.png'
  import attractions4 from '@/assets/images/enterprise-side/attractions_4.png'
  import attractions5 from '@/assets/images/enterprise-side/attractions_5.png'
  import { default as intro1 } from '@/assets/images/enterprise-side/intro_1.png'
  import food1 from '@/assets/images/enterprise-side/whms_1.png'
  import food2 from '@/assets/images/enterprise-side/whms_2.png'
  import food3 from '@/assets/images/enterprise-side/whms_3.png'
  import culture1 from '@/assets/images/enterprise-side/whwh_1.png'
  import culture2 from '@/assets/images/enterprise-side/whwh_2.png'
  import culture3 from '@/assets/images/enterprise-side/whwh_3.png'
  import culture4 from '@/assets/images/enterprise-side/whwh_4.png'

  import intro2 from '@/assets/images/enterprise-side/intro_2.png'
  import defaultImage from '@/assets/images/enterprise-side/qjzt.png'

  // 图片资源映射
  const imageResources = {
    intro: [intro1, intro2],
    attractions: [
      attractions1,
      attractions2,
      attractions3,
      attractions4,
      attractions5,
    ],
    food: [food1, food2, food3],
    culture: [culture1, culture2, culture3, culture4],
  }

  // 定义选项卡数据 - 增加activeIcon属性
  const tabs = [
    { id: 'intro', name: '武汉简介', icon: introIcon, activeIcon: introIcon1 },
    {
      id: 'attractions',
      name: '武汉景点',
      icon: attractionsIcon,
      activeIcon: attractionsIcon1,
    },
    { id: 'food', name: '武汉美食', icon: foodIcon, activeIcon: foodIcon1 },
    {
      id: 'culture',
      name: '武汉文化',
      icon: cultureIcon,
      activeIcon: cultureIcon1,
    },
  ]

  // 当前默认激活的选项卡
  const activeTab = ref('intro')

  // 城市数据 - 使用数组存储图文对
  const cityData = reactive({
    intro: {
      items: [
        {
          image: null,
          text: '武汉:湖北省省会城市,中部中心城市,国家历史文化名城,国际性综合交通枢纽城市,面积8569平方公里,实有人口超1500万人,拥有3500多年悠久历史。',
          subText: null,
        },
        {
          image: null,
          text: '',
          subText: null,
        },
      ],
      stats: [
        { icon: riverIcon, value: '166条河流', label: '166 Rivers' },
        { icon: lochsIcon, value: '166个湖泊', label: '166 Lakes' },
        { icon: mountainsIcon, value: '446座山林', label: '446 Mountains' },
        { icon: parksIcon, value: '525个公园', label: '525 Parks' },
      ],
      waterRatio: {
        icon: totalAreaIcon,
        title: '水域面积占市域总面积',
        ratio: '1/4',
        subtitle: "Ratio of Water Area to the City's Total Area",
        ratioEn: '1/4',
      },
    },
    attractions: {
      items: [
        {
          image: null,
          text: '崔颢题诗的墨痕未干，李白的孤帆仍在云间。登上蛇山之巅，看黄鹤楼与长江大桥古今辉映，飞檐刺破云霞，涛声卷走时光。凭栏处，一句“孤帆远影碧空尽”脱口而出，原来诗词里的山河，从未老去。',
          subText: '黄鹤楼：千年的诗，吹过长江的风。',
        },
        {
          image: null,
          text: '33平方公里的碧波，是武汉捧给世界的翡翠。春樱落雪时，骑行者从磨山楚城下掠过，惊起一行白鹭；秋日残荷听雨,水墨长堤上飘来汉剧婉转的唱腔。这里住着楚辞的浪漫，也藏着市井的闲——老武汉人摇着蒲扇在听涛景区下棋，茶缸里的茉莉香，比西湖龙井多了三分江湖气。',
          subText: '东湖 ： 把江南揉进一座城的温柔。',
        },
        {
          image: null,
          text: '热干面的芝麻香混着武昌鱼蒸腾的鲜，三鲜豆皮的焦脆裹着面窝的金黄。拐角婆婆三十年不变的红油配方，让鸭脖辣得人眼眶湿润却停不下嘴。这里没有精致的摆盘，只有铁锅里翻滚的江湖：一碗糊汤粉泡油条，吃出武汉人“不服周”的倔强。',
          subText: '户部巷 ： 在烟火里咬一口江湖。',
        },
        {
          image: null,
          text: '当蒸汽轮船的汽笛划破夜色，你已踏入1930年的武汉码头。旗袍美人眼波流转，报童叫卖着民国小报，舞池里留声机流淌着周璇的歌声。甲板上长江灯火如星坠落，恍惚间听见历史在耳边呢喃：这座九省通衢之城，本身就是一座永不落幕的剧院。',
          subText: '知音号 ： 穿越时光的江上夜宴。',
        },
        {
          image: null,
          text: '哥特式教堂的彩窗映着涂鸦墙，咖啡香从百年老宅飘向街角的书店。昙华林的石阶上，民国学者踩过的青苔正托起摄影师的镜头。在街角老茶馆听楚地评书，转身遇见抱着吉他的少年——新旧时光在这里碰撞，绽放出穿越世纪的文艺之花。',
          subText: '昙华林 ： 文艺与沧桑的二重奏。',
        },
      ],
    },
    food: {
      items: [
        {
          image: null,
          text: '碱水面在沸水里烫十五秒便起，淋一勺传承百年的石磨芝麻酱，辣萝卜丁与葱花是最后的侠客暗号。拌面的力度要狠，让每根面条裹上琥珀色的铠甲，蹲在塑料凳上大口吸溜，黏糊糊的烟火气里藏着武汉的生存智慧：再匆忙的日子，也要活得浓墨重彩。',
          subText: '热干面 ：芝麻香里的江湖哲学。',
        },
        {
          image: null,
          text: '九孔藕在砂锅里慢煨两小时方成，丢几块带髓的猪肋排，姜片与花椒是隐世高人的点化。火候要稳，让藕段吸饱乳白的浓醇，捧一碗在巷口小木桌边坐下，清甜的汤雾里漾着武汉的处世哲学：再硬的棱角，也能被岁月熬出绵软的回甘。',
          subText: '莲藕排骨汤 ：煨炖千年的江湖柔情',
        },
        {
          image: null,
          text: '青壳虾在滚油里爆炒三十秒定魂，浇一勺秘制豆瓣酱，啤酒与蒜瓣是暗夜里的江湖令。收汁要猛，让每只虾卷成鎏金的战甲，围坐大排档铁盆边手撕扯，红彤彤的辣雾里蒸腾着武汉的豪情宣言：再平淡的夜晚，也要吃得风生水起。',
          subText: '油焖大虾 ：红油烈焰中的江湖快意。',
        },
      ],
    },
    culture: {
      items: [
        {
          image: null,
          text: '当盘龙城的饕餮纹在3600年前睁开双眼，长江之滨便有了文明的胎动。商周青铜的绿绣里，凝固着荆楚先民“不服周”的桀骜；曾侯乙编钟敲响的宫商角徵羽，至今仍在东湖樱园的落花间震颤。这座城把古老写进行云流水——黄鹤楼飞檐挑起唐宋明月，归元寺的银杏用年轮篆刻禅意，江汉关钟声里，历史永远在青铜与玻璃幕墙间对谈。',
          subText: '青铜器上的城市胎记',
        },
        {
          image: null,
          text: '两江交汇处，船工号子曾是城市心跳。汉正街的青石板浸透盐商的汗水，码头苦力肩扛的麻袋里，滚落出“九省通衢”的基因密码。茶馆里评书声与算盘响此起彼伏，热干面的芝麻香混着轮渡汽笛——武汉文化从不是精致的青花瓷，而是码头铁锚碰撞出的火星，在龙王庙的浪涛里淬炼成”敢为人先”的胆魄。',
          subText: '码头淬炼的江湖魂',
        },
        {
          image: null,
          text: '张之洞在汉阳铁厂点亮的炉火，烧穿了封建王朝的暮色。武昌首义的枪声惊醒亚洲沉睡的雄狮，国立武汉大学的琉璃瓦上，前人的思想仍在珞珈山巅盘旋。这里的长江水既载过北伐的征帆，也映照过“一五计划”的钢花飞溅。老汉口租界区的巴洛克穹顶下，红色砖墙里依然回响着《大江报》的觉醒年代。',
          subText: '铁与火的启蒙年代',
        },
        {
          image: null,
          text: '十一座长江大桥是城市写给江河的情书，钢铁琴弦昼夜弹奏着流动的诗。渡江节的人群劈波斩浪时，长江灯光秀正用激光在夜空书写新的《水调歌头》——这座城永远在传统与先锋的碰撞中，完成文化的自我重构。',
          subText: '桥与水的现代寓言',
        },
      ],
    },
  })

  // 计算属性：获取当前标签页的数据
  const currentTabData = computed(() => {
    return cityData[activeTab.value]
  })

  // 切换选项卡的方法
  const switchTab = (tabId) => {
    activeTab.value = tabId
  }

  // 初始化所有图片
  const initializeImages = () => {
    // 遍历所有标签页
    for (const tabId in cityData) {
      const items = cityData[tabId].items
      const tabImages = imageResources[tabId] || []

      // 为每个项目分配图片
      for (let i = 0; i < items.length; i++) {
        // 如果有对应的图片就使用，否则使用默认图片
        items[i].image = i < tabImages.length ? tabImages[i] : defaultImage
      }
    }
  }

  // 组件挂载时初始化所有图片
  onMounted(() => {
    initializeImages()
  })
</script>

<style scoped>
  .content-container {
    padding-bottom: 16px;
    background-image: url('@/assets/images/enterprise-side/whBackgrordImg.png');
    background-position: center;
    background-repeat: repeat;
  }

  .wuhan-container {
    overflow: hidden;
    width: 100%;
    margin-bottom: 0;
    border-radius: 5px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  }

  .tabs {
    display: flex; /* 已有 */
    justify-content: center; /* 新增：水平居中整个tabs容器的内容 */
    align-items: center; /* 已有，垂直居中 */
    overflow-x: auto;
    width: auto;
    height: 44px;
    color: #fff;
    font-weight: 400;
    font-size: 14pt;
    font-family: 'Alibaba PuHuiTi', sans-serif;
  }

  .tab {
    display: flex;
    flex: 1;
    justify-content: center; /* 水平居中tab内的内容 */
    align-items: center; /* 垂直居中 */
    width: 28px;
    height: 32.5px;
    margin: 0 4px; /* 间隔 */
    color: #989898;
    font-weight: 400;
    font-size: 14px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    text-align: center; /* 修改：从left改为center */
  }

  .tab.active {
    border-radius: 44pt;
    background-image: linear-gradient(
      to right,
      rgb(18 175 228 / 100%),
      rgb(18 85 228 / 100%)
    );
    color: #fff;
    font-size: 14px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
  }

  .tab-icon {
    width: 14px;
    height: 14px;
    margin-right: 4px;
  }

  .content {
    width: 100%;
    padding-bottom: 0;
  }

  .content-block {
    margin: 16px;
  }

  .content-block:last-child {
    margin-bottom: 0;
  }

  .city-image {
    object-fit: cover;
    width: 100%;
    height: 180px;
    border-radius: 4px;
  }

  .city-info {
    padding-top: 15px;
    color: #9f6000;
    font-weight: 400;
    font-size: 16px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    line-height: 1.6;
  }

  /* 统计数据样式 */
  .stats-container {
    display: flex;
    justify-content: space-between;
    margin: 0 0 20px;
    padding: 15px;
    border-radius: 8px;
    background-color: #f9f9f9;

    /* flex-wrap: wrap; 允许在小屏幕上换行 */
  }

  .stat-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    min-width: 80px; /* 确保在小屏幕上有最小宽度 */
    margin: 5px 0;
  }

  .stat-icon {
    object-fit: contain; /* 保持图标比例 */
    width: 36px; /* 根据需要调整大小 */
    height: 36px;
  }

  .stat-value {
    margin-top: 12px;
    color: #d4a76a;
    font-weight: bold;
    font-size: 14px;
  }

  .stat-label {
    color: #999;
    font-size: 12px;
  }

  /* 水域面积比例样式 */
  .water-ratio {
    display: flex;
    flex-direction: column; /* 添加垂直排列 */
    justify-content: center;
    align-items: center;
    margin: -12px 4vw 0;
    border-radius: 8px;
    background-color: #f9f9f9;
  }

  .water-icon {
    object-fit: contain; /* 保持图标比例 */
    width: 48px;
    height: 48px;
    margin-right: 15px;
    margin-bottom: 15px;
    color: #d4a76a;
    font-size: 32px;
  }

  .water-text {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
    text-align: center;
  }

  .ratio {
    margin: 5px 0;
    color: #d4a76a;
    font-weight: bold;
    font-size: 24px;
  }

  .ratioSubtitle {
    color: rgb(146 146 144 / 100%);
    font-size: 12px;
  }

  .sub-text {
    margin-top: 8px;
    color: rgb(0 0 0 / 40%);
    font-size: 14px;
    font-family: 'Alibaba PuHuiTi', sans-serif;
  }
</style>
