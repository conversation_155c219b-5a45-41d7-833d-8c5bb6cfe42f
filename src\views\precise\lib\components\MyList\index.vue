<template>
  <div class="container">
    <div class="header">
      <Search
        v-if="showSearchBar"
        v-model="keyword"
        placeholder="搜索关键字"
        shape="round"
        @search="(val) => emits('search', val)"
        @clear="() => emits('search', '')" />
      <div class="filter">
        <div
          v-for="(filter, index) in filters"
          :key="index"
          class="filter_item"
          @click="showModal[filter.key] = true">
          <span>
            {{ filter.showTitle() }}
          </span>
          <span
            v-if="filter.onClear && filter.value?.length > 0"
            class="ml-[4px] text-[14px] text-[#DCDEE0]"
            @click.stop="
              () => {
                componentValues[filter.key] = []
                filter.onClear()
              }
            ">
            x
          </span>
          <i
            v-else
            class="iconfont icon-caret-down-small ml-[4px] flex-shrink-0 text-[8px] text-[#DCDEE0]"></i>
        </div>
      </div>
      <template
        v-for="(filter, index) in filters"
        :key="index">
        <Popup
          v-if="filter?.type === 'cascader'"
          v-model:show="showModal[filter.key]"
          style="height: 50%; min-height: 50%"
          round
          close-on-click-overlay
          position="bottom">
          <Cascader
            v-model="componentValues[filter.key]"
            title="请选择"
            :options="filter.options"
            @close="showModal[filter.key] = false"
            @finish="filter.onChange"></Cascader>
        </Popup>
        <ActionSheet
          v-else-if="!filter.multiple"
          v-model:show="showModal[filter.key]"
          close-on-click-action
          :actions="filter.options"
          cancel-text="取消"
          @select="filter.onChange" />
        <Popup
          v-else
          v-model:show="showModal[filter.key]"
          style="height: 60%"
          round
          close-on-click-overlay
          position="bottom">
          <div class="flex h-full flex-col p-[12px]">
            <div class="flex justify-between">
              <div @click="onClose(filter.key)">取消</div>
              <div @click="onConfirm(filter)">确定</div>
            </div>
            <CheckboxGroup
              v-model="componentValues[filter.key]"
              class="flex-1 overflow-y-auto">
              <Checkbox
                v-for="(c, i) in filter.options"
                :key="i"
                class="mt-[12px]"
                :name="c.value">
                {{ c.text }}
              </Checkbox>
            </CheckboxGroup>
          </div>
        </Popup>
      </template>
    </div>
    <!-- <div class="count_info">
      <div class="title">
        <span>{{ title }}</span>
        <span v-show="showFilterKey"> ：{{ titleDisplayValue }} </span>
      </div>

      <div class="total">
        <span>当前共有</span>
        <span class="text-[#1255E4]">{{ total }}</span>
        <span>个</span>
      </div>
    </div> -->
    <div
      ref="listRef"
      class="list_box">
      <div
        v-for="(item, index) in listArr"
        :key="index"
        class="list_item"
        @click="emits('card-click', item)">
        <slot
          name="card"
          :item="item"></slot>
      </div>
      <Empty
        v-show="!loading && total === 0"
        description="暂无数据"></Empty>
      <div
        v-show="loading"
        class="list_item text-center text-[12px] text-[rgba(0,0,0,0.6)]">
        加载中...
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useInfiniteScroll, useScroll } from '@vueuse/core'
  import {
    ActionSheet,
    Cascader,
    Checkbox,
    CheckboxGroup,
    Empty,
    Popup,
    Search,
  } from 'vant'
  import { nextTick, onBeforeMount, onMounted, ref } from 'vue'

  const { filters, loading, total, hasNext, listArr, defaultSearchValue } =
    defineProps({
      showFilterKey: {
        type: [String, Function],
        default: '',
      },
      filters: {
        type: Array,
        default: () => [],
      },
      total: {
        type: Number,
        default: 0,
      },
      title: {
        type: String,
        default: '',
      },
      listArr: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      hasNext: {
        type: Boolean,
        default: true,
      },
      showSearchBar: {
        type: Boolean,
        default: true,
      },
      search: {
        type: Object,
        default: () => ({}),
      },
      defaultSearchValue: {
        type: String,
        default: '',
      },
    })

  const emits = defineEmits(['card-click', 'down', 'search'])

  const listRef = ref(null)
  const { y } = useScroll(listRef)

  const keyword = ref('')
  const showModal = ref({})
  const componentValues = ref({})

  onMounted(() => {
    nextTick(() => {
      keyword.value = defaultSearchValue
    })
  })

  onBeforeMount(() => {
    filters.forEach((item) => {
      showModal[item.key] = false
      componentValues[item.key] = item.value
    })
  })

  // const titleDisplayValue = computed(() => {
  //   return typeof showFilterKey === 'string'
  //     ? filters.find((item) => item.key === showFilterKey)?.value
  //     : showFilterKey(filters)
  // })

  useInfiniteScroll(
    listRef,
    () => {
      emits('down')
    },
    {
      distance: 20,
      canLoadMore: () => {
        return hasNext && !loading && listArr.length > 0
      },
    },
  )

  function onClose(key) {
    showModal.value[key] = false
    componentValues.value[key] = filters.find((item) => item.key === key)?.value
  }

  function onConfirm(filter) {
    filter.onChange(componentValues.value[filter.key])
    showModal.value[filter.key] = false
  }

  defineExpose({
    listRef,
    y,
  })
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    .header {
      background-color: #fff;

      .filter {
        display: flex;
        height: 48px;
        margin-top: 8px;

        .filter_item {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          padding: 0 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .count_info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 16px;
      padding: 0 16px;

      .title {
        position: relative;
        margin-right: 8px;
        padding-left: 8px;
        overflow: hidden;
        color: rgb(0 0 0 / 88%);
        font-weight: 600;
        font-size: 14px;
        text-overflow: ellipsis;
        white-space: nowrap;

        &::before {
          position: absolute;
          top: 3px;
          left: 0;
          width: 4px;
          height: 16px;
          border-radius: 16px;
          background: #1255e4;
          content: '';
        }
      }

      .total {
        flex-shrink: 0;
        color: rgb(0 0 0 / 40%);
        font-size: 12px;
      }
    }

    .list_box {
      flex: 1;
      margin-top: 12px;
      padding: 0 16px;
      overflow-y: auto;

      .list_item {
        position: relative;
        padding: 16px;
        border-radius: 16px;
        background: white;

        & + .list_item {
          margin-top: 16px;
        }

        .name {
          display: flex;
          align-items: center;
          color: rgb(0 0 0 / 88%);
          font-size: 16px;
        }

        .item_info {
          display: flex;
          align-items: center;
          margin-top: 8px;
        }
      }
    }
  }
</style>
