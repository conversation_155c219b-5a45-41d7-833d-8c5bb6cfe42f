/**
 * 各类options dict 接口
 * 这类接口都应该缓存
 */

import { get } from '@/utils/alova/index'

const baseUrl = import.meta.env.VITE_API_RECOMMEND_BASE_URL

/**
 * 走访企业库获取企业类别
 */
export const getEntTypeList = () =>
  get(
    `${baseUrl}/visitEnterpriseDatabase/getCompanyCategory`,
    {},
    {
      cacheFor: 10 * 60 * 1000, // 缓存时间（毫秒） 10分钟
    },
  )

/**
 * 获取区列表（不是行政区）
 * @returns
 */
export const getAreaOptions = () =>
  get(
    `${baseUrl}/visitEnterpriseDatabase/getAreaList`,
    {},
    {
      cacheFor: 10 * 60 * 1000, // 缓存时间（毫秒） 10分钟
    },
  )
