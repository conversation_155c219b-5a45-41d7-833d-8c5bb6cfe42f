<template>
  <div
    ref="listRef"
    class="policy_list">
    <!-- banner -->
    <!-- <div class="banner">
      <img
        src="@/assets/images/cygk/policy-list.png"
        alt=""
        class="banner_image" />
    </div> -->
    <Tabs
      :active="tabActive"
      @click-tab="onTabFilterChange">
      <Tab
        name="default"
        title="综合篇"></Tab>
      <Tab
        name="industrychain"
        title="产业篇">
      </Tab>
    </Tabs>

    <div class="content_filter_input">
      <CellGroup style="width: 100%">
        <Search
          v-model="keyword"
          style="width: 100%"
          shape="round"
          placeholder="输入搜索关键词"
          @keydown.enter="onEnter"
          @clear="onEnter" />
      </CellGroup>
    </div>

    <div class="content_filter">
      <!-- <div
        class="content_select_button"
        :class="{ select: isShowPicker }"
        @click="isShowPicker = true">
        <div>{{ selectData }}</div>
        <i class="iconfont icon-qy_down1"></i>
      </div> -->
      <!-- <div
        class="flex h-[24px] items-center gap-[6px]"
        @click="showMenu = true">
        <span class="currentTitle leading-[24px] text-[#000]">
          {{ selectData }}
        </span>
        <i class="iconfont icon-caret-down-small text-[10px]"></i>
      </div> -->
      <!-- 选择器 -->
      <!-- <Popup
        v-model:show="isShowPicker"
        position="top">
        <Picker
          :columns="columns"
          @confirm="onConfirm"
          @cancel="isShowPicker = false" />
      </Popup> -->

      <div class="content_filter_group">
        <div
          v-for="(button, index) in levelOptions"
          :key="index"
          :class="{ active: levelType === button }"
          class="content_filter_button"
          @click="onLevelChange(button)">
          {{ button }}
        </div>
        <div
          v-if="tabActive == 'industrychain'"
          class="content_filter_button_select flex flex-1"
          @click="showSelect = true">
          <TextEllipsis
            :content="selectData"
            class="flex-1" />
          <i
            class="iconfont icon-caret-down-small ml-[6px] text-[8px] text-[#DCDEE0]"></i>
        </div>
        <ActionSheet
          v-model:show="showSelect"
          title="产业链"
          :actions="columns"
          @select="onIndustrySelect">
        </ActionSheet>
      </div>

      <!-- <div class="content_count">
        <span>共计</span>
        <span style="color: #1255e4">{{ paginate[total] }}</span>
        <span>条</span>
      </div> -->
    </div>

    <div class="list">
      <template
        v-for="(item, index) in policyList"
        :key="index">
        <div
          v-if="item.number"
          class="policy_card"
          @click="toDetail(item.id)">
          <div
            class="policy_top"
            :style="{
              background: TAG_COLOR_MAP[item.level]?.bg,
            }">
            <div
              class="policy_id_text"
              :style="{
                color: TAG_COLOR_MAP[item.level]?.text,
              }">
              {{ item.number }}
            </div>
            <div
              class="policy_tag"
              :style="{
                color: TAG_COLOR_MAP[item.level]?.text,
              }">
              {{ item.level }}
            </div>
          </div>
          <div class="px-[16px] pb-[16px]">
            <div class="policy_title">
              {{ item.name }}
            </div>
            <!-- <div class="policy_content">
            <TextEllipsis
              rows="4"
              :content="
                (item.content || item.synopsis).replaceAll('\\n', '')
              " />
          </div> -->
            <div class="policy_bottom">
              <!-- <div
              v-show="item?.industrychain"
              class="policy_tags">
              <div
                v-for="(i, idx) in item?.industrychain?.split(',')"
                :key="idx"
                class="policy_tag">
                {{ i }}
              </div>
            </div> -->
              <div class="policy_time_box">
                <div
                  v-show="item.start_time"
                  class="policy_time">
                  <i class="iconfont icon-Calendar tw-text-[12px]"></i>
                  <span>
                    {{ item.start_time?.split(' ')?.[0] }} ~
                    {{ getEndTime(item.lifespan?.split(' ')?.[0]) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <Loading
        v-show="loading"
        class="policy_card loading">
        加载中...
      </Loading>
      <ActionSheet
        v-model:show="showMenu"
        :actions="columns"
        cancel-text="取消"
        close-on-click-action
        @select="onSelectType"></ActionSheet>
      <div
        v-show="paginate[total] === 0 && !loading"
        style="text-align: center"
        class="policy_card">
        暂无数据
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getPageList } from '@/apis'
  import cardTop from '@/assets/images/cygk/policy-card-top.png'
  import { INDUSTRY_OPTIONS, TAG_COLOR_MAP } from '@/views/cygk/options'
  import {
    ActionSheet,
    CellGroup,
    Loading,
    Search,
    Tab,
    Tabs,
    TextEllipsis,
  } from 'vant'
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const router = useRouter()
  const route = useRoute()
  console.log(route)

  // const { key, level } = route.query
  const loading = ref(false)
  const listRef = ref(null)
  // const isShowPicker = ref(false)
  const showMenu = ref(false)
  // 展示产业链选择器
  const showSelect = ref(false)

  const cardTopBg = computed(() => {
    return `url(${cardTop})`
  })

  const columns = [
    {
      name: '产业链',
    },
    ...INDUSTRY_OPTIONS.map((m) => ({ name: m.value })),
  ]

  const policyList = ref([])

  const selectData = ref('产业链')
  const levelType = ref('市级')
  const levelOptions = ['国家级', '省级', '市级', '区级']

  const keyword = ref('')
  // tab筛选
  const tabActive = ref('default')

  // onMounted(() => {
  //   levelType.value = level ?? '国家级'
  //   keyword.value = key ?? ''
  // })

  // function onConfirm({ selectedValues }) {
  //   if (selectedValues[0] === selectData.value) {
  //     isShowPicker.value = false
  //     return
  //   }
  //   selectData.value = selectedValues[0]
  //   isShowPicker.value = false
  //   paginate.value.pageNum = 1
  //   paginate.value[total] = 0
  //   policyList.value = []
  //   getPageData()
  // }
  const onSelectType = (selectedValues) => {
    // if (selectedValues === selectData.value) {
    //   isShowPicker.value = false
    //   return
    // }

    selectData.value = selectedValues.name
    // isShowPicker.value = false
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }
  function onLevelChange(level) {
    if (level === levelType.value) {
      return
    }

    levelType.value = level
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }

  function onTabFilterChange({ name }) {
    if (name === tabActive.value) {
      return
    }
    tabActive.value = name
    selectData.value = '产业链'
    levelType.value = '市级'
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }

  function onIndustrySelect({ name }) {
    selectData.value = name
    showSelect.value = false
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }

  const total = Symbol()
  const paginate = ref({
    pageNum: 1,
    pageSize: 10,
    [total]: 0,
  })

  async function getPageData() {
    loading.value = true
    try {
      const param = {
        page: paginate.value,
        params: {
          industrychain:
            selectData.value === '产业链' ? undefined : selectData.value,
          // level: levelType.value === '全部' ? undefined : levelType.value,
          level: levelType.value,
          keyWord: keyword.value,
          policyType: tabActive.value,
        },
      }
      const {
        data: { data: res },
      } = await getPageList('policy', param)
      policyList.value.push(...res.list)
      paginate.value[total] = res?.total || 0
      paginate.value.pageNum++
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  function onEnter() {
    if (
      loading.value ||
      (policyList.value.length > paginate.value[total] &&
        paginate.value[total] !== 0)
    ) {
      return
    }
    // router.replace({
    //   path: '/cygk/policy-list',
    //   query: {
    //     ...route.query,
    //     key: keyword.value,
    //   },
    // })
    paginate.value.pageNum = 1
    paginate.value[total] = 0
    policyList.value = []
    getPageData()
  }

  function onDownLoad(e) {
    if (
      loading.value ||
      (policyList.value.length >= paginate.value[total] &&
        paginate.value[total] !== 0)
    ) {
      return
    }

    let scrollTop = e.target.scrollTop
    let scrollHeight = e.target.scrollHeight
    let offsetHeight = Math.ceil(e.target.getBoundingClientRect().height)
    let currentHeight = scrollTop + offsetHeight
    // if (currentHeight >= scrollHeight - 50) {
    //   getPageData()
    // }
    if (currentHeight >= scrollHeight) {
      getPageData()
    }
  }

  onMounted(() => {
    getPageData()
    listRef.value.addEventListener('scroll', onDownLoad)
  })

  onBeforeUnmount(() => {
    listRef.value.removeEventListener('scroll', onDownLoad)
  })

  function toDetail(id) {
    router.push(`/enterprise-side/policy-detail?id=${id}`)
  }

  function getEndTime(time) {
    return time || '长期'
  }
</script>

<style lang="scss" scoped>
  .policy_list {
    overflow-y: auto;
    height: 100%;

    // padding-bottom: 56px;
    background: #f7f8fa;

    .banner {
      height: 153px;

      .banner_image {
        object-fit: cover;
        width: 100%;
        height: 153px;
      }
    }

    .content_filter {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fff;

      .content_select_button {
        display: flex;
        align-items: center;
        color: #323233;
        font-size: 15px;
        line-height: 22px;

        .iconfont {
          margin-left: 12px;
          font-size: 9px;
          transition: all 0.1s linear;
        }

        &.select {
          color: #1255e4;

          .iconfont {
            transform: rotate(180deg);
          }
        }
      }

      .content_filter_group {
        display: flex;
        flex: 1;
        gap: 16px;
        align-content: center;
        overflow: hidden;
        padding-top: 12px;

        .content_filter_button {
          flex-shrink: 0;
          padding: 4px 8px;
          border-radius: 12px;
          background: #f3f3f3;
          color: rgb(0 0 0 / 60%);
          font-size: 12px;
          line-height: 16px;
          cursor: pointer;

          &.active {
            background: #1255e4;
            color: #fff;
          }

          &_select {
            display: flex;
            align-items: center;
            overflow: hidden;
            height: 24px;
            padding: 1px 8px;
            border-radius: 50px;
            background: rgb(18 85 228 / 6%);
            color: rgb(0 0 0 / 60%);
            font-size: 12px;
          }
        }
      }

      // .content_count {
      //   font-size: 12px;
      //   color: rgba(0, 0, 0, 0.4);
      //   line-height: 20px;
      // }
    }

    .content_filter_input {
      width: 100%;
      height: 20px;
      margin-bottom: 22px;
    }

    .list {
      padding: 8px;

      .policy_card {
        position: relative;
        margin-top: 16px;
        border-radius: 12px;
        background: #fff;
        box-shadow: 0 0 20px 0 rgb(0 0 0 / 5%);

        .policy_tag {
          padding: 1px 4px;
          background: #fff;
          font-size: 12px;
          line-height: 16px;
        }

        .policy_id {
          position: absolute;
          top: -6px;
          left: 0;
          min-width: 182px;
          height: 38px;
          padding-right: 16px;
          background-image: v-bind(cardTopBg);
          background-size: 100% 100%;
          background-repeat: no-repeat;
          color: #f7f8fa;
          font-size: 12px;
          line-height: 28px;
          white-space: nowrap;

          // .policy_bg {
          //   position: absolute;
          // }
        }

        .policy_top {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 12px;

          .policy_id_text {
            color: #1255e4;
            font-size: 12px;
            text-transform: none;
          }
        }

        .policy_title {
          margin-top: 6px;
          color: rgb(0 0 0 / 90%);
          font-size: 16px;
          line-height: 24px;
        }

        .policy_content {
          margin-top: 14px;
          color: rgb(0 0 0 / 40%);
          font-size: 12px;
          line-height: 20px;
        }

        .policy_bottom {
          // display: flex;
          // justify-content: space-between;
          // align-items: center;
          margin-top: 10px;

          .policy_tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            margin-bottom: 10px;

            .policy_tag {
              background: rgb(25 137 250 / 10%);
              color: #1989fa;
            }
          }

          .policy_time_box {
            flex-shrink: 0;
          }

          .policy_time {
            display: flex;
            align-items: center;
            color: rgb(0 0 0 / 40%);
            font-size: 12px;
            line-height: 20px;

            .iconfont {
              margin-right: 4px;
            }
          }
        }

        &.loading {
          text-align: center;
        }
      }
    }
  }
</style>
