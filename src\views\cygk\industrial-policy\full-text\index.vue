<template>
  <div class="full-text">
    <iframe
      :src="website"
      width="100%"
      height="600px"
      frameborder="0"></iframe>
  </div>
</template>

<script setup>
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const { website } = route.query
</script>

<style lang="scss" scoped>
  .full-text {
    display: flex;
    flex-direction: column;
    height: 100vh;
  }

  iframe {
    flex: 1;
    width: 100%;
    border: none;
  }
</style>
