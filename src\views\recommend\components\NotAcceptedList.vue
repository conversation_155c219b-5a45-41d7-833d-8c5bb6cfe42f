<template>
  <div class="container">
    <div class="notice">
      <i class="iconfont icon-Union" />
      <div class="ml-[8px]">未承接企业将会在3天后进行区内调换</div>
    </div>
    <FilterSelect
      :show-area="auth === 'coordination'"
      :init-data="recommendStore.noAcceptInfo.filterData"
      @search="onFilter" />
    <EnterpriseList
      :list="recommendStore.noAcceptInfo.list"
      :can-load="
        !loading &&
        recommendStore.noAcceptInfo.list.length <
          recommendStore.noAcceptInfo.pagination.total
      "
      :is-accept="auth === 'merchants'"
      :is-edit="auth === 'coordination'"
      :loading
      :default-scroll="recommendStore.noAcceptInfo.scroll"
      @edit="onEdit"
      @accept="onAccept"
      @item-click="onItemClick"
      @down="onLoadData"
      @scroll-y="onListScroll" />
  </div>
</template>

<script setup>
  import { getRecommendList, trunover } from '@/apis/recommend'
  import { useRecommendStore } from '@/stores/recommend'
  import { showToast } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import wx from 'weixin-js-sdk'
  import { initParams, listInfoHandle } from '../options'
  import EnterpriseList from './EnterpriseList.vue'
  import FilterSelect from './FilterSelect/index.vue'

  const router = useRouter()
  const route = useRoute()
  const recommendStore = useRecommendStore()

  const loading = ref(false)
  const userInfo = ref({})

  const auth = computed(() => {
    return userInfo.value?.operationPermissions?.type
  })

  function onListScroll(y) {
    recommendStore.noAcceptInfo.scroll = y
  }

  async function getEnterpriseList(pageObj) {
    if (!auth.value) {
      return
    }
    try {
      loading.value = true
      // 判断用户是否为招商员
      const isMerchants =
        userInfo.value.operationPermissions?.type === 'merchants'
      console.log('isMerchants', isMerchants, userInfo.value)

      const params = initParams(
        recommendStore.noAcceptInfo.filterData,
        isMerchants ? userInfo.value.bid : undefined,
        false,
        {
          pageNum: pageObj
            ? pageObj.pageNum
            : recommendStore.noAcceptInfo.pagination.pageNum,
          pageSize: pageObj
            ? pageObj.pageSize
            : recommendStore.noAcceptInfo.pagination.pageSize,
        },
      )
      const {
        data: { data: res },
      } = await getRecommendList(params)
      recommendStore.noAcceptInfo.list.push(...listInfoHandle(res.list))
      recommendStore.noAcceptInfo.pagination.total = res.total
      recommendStore.noAcceptInfo.pagination.pageNum = res.page + 1
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(async () => {
    userInfo.value = await recommendStore.getRecommendUserInfo(
      route.query.userName,
    )

    if (recommendStore.noAcceptInfo.list.length === 0) {
      getEnterpriseList()
    }
  })

  async function onEdit(info) {
    console.log('分配', info)

    try {
      await trunover({
        companyId: info.item.companyId,
        userBid: info.value[1],
      })
      showToast('分配成功')
      recommendStore.noAcceptInfo.list = []
      recommendStore.noAcceptInfo.pagination.pageNum = 1
      recommendStore.noAcceptInfo.pagination.total = 0
      getEnterpriseList()
    } catch (error) {
      console.log(error)
    }
  }

  function onAccept(info) {
    const url = `/pages/project/create?token=${route.query.token}&code=${info.creditCode}&companyName=${info.companyName}`
    console.log('跳转', url)
    wx.miniProgram.navigateTo({
      url,
    })
  }

  function onLoadData() {
    getEnterpriseList()
  }

  function onFilter(data) {
    recommendStore.noAcceptInfo.list = []
    recommendStore.noAcceptInfo.pagination.pageNum = 1
    recommendStore.noAcceptInfo.pagination.total = 0
    recommendStore.noAcceptInfo.filterData = {
      similarCompanyName: data.keyword.value,
      region: data.area.value,
      before: data.times.value.end.join('-'),
      after: data.times.value.start.join('-'),
    }
    recommendStore.noAcceptInfo.scroll = 0
    getEnterpriseList()
  }

  function onItemClick(item) {
    router.push({
      path: '/recommend-detail',
      query: {
        companyId: item.companyId,
        token: route.query.token,
        userName: route.query.userName,
      },
    })
  }
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px - 44px - 44px);

    .notice {
      display: flex;
      align-items: center;
      width: 100%;
      height: 40px;
      padding-left: 17px;
      background: #fffbe8;
      color: #ed6a0c;
    }

    .filter_select {
      display: flex;
      align-items: center;
      height: 48px;

      .filter_item {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        height: 100%;
      }
    }
  }
</style>
