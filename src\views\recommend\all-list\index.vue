<template>
  <div class="container">
    <FilterSelect
      :industry-options="industryOptions"
      :init-data="recommendStore.allInfo.filterData"
      is-all
      @search="onFilter"
      @clear-area="showArea = ''" />
    <EnterpriseTotal
      :total="recommendStore.allInfo.pagination.total"
      :industry-list="topIndustryByFilter" />
    <EnterpriseList
      :can-load="
        !loading &&
        recommendStore.allInfo.pagination.total >
          recommendStore.allInfo.list.length
      "
      :default-scroll="recommendStore.allInfo.scroll"
      :is-association="true"
      :list="recommendStore.allInfo.list"
      :loading
      is-all
      :handle-industry-list="findTopIndustry"
      @association="onAssociation"
      @down="onLoadData"
      @item-click="onItemClick"
      @scroll-y="onListScroll" />
  </div>
</template>

<script setup>
  import { getIndustry, getRecommendAllList } from '@/apis/recommend'
  import pcaJson from '@/assets/json/pca-code.json'
  import { useRecommendStore } from '@/stores/recommend'
  import { computed, onMounted, ref, toRaw } from 'vue'
  import { useRouter } from 'vue-router'
  import EnterpriseList from '../components/EnterpriseList.vue'
  import EnterpriseTotal from '../components/EnterpriseTotal.vue'
  import FilterSelect from '../components/FilterSelect/index.vue'
  import { listInfoHandle } from '../options'

  const router = useRouter()
  const recommendStore = useRecommendStore()

  const loading = ref(false)
  const showArea = ref('')
  const industryOptions = ref([])

  function onListScroll(y) {
    recommendStore.allInfo.scroll = y
  }

  async function getIndustryOptions() {
    try {
      const {
        data: { data: res },
      } = await getIndustry()
      industryOptions.value = res
    } catch (error) {
      console.log(error)
    }
  }

  async function getRecommendEnterpriseList() {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await getRecommendAllList({
        page: {
          pageNum: recommendStore.allInfo.pagination.pageNum,
          pageSize: recommendStore.allInfo.pagination.pageSize,
        },
        params: {
          companyName: recommendStore.allInfo.filterData.keyword,
          provinceName: recommendStore.allInfo.filterData?.provinceName,
          cityName: recommendStore.allInfo.filterData?.cityName,
          areaName: recommendStore.allInfo.filterData?.areaName,
          industrychainName: recommendStore.allInfo.filterData.industry,
        },
      })

      recommendStore.allInfo.list.push(...listInfoHandle(res.list))
      recommendStore.allInfo.pagination.total = res.total
      recommendStore.allInfo.pagination.pageNum = res.page + 1
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  function eachIndustry(list, deep = 0) {
    let result = []
    list.forEach((item) => {
      if (item.children) {
        const arr = eachIndustry(item.children, deep + 1)
        if (deep !== 0) {
          result.push({
            code: item.code,
            name: item.name,
          })
          result.push(...arr)
        } else {
          result.push({
            code: item.code,
            name: item.name,
            children: arr,
          })
        }
      } else {
        result.push({
          code: item.code,
          name: item.name,
          children: [],
        })
      }
    })
    return result
  }

  const flatIndustryOptions = computed(() => {
    const arr = []

    industryOptions.value
      .map((item) => {
        return {
          children: eachIndustry(item?.children),
        }
      })
      .forEach((item) => {
        arr.push(...item.children)
      })
    console.log('flatIndustryOptions', arr)
    return arr
  })

  function findTopIndustry(list) {
    if (
      !recommendStore.allInfo.filterData.industry ||
      recommendStore.allInfo.filterData.industry.length === 0
    ) {
      return list
    }
    let result = new Set()
    flatIndustryOptions.value?.forEach((item) => {
      recommendStore.allInfo.filterData.industry?.forEach((i) => {
        const node = item.children.find((industry) => industry.code === i)
        if (node) {
          result.add(item?.name)
        }
      })
    })
    result = Array.from(result)
    const resultList = []
    list.forEach((item) => {
      if (result.includes(item)) {
        resultList.unshift(item)
      } else {
        resultList.push(item)
      }
    })
    return resultList
  }
  // 一级标签扁平化
  const flatIndustryOptionsFirst = computed(() => {
    let arr = []
    arr = industryOptions.value
      .map((item) => {
        return {
          ...item,
          children: eachIndustry(item.children),
        }
      })
      .map((item) => {
        const children = []
        item.children.forEach((i) => {
          children.push(
            {
              code: i.code,
              name: item.name,
            },
            ...i.children,
          )
        })
        return {
          name: item.name,
          code: item.code,
          children,
        }
      })
    console.log('flatIndustryOptionsFirst', arr)
    return arr
  })

  // 根据筛选项查找一级标签
  const topIndustryByFilter = computed(() => {
    let result = new Set()
    flatIndustryOptionsFirst.value?.forEach((item) => {
      recommendStore.allInfo.filterData.industry?.forEach((industryItem) => {
        const node = item.children.find(
          (industry) => industry.code === industryItem,
        )
        if (node) {
          result.add(item?.name)
        }
      })
    })
    console.log('topIndustryByFilter', Array.from(result))
    return Array.from(result)
  })

  onMounted(() => {
    getIndustryOptions()
    if (recommendStore.allInfo.list.length === 0) {
      getRecommendEnterpriseList()
    }
  })

  function onItemClick(item) {
    console.log('跳转', item)
    router.push({
      path: '/recommend-detail',
      query: { companyId: item.companyId, all: true },
    })
  }

  function onFilter(info) {
    const areaCode =
      typeof info.area.value === 'string' || !info.area.value
        ? []
        : info.area.value
    let areaResult = []
    areaResult = areaCode.map((item) => transformAreaCode(item))
    const result = { provinceName: [], cityName: [], areaName: [] }
    areaResult.forEach((item) => {
      result.provinceName.push(item[0])
      item[1] && result.cityName.push(item[1])
      item[2] && result.areaName.push(item[2])
    })
    result.provinceName = Array.from(new Set(result.provinceName))
    result.cityName = Array.from(new Set(result.cityName))
    result.areaName = Array.from(new Set(result.areaName))
    console.log('地区', areaResult)
    info = { ...info, ...result }
    console.log('筛选', info)
    recommendStore.allInfo.filterData = toRaw(info)
    recommendStore.allInfo.list = []
    recommendStore.allInfo.pagination.pageNum = 1
    recommendStore.allInfo.pagination.total = 0
    recommendStore.allInfo.scroll = 0
    getRecommendEnterpriseList()
  }

  function transformAreaCode(code) {
    let areaObj = null
    const areaResult = []
    if (code.length >= 2) {
      areaObj = pcaJson.find((item) => item.code === code.slice(0, 2))
      areaResult.push(areaObj.name)
      showArea.value = areaObj.name
      console.log('areaObj', areaObj)

      if (code.length >= 4) {
        areaObj = areaObj.children.find(
          (item) => item.code === code.slice(0, 4),
        )
        areaResult.push(areaObj.name === '市辖区' ? '' : areaObj.name)
        showArea.value = areaObj.name
        console.log('areaObj', areaObj)

        if (code.length === 6) {
          areaObj = areaObj.children.find((item) => item.code === code)
          areaResult.push(areaObj.name)
          showArea.value = areaObj.name
          console.log('areaObj', areaObj)
        }
      }
    }
    return areaResult
  }

  function onAssociation(info) {
    const _info = toRaw(info)
    router.push(`/local-connection?companyName=${_info.companyName}`)
  }

  function onLoadData() {
    getRecommendEnterpriseList()
  }
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px - 44px);
  }
</style>
