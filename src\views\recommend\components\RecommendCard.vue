<template>
  <div class="card_item">
    <div class="title">
      <div class="deco"></div>
      <div class="title-text">推送信息</div>
    </div>
    <div class="content">
      <div class="flex">
        <div class="label">推送状态：</div>
        <div class="value">
          <div v-if="info.isUndertaken">已承接</div>
          <div v-else>未承接</div>
        </div>
      </div>
      <div class="mt-[6px] flex">
        <div class="label">推送区域：</div>
        <div class="value">
          {{ info.region }}
        </div>
      </div>
      <div class="mt-[6px] flex">
        <div class="label">接收人：</div>
        <div class="value">{{ info.sxlNickname || '-' }}</div>
      </div>
      <div class="mt-[6px] flex">
        <div class="label">推送时间：</div>
        <div class="value">
          {{ info.createdAt }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })
</script>

<style lang="scss" scoped>
  .card_item {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;

    .title {
      display: flex;
      align-items: center;

      .deco {
        width: 4px;
        height: 16px;
        margin-right: 4px;
        border-radius: 16px;
        background: #1255e4;
      }

      .title-text {
        color: rgb(0 0 0 / 88%);
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
      }
    }

    .content {
      margin-top: 12px;
      font-size: 14px;
      line-height: 22px;

      .label {
        flex-shrink: 0;
        width: 80px;
        color: rgb(0 0 0 / 60%);
      }

      .value {
        color: rgb(0 0 0 / 90%);
        word-break: break-all;
      }
    }
  }
</style>
