/**
 * // 使用示例：
    const tree = [
      {
        id: '1',
        label: 'Node 1',
        children: [
          {
            id: '1-1',
            label: 'Node 1-1',
            children: [
              {
                id: '1-1-1',
                label: 'Node 1-1-1'
              }
            ]
          }
        ]
      }
    ];

    // 查找特定节点
    const node = TreeUtils.findNode(tree, node => node.id === '1-1-1');
    console.log('Found node:', node);

    // 查找节点路径
    const path = TreeUtils.findPath(tree, node => node.id === '1-1-1');
    console.log('Node path:', path.map(n => n.label));

    // 过滤树
    const filteredTree = TreeUtils.filterTree(tree, node =>
      node.label.includes('1-1')
    );
    console.log('Filtered tree:', filteredTree);

    // 更新节点
    const updatedTree = TreeUtils.updateNode(
      tree,
      node => node.id === '1-1-1',
      node => ({ ...node, label: 'Updated Node' })
    );
    console.log('Updated tree:', updatedTree);
 */
const TreeUtils = {
  /**
   * 遍历树结构
   * @param {Array | object} tree - 树结构数据
   * @param {Function} handler - 处理函数，返回 false 时停止遍历
   * @param {object} options - 配置项
   * @param {string} options.childrenKey - 子节点的键名，默认 'children'
   */
  traverse(tree, handler, options = {}) {
    const { childrenKey = 'children' } = options
    const array = Array.isArray(tree) ? tree : [tree]

    for (const node of array) {
      if (handler(node) === false) return false
      if (node[childrenKey]) {
        if (this.traverse(node[childrenKey], handler, options) === false) {
          return false
        }
      }
    }
  },

  /**
   * 查找节点
   * @param {Array | object} tree - 树结构数据
   * @param {Function} predicate - 断言函数
   * @param {object} options - 配置项
   * @returns {object | undefined} 找到的节点
   */
  findNode(tree, predicate, options = {}) {
    let result
    this.traverse(
      tree,
      (node) => {
        if (predicate(node)) {
          result = node
          return false // 停止遍历
        }
      },
      options,
    )
    return result
  },

  /**
   * 查找节点路径
   * @param {Array | object} tree - 树结构数据
   * @param {Function} predicate - 断言函数
   * @param {object} options - 配置项
   * @returns {Array} 节点路径
   */
  findPath(tree, predicate, options = {}) {
    const { childrenKey = 'children' } = options
    const path = []

    const find = (nodes) => {
      const array = Array.isArray(nodes) ? nodes : [nodes]

      for (const node of array) {
        path.push(node)
        if (predicate(node)) return true

        if (node[childrenKey]) {
          if (find(node[childrenKey], node)) return true
        }
        path.pop()
      }
      return false
    }

    find(tree)
    return path
  },

  /**
   * 过滤树节点
   * @param {Array | object} tree - 树结构数据
   * @param {Function} predicate - 断言函数
   * @param {object} options - 配置项
   * @returns {Array} 过滤后的节点数组
   */
  filterTree(tree, predicate, options = {}) {
    const { childrenKey = 'children' } = options
    const array = Array.isArray(tree) ? tree : [tree]

    return array.reduce((acc, node) => {
      const copy = { ...node }

      if (node[childrenKey]) {
        copy[childrenKey] = this.filterTree(
          node[childrenKey],
          predicate,
          options,
        )
      }

      if (predicate(copy) || (copy[childrenKey] && copy[childrenKey].length)) {
        acc.push(copy)
      }

      return acc
    }, [])
  },

  /**
   * 更新树节点
   * @param {Array | object} tree - 树结构数据
   * @param {Function} predicate - 断言函数
   * @param {Function} updater - 更新函数，接收当前节点和父节点作为参数
   * @param {object} options - 配置项
   * @param {object} parentNode - 父节点（内部使用）
   * @returns {Array} 更新后的树
   */
  updateNode(tree, predicate, updater, options = {}, parentNode = null) {
    const { childrenKey = 'children' } = options
    const array = Array.isArray(tree) ? tree : [tree]

    return array.map((node) => {
      let newNode = { ...node }

      if (predicate(node)) {
        newNode = updater(parentNode, newNode)
      }

      if (node[childrenKey]) {
        newNode[childrenKey] = this.updateNode(
          node[childrenKey],
          predicate,
          updater,
          options,
          newNode, // Pass the current node as the parent for child nodes
        )
      }

      return newNode
    })
  },

  /**
   * 将树形结构扁平化为一维数组
   * @param {Array | object} tree - 树结构数据
   * @param {object} options - 配置项
   * @param {string} options.childrenKey - 子节点的键名，默认 'children'
   * @returns {Array} 扁平化后的数组
   */
  flatten(tree, options = {}) {
    const { childrenKey = 'children' } = options
    const result = []

    this.traverse(
      tree,
      (node) => {
        result.push(node)
      },
      { childrenKey },
    )

    return result
  },
}

export default TreeUtils
