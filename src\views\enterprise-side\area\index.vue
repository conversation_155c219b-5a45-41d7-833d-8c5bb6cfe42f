<template>
  <div class="area_box">
    <div class="bg-white py-[8px]">
      <!-- <div class="flex h-[24px] flex-1 items-center justify-center gap-[6px]"@click="showMenu = true"> -->
      <div class="flex h-[24px] flex-1 items-center justify-center gap-[6px]">
        <span
          class="currentTiele text-[16px] font-bold leading-[24px] text-[#000]">
          {{ selectArea }}
        </span>
        <i
          class="iconfont icon-caret-down-small text-[10px] text-[rgba(220,222,224,0.9)]"></i>
      </div>
    </div>
    <video
      width="100%"
      controls
      :src="showVideo"
      :poster="showCover" />

    <div class="flex-1 overflow-hidden">
      <Tabs
        v-model:active="activeTab"
        class="flex h-full flex-col"
        @change="onChange">
        <Tab
          name="1"
          title="查政策"
          class="h-full">
          <Policy :area="selectArea" />
        </Tab>
        <Tab
          name="2"
          title="查园区"
          class="h-full">
          <Park :area="selectArea" />
        </Tab>
        <Tab
          name="3"
          title="查楼宇"
          class="h-full">
          <Building :area="selectArea" />
        </Tab>
      </Tabs>
    </div>
    <ActionSheet
      v-model:show="showMenu"
      :actions="areaSelectOptions"
      cancel-text="取消"
      close-on-click-action
      @select="onSelectType"></ActionSheet>
  </div>
</template>

<script setup>
  import { areaOptions } from '@/views/recommend/options'
  import { ActionSheet, Tab, Tabs } from 'vant'
  import { onMounted, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import Building from './building/index.vue'
  import Park from './park/index.vue'
  import Policy from './policy/index.vue'

  const videos = import.meta.glob('@/assets/videos/area/*.mp4')
  const covers = import.meta.glob('@/assets/videos/cover/*.png')

  const router = useRouter()
  const route = useRoute()

  const activeTab = ref('1')
  const showMenu = ref(false)
  const selectArea = ref('武汉临空港')

  const showVideo = ref('')
  const showCover = ref('')

  const areaSelectOptions = areaOptions.filter(
    (item) => !['驻京办', '驻广办', '驻沪办'].includes(item),
  )

  watch(
    selectArea,
    async (newVal) => {
      try {
        const modulePath = `/src/assets/videos/area/${newVal}.mp4`
        const module = await videos[modulePath]()
        showVideo.value = module.default
      } catch (error) {
        console.error('视频加载失败:', error)
        showVideo.value = null
      }

      try {
        const coverPath = `/src/assets/videos/cover/${newVal}.png`
        const cover = await covers[coverPath]()
        showCover.value = cover.default
      } catch (error) {
        console.error('封面加载失败:', error)
        showCover.value = null
      }
    },
    { immediate: true },
  )

  function onSelectType(option) {
    selectArea.value = option.name
    showMenu.value = false
  }

  function onChange(val) {
    activeTab.value = val
    router.replace(`${route.path}?tab=${val}`)
  }

  onMounted(() => {
    activeTab.value = route.query.tab
  })
</script>

<style lang="scss" scoped>
  .area_box {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    :deep(.van-tabs__content) {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
