<template>
  <div class="target_enp">
    <div class="bg-white px-[16px] py-[16px]">
      <!-- <div class="target_buttons">
        <div
          class="target_button"
          @click="jumpTo('recommend')">
          <span class="text-[15px] text-[#528af6]">优企推荐</span>
          <i
            class="iconfont icon-yqtj text-[30px]"
            :style="getTextGradient(['#95c6fa', '#4d85f6'], '180deg')"></i>
        </div>
        <div
          class="target_button"
          @click="jumpTo">
          <span class="text-[15px] text-[#00A4C7]">推送企业</span>
          <i
            class="iconfont icon-tsqy text-[30px]"
            :style="getTextGradient(['#69D9F4', '#54B5F6'], '180deg')"></i>
        </div>
      </div> -->

      <div class="content_head">
        <Search
          v-model="keyword"
          style="flex: 1; padding: 0"
          shape="round"
          placeholder="搜索企业"
          @search="onSearch" />
        <div
          class="add"
          @click="onAdd">
          添加企业
        </div>
      </div>
    </div>
    <div
      ref="listRef"
      class="list">
      <div
        v-for="(item, index) in list"
        :key="item.bid">
        <div
          :key="index"
          class="relative">
          <EnpCard
            :key="index"
            :info="item"
            @on-share="onShare" />
          <!-- 收藏图标 -->
          <div
            class="absolute right-[16px] top-[8px] flex flex-col items-center"
            @click="handleFav(item, index)">
            <i
              class="iconfont text-[18px]"
              :class="true ? 'icon-StarFilled' : 'icon-StarOutlined'"
              :style="
                true
                  ? getTextGradient(['#FFE292', '#FF9C4A'], 'top')
                  : { color: 'rgba(0, 0, 0, 0.1)' }
              "></i>
            <div class="fav_text">取消</div>
          </div>
        </div>
      </div>
      <div
        v-show="!paginate.hasNext && loading"
        class="rounded-[8px] bg-white p-[12px] text-center text-[12px] text-[rgba(0,0,0,0.6)]">
        加载中...
      </div>
      <Empty
        v-show="!loading && paginate.total === 0"
        description="此处为收藏企业列表，请手动添加" />
    </div>

    <PersonPopup
      v-model:show="showSharePop"
      :list="showShareList"
      title="共享"
      empty-text="暂无符合条件的人员"
      :loading="shareLoading"
      @close="onShareClose"
      @clear="onShareSearch('')"
      @confirm="submitShare" />
  </div>
</template>

<script setup>
  import { getShareTargetList, onShareSubmit, targetPage } from '@/apis/precise'
  import PersonPopup from '@/components/person-popup/index.vue'
  import { usePreciseStore } from '@/stores/percise'
  import { getTextGradient } from '@/utils'
  import { onUnFavEnt } from '@/utils/precise'
  import { useInfiniteScroll } from '@vueuse/core'
  import { useRequest } from 'alova/client'
  import { Empty, Search, showToast } from 'vant'
  import { nextTick, onMounted, ref, useTemplateRef } from 'vue'
  import { useRouter } from 'vue-router'
  import EnpCard from './enp-card.vue'

  const preciseStore = usePreciseStore()

  const listRef = ref(null)

  const router = useRouter()
  // const route = useRoute()

  const loading = ref(false)
  const keyword = ref('')
  const list = ref([])

  const paginate = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    hasNext: true,
  })

  // function jumpTo(type) {
  //   router.push({
  //     path: type === 'recommend' ? '/precise/recommend' : '/precise/push',
  //     query: {
  //       uid: route.query.uid || preciseStore.uid,
  //     },
  //   })
  // }

  function onAdd() {
    router.push({
      path: '/precise/addOrEdit',
      query: {
        key: '添加企业',
      },
    })
  }

  async function getList(
    page = {
      pageNum: 1,
      pageSize: 10,
    },
    params = {
      userBid: preciseStore.uid,
      companyName: '',
    },
    partialRefresh = false,
  ) {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await targetPage({ page, params })
      if (partialRefresh) {
        return res
      }
      paginate.value.total = res.total
      paginate.value.page = res.page
      paginate.value.hasNext = res.hasNext
      list.value.push(...res.list)
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    getList()
  })

  useInfiniteScroll(
    listRef,
    () => {
      paginate.value.hasNext &&
        getList(
          {
            pageNum: paginate.value.page + 1,
            pageSize: paginate.value.pageSize,
          },
          {
            userBid: preciseStore.uid,
            companyName: keyword.value,
          },
        )
    },
    {
      distance: 10,
      canLoadMore: () => {
        return !loading.value && paginate.value.hasNext
      },
    },
  )

  function onSearch() {
    list.value = []
    getList(
      {
        pageNum: 1,
        pageSize: paginate.value.pageSize,
      },
      {
        userBid: preciseStore.uid,
        companyName: keyword.value,
      },
    )
  }

  const cancelItem = ref(null)
  const cancelIndex = ref(null)

  function handleFav(item, index) {
    event.stopPropagation()
    cancelItem.value = item
    cancelIndex.value = index
    onConfirm()
  }

  function onConfirm() {
    onUnFavEnt(preciseStore.uid, cancelItem.value.bid, {
      onSuccess() {
        const thisPage = Math.ceil(
          (cancelIndex.value + 1) / paginate.value.pageSize,
        )

        const start = (thisPage - 1) * paginate.value.pageSize
        getList(
          {
            pageNum: thisPage,
            pageSize: paginate.value.pageSize - 1,
          },
          {
            userBid: preciseStore.uid,
            companyName: keyword.value,
          },
          true,
        ).then((res) => {
          paginate.value.total = res.total
          list.value.splice(start, 10, ...res.list)
          const obj = preciseStore.preciseRecommendCache.list.find(
            (i) => i.companyId === i.companyId,
          )
          obj?.stared && (obj.stared = false)
          cancelItem.value = null
          cancelIndex.value = null
        })
      },
    })
  }

  /** 共享 */
  /** 当前共享操作的项 */
  const shareListRef = useTemplateRef('shareListRef')
  const shareInfo = ref(null)
  const showSharePop = ref(false)
  const sharePeopleList = ref([])
  const shareKeyword = ref('')
  const selectPeople = ref([])
  const shareLoading = ref(false)
  const { send: fetchShareList } = useRequest(getShareTargetList, {
    immediate: false,
  }).onSuccess(({ data }) => {
    sharePeopleList.value = showShareList.value = data
    nextTick(() => {
      shareLoading.value = false
    })
  })
  const showShareList = ref([])
  function onShare(info) {
    shareInfo.value = info
    showSharePop.value = true
    shareLoading.value = true
    nextTick(() => {
      shareListRef.value?.scrollTo({ top: 0 })
    })
    fetchShareList({
      cardBid: shareInfo.value?.cardBid,
      userBid: preciseStore.uid,
    })
  }
  function onShareClose() {
    shareKeyword.value = ''
    selectPeople.value = []
  }
  function onShareSearch(val) {
    showShareList.value = sharePeopleList.value.map((item) => {
      return {
        ...item,
        hidden: !item.nickname.includes(val),
      }
    })
  }
  const { send: sendSubmitShare } = useRequest(onShareSubmit, {
    immediate: false,
  }).onSuccess(() => {
    showToast('共享成功')
    showSharePop.value = false
  })
  function submitShare(vals) {
    sendSubmitShare({
      cardBid: shareInfo.value.bid,
      userBid: preciseStore.uid,
      targetUserBids: vals,
    })
  }
</script>

<style lang="scss" scoped>
  .target_enp {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    .target_buttons {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .target_button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 166px;
        height: 62px;
        padding: 0 16px;
        border-radius: 12px;

        &:nth-child(1) {
          background: linear-gradient(180deg, #e7eeff 0%, #f5f6ff 100%);
        }

        &:nth-child(2) {
          background: linear-gradient(180deg, #e7fbff 0%, #f6fdff 100%);
        }
      }
    }

    .fav_text {
      color: #9e9e9e;
      font-size: 12px;
    }

    .content_head {
      display: flex;
      align-items: center;
      width: 100%;

      .add {
        margin-left: 10px;
        color: #1255e4;
        font-size: 15px;
      }
    }

    .list {
      flex: 1;
      padding: 16px 16px 0;
      overflow-y: auto;
    }
  }

  .share_item {
    padding: 10px 0;
    border-bottom: 1px solid #ebedf0;
  }
</style>
