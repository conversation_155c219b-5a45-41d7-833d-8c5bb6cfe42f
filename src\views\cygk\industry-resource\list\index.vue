<template>
  <div
    ref="listRef"
    class="resource_list">
    <!-- banner -->
    <div class="banner">
      <img
        src="@/assets/images/cygk/resource_list.png"
        alt=""
        class="banner_image" />
    </div>
    <!-- 列表 -->
    <div class="list">
      <div
        v-for="(item, index) in list"
        :key="index"
        class="card"
        @click="goDetail(item.id, item.type_)">
        <div class="title_box">
          <div class="title">
            <div class="title_text">{{ item.name }}</div>
            <div
              v-if="item.type_ === 'colleges'"
              class="college">
              （{{ item?.xy }}）
            </div>
          </div>
          <div
            class="tag"
            :style="{
              background: TAG_COLOR_MAP[item.type_]?.bg,
              color: TAG_COLOR_MAP[item.type_]?.text,
            }">
            {{ RESOURCE_TYPE_MAP[item?.type_] }}
          </div>
        </div>
        <div class="tags">
          <div
            v-for="(tag, tagIndex) in item?.industrychain"
            :key="tagIndex"
            class="tag">
            {{ tag }}
          </div>
        </div>
        <div class="synopsis">
          <TextEllipsis
            rows="2"
            :content="item.jj" />
        </div>
        <div class="address">
          <div class="address_item">
            <div class="address_label">地址：</div>
            <div class="address_content">{{ item.dz }}</div>
          </div>
          <div class="address_item">
            <div class="address_label">行政区：</div>
            <div class="address_content">{{ item.ssxzq }}</div>
          </div>
        </div>
      </div>

      <div
        v-show="list.length === 0 && !loading"
        style="text-align: center"
        class="card">
        暂无数据
      </div>

      <Loading
        v-show="loading"
        class="card loading">
        加载中...
      </Loading>
    </div>
  </div>
</template>

<script setup>
  import { Loading, TextEllipsis } from 'vant'
  import { onBeforeUnmount, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  import { getIndustryResourceList } from '@/apis'
  import { RESOURCE_TYPE_MAP, TAG_COLOR_MAP } from '../options'

  const router = useRouter()
  const route = useRoute()
  const listRef = ref(null)
  const { key } = route.query

  const total = Symbol()
  const paginate = ref({
    pageNum: 1,
    pageSize: 10,
    [total]: 0,
  })

  const list = ref([])
  const loading = ref(false)

  async function getPageData() {
    try {
      loading.value = true
      const params = {
        page: paginate.value,
        params: {
          industrychain: key,
        },
      }
      const {
        data: { data: res },
      } = await getIndustryResourceList(params)
      list.value.push(
        ...res.list.map((item) => {
          let industrychain = item.industrychain
          try {
            industrychain = JSON.parse(industrychain)
          } catch {
            if (industrychain.includes(',')) {
              industrychain = industrychain.split(',')
            } else {
              industrychain = [industrychain]
            }
          }
          return {
            ...item,
            industrychain,
          }
        }),
      )

      paginate.value.pageNum++
      paginate.value[total] = res.total
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  function onDownLoad(e) {
    if (loading.value || list.value.length >= paginate.value[total]) return
    let scrollTop = e.target.scrollTop
    let scrollHeight = e.target.scrollHeight
    let offsetHeight = Math.ceil(e.target.getBoundingClientRect().height)
    let currentHeight = scrollTop + offsetHeight
    if (currentHeight >= scrollHeight) {
      getPageData()
    }
  }

  onMounted(() => {
    getPageData()
    listRef.value.addEventListener('scroll', onDownLoad)
  })

  onBeforeUnmount(() => {
    listRef.value.removeEventListener('scroll', onDownLoad)
  })

  function goDetail(id, type) {
    router.push(`/cygk/resource-detail?id=${id}&type=${type}`)
  }
</script>

<style lang="scss" scoped>
  .resource_list {
    overflow-y: auto;
    height: 100%;
    background: #f7f8fa;

    .banner {
      height: 153px;

      .banner_image {
        object-fit: cover;
        width: 100%;
        height: 153px;
      }
    }

    .list {
      padding: 0 16px;

      .tag {
        padding: 1px 4px;
        border-radius: 2px;
        font-size: 12px;
        text-align: center;
        white-space: nowrap;
      }

      .card {
        margin-bottom: 8px;
        padding: 16px;
        border-radius: 12px;
        box-shadow: 0 0 20px 0 rgb(0 0 0 / 5%);

        .title_box {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            display: flex;
            flex: 1;
            align-items: center;
            margin-right: 4px;

            .title_text {
              color: rgb(0 0 0 / 90%);
              font-size: 16px;
              line-height: 24px;
            }

            .college {
              color: rgb(0 0 0 / 40%);
              font-size: 12px;
              line-height: 20px;
            }
          }

          .tag {
            flex-shrink: 0;
          }
        }

        .tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          margin-top: 8px;

          .tag {
            background: rgb(0 0 0 / 5%);
            color: rgb(0 0 0 / 60%);
          }

          &.loading {
            text-align: center;
          }
        }

        .synopsis {
          margin-top: 15px;
          padding: 4px;
          color: rgb(0 0 0 / 40%);
          font-size: 12px;
          line-height: 20px;
        }

        .address {
          margin-top: 8px;

          .address_item {
            display: flex;
            align-items: center;

            .address_label {
              flex-shrink: 0;
              color: rgb(0 0 0 / 60%);
              font-size: 12px;
              line-height: 20px;
            }

            .address_content {
              overflow: hidden;
              color: rgb(0 0 0 / 40%);
              font-size: 12px;
              line-height: 20px;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }
</style>
