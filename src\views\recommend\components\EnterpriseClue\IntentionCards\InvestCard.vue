<template>
  <div class="invest_card">
    <div class="flex items-center">
      <div class="mr-[8px] flex-1 text-[14px] font-[600]">
        上海坤杰光电科技有限公司
      </div>
      <div class="tag">人工</div>
    </div>
    <div class="mt-[8px] flex items-center justify-between">
      <div class="flex flex-1">
        <div class="label">投资金额：</div>
        <div class="value">100万元</div>
      </div>
      <div class="flex flex-1">
        <div class="label">持股比例：</div>
        <div class="value">0.129032</div>
      </div>
    </div>
  </div>
</template>

<script setup>
  defineProps({
    data: {
      type: Object,
      default: () => ({}),
    },
  })
</script>

<style lang="scss" scoped>
  .invest_card {
    padding: 6px;
    border-radius: 8px;
    background: rgb(18 85 228 / 4%);

    .tag {
      height: 18px;
      padding: 2px 8px;
      border-radius: 13px;
      background: #eda040;
      color: #fff;
      font-size: 10px;
      line-height: 14px;
    }

    .label {
      color: rgb(0 0 0 / 60%);
      font-size: 12px;
    }

    .value {
      color: rgb(0 0 0 / 90%);
      font-size: 12px;
    }

    & + .invest_card {
      margin-top: 8px;
    }
  }
</style>
