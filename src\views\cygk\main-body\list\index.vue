<template>
  <div class="container">
    <FilterSelect
      :industry-options="industryOptions"
      :init-data="mainBodyStore.pageCache.filterData"
      is-all
      :industry-select-options="{
        keepLabelDeep: 0,
        keepMode: 'none',
      }"
      @search="onFilter"
      @clear-area="showArea = ''" />
    <EnterpriseTotal
      :total="mainBodyStore.pageCache.pagination.total"
      :industry-list="topIndustryByFilter" />
    <EnterpriseList
      :can-load="
        !loading &&
        mainBodyStore.pageCache.pagination.total >
          mainBodyStore.pageCache.list.length
      "
      :default-scroll="mainBodyStore.pageCache.scroll"
      :list="mainBodyStore.pageCache.list"
      :loading
      is-all
      :handle-industry-list="findTopIndustry"
      @down="onLoadData"
      @item-click="onItemClick"
      @scroll-y="onListScroll" />
  </div>
</template>

<script setup>
  import { getMainBodyList } from '@/apis/mainbody'
  import { getIndustry } from '@/apis/recommend'
  import { useMainBodyStore } from '@/stores/main-body'
  import { computed, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import EnterpriseList from '../components/EnterpriseList.vue'
  import EnterpriseTotal from '../components/EnterpriseTotal.vue'
  import FilterSelect from '../components/FilterSelect.vue'

  const router = useRouter()
  const mainBodyStore = useMainBodyStore()

  const loading = ref(false)
  const showArea = ref('')
  const industryOptions = ref([])

  function onListScroll(y) {
    mainBodyStore.pageCache.scroll = y
  }

  async function getIndustryOptions() {
    try {
      const {
        data: { data: res },
      } = await getIndustry()
      industryOptions.value = res
    } catch (error) {
      console.log(error)
    }
  }

  async function getList() {
    try {
      loading.value = true
      const params = {
        page: {
          pageNum: mainBodyStore.pageCache.pagination.pageNum,
          pageSize: mainBodyStore.pageCache.pagination.pageSize,
        },
        params: {
          companyName: mainBodyStore.pageCache.filterData.keyword,
          areaName: mainBodyStore.pageCache.filterData.area,
          industrychainName: mainBodyStore.pageCache.filterData.industry,
        },
      }

      const {
        data: { data: res },
      } = await getMainBodyList(params)
      mainBodyStore.pageCache.list.push(...res.list)
      mainBodyStore.pageCache.pagination.total = res.total
      mainBodyStore.pageCache.pagination.pageNum = res.page + 1
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  function eachIndustry(list, deep = 0) {
    let result = []
    list.forEach((item) => {
      if (item.children) {
        const arr = eachIndustry(item.children, deep + 1)
        if (deep !== 0) {
          result.push({
            code: item.code,
            name: item.name,
          })
          result.push(...arr)
        } else {
          result.push({
            code: item.code,
            name: item.name,
            children: arr,
          })
        }
      }
    })
    return result
  }

  // 第二级筛选扁平化
  const flatIndustryOptions = computed(() => {
    const arr = []
    industryOptions.value
      .map((item) => {
        return {
          children: eachIndustry(item.children),
        }
      })
      .forEach((item) => {
        arr.push(...item.children)
      })
    console.log('flatIndustryOptions', arr)
    return arr
  })

  function findTopIndustry(list) {
    if (
      !mainBodyStore.pageCache.filterData.industry ||
      mainBodyStore.pageCache.filterData.industry.length === 0
    ) {
      return list
    }
    let result = new Set()
    flatIndustryOptions.value?.forEach((item) => {
      mainBodyStore.pageCache.filterData.industry?.forEach((i) => {
        const node = item.children.find((industry) => industry.code === i)
        if (node) {
          result.add(item?.name)
        }
      })
    })
    result = Array.from(result)
    const resultList = []
    list.forEach((item) => {
      if (result.includes(item)) {
        resultList.unshift(item)
      } else {
        resultList.push(item)
      }
    })
    return resultList
  }

  // 一级标签扁平化
  const flatIndustryOptionsFirst = computed(() => {
    let arr = []
    arr = industryOptions.value
      .map((item) => {
        return {
          ...item,
          children: eachIndustry(item.children),
        }
      })
      .map((item) => {
        const children = []
        item.children.forEach((i) => {
          children.push(
            {
              code: i.code,
              name: item.name,
            },
            ...i.children,
          )
        })
        return {
          name: item.name,
          code: item.code,
          children,
        }
      })
    console.log('flatIndustryOptionsFirst', arr)
    return arr
  })

  // 根据筛选项查找一级标签
  const topIndustryByFilter = computed(() => {
    let result = new Set()
    flatIndustryOptionsFirst.value?.forEach((item) => {
      mainBodyStore.pageCache.filterData.industry?.forEach((industryItem) => {
        const node = item.children.find(
          (industry) => industry.code === industryItem,
        )
        if (node) {
          result.add(item?.name)
        }
      })
    })
    console.log('topIndustryByFilter', Array.from(result))
    return Array.from(result)
  })

  onMounted(() => {
    getIndustryOptions()
    if (mainBodyStore.pageCache.list.length === 0) {
      getList()
    }
  })

  function onItemClick(item) {
    console.log('跳转', item)
    router.push({
      name: 'main-body-detail',
      query: { companyId: item.companyId },
    })
  }

  function onFilter(info) {
    console.log('筛选', info)

    mainBodyStore.pageCache.filterData = info
    mainBodyStore.pageCache.list = []
    mainBodyStore.pageCache.pagination.pageNum = 1
    mainBodyStore.pageCache.pagination.total = 0
    mainBodyStore.pageCache.scroll = 0
    getList()
  }

  function onLoadData() {
    getList()
  }
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 56px - 44px);
  }
</style>
