import { useDict } from '@/stores/dict'
import { INDUSTRY_OPTIONS } from '@/views/cygk/options'

/**
 * 行业类别和企业类别下拉选择器选项数据
 * @returns
 */
export function usePickerOptions() {
  const { companyTypes } = useDict('companyTypes')

  // 是否楚商
  const isChushangOptions = [
    {
      text: '是',
      value: 0,
    },
    {
      text: '否',
      value: 1,
    },
  ]

  return {
    isChushangOptions,
    companyTypeOptions: companyTypes,
    industryOptions: INDUSTRY_OPTIONS,
  }
}
