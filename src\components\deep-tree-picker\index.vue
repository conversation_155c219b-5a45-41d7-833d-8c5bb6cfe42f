<template>
  <Popup
    v-model:show="show"
    destroy-on-close
    style="height: 70%"
    position="bottom">
    <div class="flex h-full flex-col overflow-hidden">
      <div class="flex items-center justify-between px-[16px] pt-[8px]">
        <div @click="emits('cancel')">取消</div>
        <div @click="emits('confirm', vals, labels)">确认</div>
      </div>
      <div class="checkbox_list">
        <CheckboxGroup
          shape="square"
          :model-value="vals">
          <CheckList
            :options
            :toggle-list="propToggleList"
            :label-name="labelName"
            :value-name="valueName"
            @check="onCheck"
            @toggle="onToggle" />
        </CheckboxGroup>
      </div>
    </div>
  </Popup>
</template>

<script setup>
  import { Popup, CheckboxGroup } from 'vant'
  import { ref, computed, watch, onMounted, nextTick } from 'vue'
  import CheckList from './CheckList.vue'

  const show = defineModel({ type: Boolean, default: false })
  const emits = defineEmits(['confirm', 'cancel'])

  const props = defineProps({
    values: {
      type: Array,
      default: () => [],
    },
    options: {
      type: Array,
      default: () => [],
    },
    labelName: {
      type: String,
      default: 'label',
    },
    valueName: {
      type: String,
      default: 'value',
    },
    selected: {
      type: Array,
      default: () => [],
    },
    hasChildren: {
      type: Boolean,
      default: true,
    },
    // 保留的label层级，控制返回的label
    keepLabelDeep: {
      type: Number,
      default: -1,
    },
    keepMode: {
      validator(value) {
        return ['none', 'multiple'].includes(value)
      },
      default: 'multiple',
    },
  })

  watch(props.values, (val) => {
    checkData.value = new Set(val)
  })

  const toggleList = ref(new Set())

  const propToggleList = computed(() => {
    return Array.from(toggleList.value)
  })

  function onToggle(item) {
    if (toggleList.value.has(item)) {
      toggleList.value.delete(item)
    } else {
      toggleList.value.add(item)
    }
  }

  const checkData = ref(new Set())
  const checkDataForLabel = ref(new Set())

  const vals = computed(() => {
    return Array.from(checkData.value)
  })

  const labels = computed(() => {
    return Array.from(checkDataForLabel.value)
  })

  function onCheck(items, deep) {
    const key = items[props.valueName] ? props.valueName : props.labelName
    const labelKey = items[props.labelName] ? props.labelName : props.valueName
    let val = false
    // 值重复校验
    if (checkData.value.has(items[key])) {
      checkData.value.delete(items[key])
    } else {
      checkData.value.add(items[key])
      val = true
    }
    // 标签重复校验
    if (checkDataForLabel.value.has(items[labelKey])) {
      checkDataForLabel.value.delete(items[labelKey])
    } else {
      checkDataForLabel.value.add(items[labelKey])
    }
    if (props.hasChildren) {
      if (items.children) {
        deepEach(items.children, key, labelKey, val, deep + 1)
      }
    }
  }

  function deepEach(list, key, labelKey, val, deep) {
    list.forEach((item) => {
      // 值校验
      if (val && !checkData.value.has(item[key])) {
        checkData.value.add(item[key])
      } else if (!val && checkData.value.has(item[key])) {
        checkData.value.delete(item[key])
      }
      // 标签校验
      if (props.keepMode === 'multiple') {
        if (val && !checkDataForLabel.value.has(item[labelKey])) {
          if (
            props.keepLabelDeep === -1 ||
            (props.keepLabelDeep !== -1 && deep < props.keepLabelDeep)
          ) {
            checkDataForLabel.value.add(item[labelKey])
          }
        } else {
          checkData.value.delete(item[labelKey])
        }
      }
      if (item.children) {
        deepEach(item.children, key, labelKey, val, deep + 1)
      }
    })
  }

  onMounted(() => {
    nextTick(() => {
      checkData.value = new Set(props.selected)
    })
  })

  defineExpose({
    reset() {
      checkData.value = new Set()
      checkDataForLabel.value = new Set()
    },
  })
</script>

<style lang="scss" scoped>
  .checkbox_list {
    flex: 1;
    padding: 16px 16px 8px;
    overflow-y: auto;

    .checkout_box_item + .checkout_box_item {
      margin-top: 10px;
    }
  }
</style>
