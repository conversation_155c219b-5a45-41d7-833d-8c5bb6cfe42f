<template>
  <div class="corporate-portrait">
    <div class="title">
      <div class="deco"></div>
      <div class="title-text">企业画像</div>
    </div>
    <div class="tab-box">
      <div
        :class="`tab ${activeTab === '基本信息' && 'tab-active'}`"
        @click="onTabClick('基本信息')"
        >基本信息
      </div>
      <!-- <div
        :class="`tab ${activeTab === '财务信息' && 'tab-active'}`"
        @click="onTabClick('财务信息')"
        >财务信息
      </div> -->
      <div
        :class="`tab ${activeTab === '经营信息' && 'tab-active'}`"
        @click="onTabClick('经营信息')"
        >经营信息
      </div>
      <div
        :class="`tab ${activeTab === '知识产权' && 'tab-active'}`"
        @click="onTabClick('知识产权')"
        >知识产权
      </div>
      <div
        :class="`tab ${activeTab === '风险信息' && 'tab-active'}`"
        @click="onTabClick('风险信息')"
        >风险信息
      </div>
    </div>
    <BaseInfo
      v-if="activeTab === '基本信息'"
      :info />
    <FinancialInfo
      v-if="activeTab === '财务信息'"
      :info />
    <ManageInfo
      v-if="activeTab === '经营信息'"
      :info />
    <IntellectualProperty
      v-if="activeTab === '知识产权'"
      :info />
    <RiskInfo
      v-if="activeTab === '风险信息'"
      :info />
  </div>
</template>

<script setup>
  import BaseInfo from '@/views/recommend/components/BaseInfo.vue'
  import FinancialInfo from '@/views/recommend/components/FinancialInfo.vue'
  import IntellectualProperty from '@/views/recommend/components/IntellectualProperty.vue'
  import ManageInfo from '@/views/recommend/components/ManageInfo.vue'
  import RiskInfo from '@/views/recommend/components/RiskInfo.vue'
  import { ref } from 'vue'

  defineProps({
    info: {
      type: Object,
      default: () => ({}),
    },
  })

  const activeTab = ref('基本信息')

  const onTabClick = (tab) => {
    activeTab.value = tab
  }
</script>

<style lang="scss" scoped>
  .corporate-portrait {
    padding: 16px;
    background: #fff;

    .title {
      display: flex;
      align-items: center;

      .deco {
        width: 4px;
        height: 16px;
        margin-right: 4px;
        border-radius: 16px;
        background: #1255e4;
      }

      .title-text {
        color: rgb(0 0 0 / 88%);
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
      }
    }

    .tab-box {
      display: flex;
      flex-wrap: wrap;
      gap: 13px;
      margin-top: 12px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e7e7e7;

      .tab {
        width: 76px;
        height: 28px;
        border-radius: 6px;
        background: #f3f3f3;
        color: rgb(0 0 0 / 60%);
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        text-align: center;
      }

      .tab-active {
        border: 1px solid #0052d9;
        background: #fff;
        color: #0052d9;
      }
    }
  }
</style>
