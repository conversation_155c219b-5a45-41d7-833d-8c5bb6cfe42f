Popup
<template>
  <div class="h-[56px] px-[16px] py-[8px]">
    <Search
      v-model="keyword"
      style="height: 40px"
      placeholder="搜索企业"
      shape="round"
      @search="onSearch"
      @clear="onSearch" />
  </div>
  <div class="filter_select">
    <div class="filter_item">
      <span
        class="mr-[4px]"
        @click="isShowArea = true">
        区域
      </span>
      <Icon
        v-if="area?.length"
        name="close"
        class="text-[#999ba5]"
        @click="
          () => {
            area = []
            _area = []
            onSearch()
          }
        " />
      <i
        v-else
        class="iconfont icon-caret-down-small text-[8px] text-[#DCDEE0]" />
    </div>
    <Popup
      v-if="isShowArea"
      v-model:show="isShowArea"
      style="height: 50%"
      position="bottom">
      <div class="flex h-full flex-col">
        <div class="flex items-center justify-between px-[16px] pt-[8px]">
          <div
            @click="
              () => {
                _area = []
                isShowArea = false
              }
            "
            >取消</div
          >
          <div
            @click="
              () => {
                area = _area
                isShowArea = false
                onSearch()
              }
            ">
            确认
          </div>
        </div>
        <div class="flex-1 overflow-y-auto p-[8px]">
          <CheckboxGroup v-model="_area">
            <Checkbox
              v-for="(areaItem, index) in areaOptions.slice(0, -3)"
              :key="index"
              shape="square"
              class="mb-[8px]"
              :name="areaItem.value">
              {{ areaItem.text }}
            </Checkbox>
          </CheckboxGroup>
        </div>
      </div>
      <!-- <Picker
        :columns="areaOptions"
        @cancel="isShowArea = false"
        @confirm="
          ({ selectedValues }) => {
            area = selectedValues[0]
            isShowArea = false
            onSearch()
          }
        " /> -->
    </Popup>

    <div class="filter_item">
      <span
        class="mr-[4px]"
        @click="isShowIndustry = true">
        产业
      </span>
      <i
        v-show="industry.length === 0"
        class="iconfont icon-caret-down-small text-[8px] text-[#DCDEE0]" />
      <Icon
        v-if="industry.length > 0"
        name="close"
        class="text-[#999ba5]"
        @click="
          () => {
            industry = []
            industryNames = []
            DeepTreePickerRef.reset()
            onSearch()
          }
        " />
    </div>
    <DeepTreePicker
      ref="DeepTreePickerRef"
      v-model:show="isShowIndustry"
      :selected="industry"
      label-name="name"
      value-name="code"
      :options="industryOptions"
      :keep-label-deep="industrySelectOptions.keepLabelDeep"
      :keep-mode="industrySelectOptions.keepMode"
      @cancel="isShowIndustry = false"
      @confirm="
        (vals, labels) => {
          industryNames = labels
          industry = vals
          isShowIndustry = false
          onSearch()
        }
      " />
  </div>
</template>

<script setup>
  import DeepTreePicker from '@/components/deep-tree-picker/index.vue'
  import { Checkbox, CheckboxGroup, Icon, Popup, Search } from 'vant'
  import { onMounted, ref, watch } from 'vue'
  import { areaOptions } from '../options'

  const emits = defineEmits(['search', 'clearArea'])
  const props = defineProps({
    isAll: {
      type: Boolean,
      default: false,
    },
    industryOptions: {
      type: Array,
      default: () => [],
    },
    initData: {
      type: Object,
      default: () => ({}),
    },
    industrySelectOptions: {
      type: Object,
      default: () => ({
        keepLabelDeep: -1,
        keepMode: 'multiple',
      }),
    },
  })

  const DeepTreePickerRef = ref(null)
  const keyword = ref('')
  const area = ref([])
  const _area = ref([])
  const industry = ref([])
  const industryNames = ref([])

  const isShowArea = ref(false)
  const isShowIndustry = ref(false)

  watch(isShowArea, () => {
    _area.value = area.value
  })

  function onSearch() {
    const params = {
      keyword: keyword.value,
      area: area.value,
      industry: industry.value,
      industryNames: industryNames.value,
    }
    emits('search', params)
  }

  onMounted(() => {
    console.log('初始化筛选', props.initData)
    keyword.value = props.initData?.keyword || ''
    area.value = props.initData?.area || []
    _area.value = props.initData?.area || []
    industry.value = props.initData?.industry || []
    industryNames.value = props.initData?.industryNames || []
  })
</script>

<style lang="scss" scoped>
  .filter_select {
    display: flex;
    align-items: center;
    height: 48px;

    .filter_item {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      width: 180px;
      height: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    :deep(.checkout_box_item + .checkout_box_item) {
      margin-top: 10px;
    }
  }
</style>
