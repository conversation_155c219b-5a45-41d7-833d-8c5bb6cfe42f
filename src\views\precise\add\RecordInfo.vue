<template>
  <div class="record_info">
    <div class="info">
      <Form>
        <Cell
          :required="!readOnly"
          title="对接日期"
          :is-link="!readOnly"
          :value="
            formItems.date.join('-') || readOnly
              ? formItems.date.join('-')
              : '请填写对接日期'
          "
          @click="!readOnly && (showPop = 'date')" />
        <Popup
          position="bottom"
          :show="showPop === 'date'"
          round
          @close="
            () => {
              showPop = ''
            }
          ">
          <DatePicker
            @confirm="
              ({ selectedValues }) => {
                formItems.date = selectedValues
                showPop = ''
              }
            "
            @cancel="showPop = ''" />
        </Popup>
        <Field
          v-model="formItems.receptionists"
          label="接待人"
          :required="!readOnly"
          input-align="right"
          :placeholder="readOnly ? '' : '请填写接待人'" />
        <Field
          v-model="formItems.context"
          label="招商有效信息"
          input-align="right"
          :readonly="readOnly"
          :placeholder="readOnly ? '' : '请输入招商有效信息'"
          type="textarea" />
        <Field
          v-model="formItems.plan"
          :readonly="readOnly"
          input-align="right"
          :placeholder="readOnly ? '' : '请输入后续计划安排信息'"
          label="后续计划安排信息"
          type="textarea" />
        <FieldUpload
          v-model="formItems.fileList"
          :readonly="readOnly"
          label="附件"
          preview-path="/file-view"
          multiple />
      </Form>
    </div>
    <div class="list">
      <div class="flex items-center">
        <div class="flex-1">对接人</div>
        <div
          v-if="!readOnly"
          class="button"
          @click="formItems.personList.push({})">
          添加对接人
        </div>
      </div>
      <div class="person_list">
        <div
          v-for="(item, index) in formItems.personList"
          :key="index"
          class="person_item">
          <div class="item_line flex items-center">
            <div class="item_title">对接人{{ index + 1 }}</div>
            <div
              v-if="!readOnly"
              class="delete"
              @click="formItems.personList.splice(index, 1)">
              <span>删除</span>
            </div>
          </div>
          <Field
            v-model="item.name"
            label="姓名"
            :readonly="readOnly"
            placeholder="请填写姓名"></Field>
          <Field
            v-model="item.phone"
            label="联系方式"
            :readonly="readOnly"
            placeholder="请填写联系方式"></Field>
          <Field
            v-model="item.job"
            label="职务"
            :readonly="readOnly"
            placeholder="请填写职务"></Field>
        </div>
        <Empty v-if="formItems.personList.length === 0">暂无对接人</Empty>
      </div>
    </div>
    <Button
      v-if="!createdBy || createdBy === preciseStore.uid"
      block
      class="submit_button"
      type="primary"
      @click="onSave">
      {{ pageType === 'view' ? '编辑' : '保存' }}
    </Button>
  </div>
</template>

<script setup>
  import { addRecord, editRecord, getRecordDetail } from '@/apis/precise'
  import FieldUpload from '@/components/form/field-upload.vue'
  import { useAddStore } from '@/stores/add'
  import { usePreciseStore } from '@/stores/percise'
  import { isEmpty } from '@/utils/is'
  import { useRequest } from 'alova/client'
  import {
    Button,
    Cell,
    DatePicker,
    Empty,
    Field,
    Form,
    Popup,
    showToast,
  } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  const route = useRoute()
  const router = useRouter()
  const { type } = route.query
  const addStore = useAddStore()
  const preciseStore = usePreciseStore()

  /** 当前打开的记录bid */
  const id = route.query.id || ''
  const pageType = ref(type)

  const showPop = ref('')
  const createdBy = ref('')
  const readOnly = computed(
    () =>
      pageType.value === 'view' ||
      (createdBy.value !== preciseStore.uid && pageType.value !== 'add'),
  )
  const formItems = ref({
    date: [],
    receptionists: '',
    context: '',
    plan: '',
    fileList: [],
    personList: [],
  })

  const { send: fetchDetail } = useRequest(getRecordDetail, {
    immediate: false,
  }).onSuccess(({ data }) => {
    formItems.value = {
      date: data.communicationDate.split('-'),
      receptionists: data.receptionists,
      context: data.context,
      plan: data.plan,
      fileList: data.attachments.trim().length
        ? data.attachments.split(',').map((i) => ({ url: i }))
        : [],
      personList: data.clients || [],
    }
    createdBy.value = data.createdBy
  })

  onMounted(() => {
    if (pageType.value !== 'add') {
      if (addStore.pageType === 'add') {
        formItems.value = {
          date: addStore.records[id].communicationDate.split('-'),
          receptionists: addStore.records[id].receptionists,
          context: addStore.records[id].context,
          plan: addStore.records[id].plan,
          fileList: addStore.records[id].attachments
            .split(',')
            .map((i) => ({ url: i })),
          personList: addStore.records[id].clients || [],
        }
      } else {
        fetchDetail(id)
      }
    }
  })

  function onSave() {
    if (pageType.value === 'view') {
      pageType.value = 'edit'
    } else {
      onSubmit()
    }
  }

  const { send: fetchAdd } = useRequest(addRecord, {
    immediate: false,
  }).onSuccess(() => {
    showToast('新增成功')
    router.back()
  })

  const { send: fetchUpdate } = useRequest(editRecord, {
    immediate: false,
  }).onSuccess(() => {
    showToast('编辑成功')
    router.back()
  })

  function onSubmit() {
    if (
      !formItems.value.date ||
      formItems.value.date.length === 0 ||
      !formItems.value.receptionists
    ) {
      return showToast('请填写日期和接待人')
    }
    if (addStore.pageType === 'add') {
      addStore.records.unshift({
        communicationDate: formItems.value.date.join('-') || '',
        receptionists: formItems.value.receptionists,
        context: formItems.value.context,
        plan: formItems.value.plan,
        attachments: formItems.value.fileList.map((i) => i.url).join(','),
        clients: formItems.value.personList.filter((i) => !isEmpty(i)),
      })
      router.back()
    } else {
      if (pageType.value === 'add') {
        fetchAdd({
          cardBid: addStore.cardBid,
          userBid: preciseStore.uid,
          communicationDate: formItems.value.date.join('-') || '',
          receptionists: formItems.value.receptionists,
          context: formItems.value.context,
          plan: formItems.value.plan,
          attachments: formItems.value.fileList.map((i) => i.url).join(','),
          clients: formItems.value.personList.filter((i) => !isEmpty(i)),
        })
      } else if (pageType.value === 'edit') {
        fetchUpdate({
          bid: id,
          userBid: preciseStore.uid,
          communicationDate: formItems.value.date.join('-') || '',
          receptionists: formItems.value.receptionists,
          context: formItems.value.context,
          plan: formItems.value.plan,
          attachments: formItems.value.fileList.map((i) => i.url).join(','),
          clients: formItems.value.personList.filter((i) => !isEmpty(i)),
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .record_info {
    width: 100%;
    height: 100%;
    padding: 16px;
    overflow-y: auto;
    background: #f7f8fa;

    .info {
      border-radius: 8px;
      background-color: #fff;
    }

    .list {
      margin-top: 32px;
      margin-bottom: 16px;
      color: #969799;
      font-size: 14px;

      .button {
        color: #1255e4;
        font-size: 14px;
        line-height: 20px;
      }

      .person_list {
        margin-top: 16px;

        .person_item {
          border-radius: 8px;
          background-color: #fff;

          .item_line {
            padding: 12px 16px;

            .item_title {
              flex: 1;
              color: #1255e4;
              font-weight: 500;
              font-size: 16px;
            }

            .delete {
              color: #f05542;
              font-size: 14px;
            }
          }

          & + .person_item {
            margin-top: 16px;
          }
        }
      }
    }

    .submit_button {
      position: sticky;
      bottom: 0px;
    }
  }
</style>
