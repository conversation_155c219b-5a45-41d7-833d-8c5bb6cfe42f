<template>
  <!-- <BackButton v-show="hasBack" /> -->
  <div class="main_page">
    <TopHeader></TopHeader>
    <div class="router">
      <RouterView />
    </div>
    <FooterTab v-if="hasFooter" />
  </div>
</template>

<script setup>
  import FooterTab from '@/components/footer-tab/index.vue'
  import TopHeader from '@/components/top-header/index.vue'
  import { excludeFooter } from './utils/exclude-layout'

  import { computed } from 'vue'

  import { RouterView, useRoute } from 'vue-router'

  const route = useRoute()

  // const hasBack = computed(() => {
  //   if (route.query.isBack === 'false') {
  //     return false
  //   } else if (route.query.isBack === 'true') {
  //     return true
  //   }
  //   return !excludeBack.some((item) => {
  //     if (item instanceof RegExp) {
  //       return item.test(route.path)
  //     } else {
  //       return item === route.path
  //     }
  //   })
  // })

  const hasFooter = computed(() => {
    if (route.query.noFooter) {
      return false
    }
    return !excludeFooter.some((item) => {
      if (item instanceof RegExp) {
        return item.test(route.path)
      } else {
        return item === route.path
      }
    })
  })
</script>

<style lang="scss" scoped>
  .main_page {
    display: flex;
    flex-direction: column;
    height: 100vh;

    .router {
      flex: 1;
      overflow: auto;
    }
  }
</style>
