<template>
  <div class="contacts">
    <div
      v-for="item in list"
      :key="item.bid || item.c_bid"
      class="item">
      <Row class="info">
        <Col
          class="truncate"
          span="6">
          {{ item.name }}
        </Col>
        <Col
          class="rl_border truncate"
          span="10">
          {{ item.post }}
        </Col>
        <Col
          class="truncate"
          span="8">
          {{ item.contactInformation }}
        </Col>
      </Row>
    </div>
  </div>
</template>

<script setup>
  import { Col, Row } from 'vant'

  defineEmits(['edit', 'delete', 'itemClick'])

  defineProps({
    list: {
      type: Array,
      default: () => [],
    },
  })
</script>

<style lang="scss" scoped>
  .contacts {
    .item {
      border-radius: 8px;
      background: #ffffff;

      .info {
        padding: 16px 0;
        color: rgba(0, 0, 0, 0.88);
        font-size: 14px;
        line-height: 22px;
        text-align: center;

        .rl_border {
          border: none;
          border-right: 1px solid rgba(0, 0, 0, 0.06);
          border-left: 1px solid rgba(0, 0, 0, 0.06);
        }
      }

      & + .item {
        margin-top: 16px;
      }
    }
  }
</style>
