<template>
  <div class="policy_detail">
    <!-- <BackButton /> -->
    <!-- banner -->
    <!-- <div class="banner">
      <img
        src="@/assets/images/cygk/policy-detail.png"
        alt=""
        class="banner_image" />
    </div> -->

    <div class="content">
      <Loading v-show="loading">加载中...</Loading>
      <div class="content_title">
        {{ pageData.name }}
      </div>
      <div class="content_info">
        <div>{{ pageData.number }}</div>
        <!-- <div>{{ pageData.time?.split(' ')?.[0] }}</div> -->
      </div>
      <div class="content_file">
        <div class="label">文件链接:</div>
        <div
          class="link"
          :show="pageData.website"
          @click="copyLink">
          {{ pageData.website }}
          <span>点击复制链接，然后在浏览器中查看。</span>
        </div>
        <!-- <a
          class="link"
          @click="toFullText">
          {{ pageData.website }}
        </a> -->
      </div>
      <div class="content_text">
        <p
          v-for="(item, index) in content"
          :key="index"
          class="content_item">
          {{ item }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { Loading, showFailToast, showSuccessToast } from 'vant'
  import { computed, onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'

  import { getKeyDetail } from '@/apis'
  // import BackButton from '@/components/back/index.vue'

  const route = useRoute()
  // const router = useRouter()
  const { id } = route.query

  const pageData = ref({})
  const loading = ref(false)

  const content = computed(() => {
    let content = pageData.value.content || pageData.value.synopsis || ''
    content = content?.replace(/\\n/g, '\n')
    return content?.split('\n')
  })
  // function toFullText() {
  //   router.push(`/cygk/policy-full-text?website=${pageData.value.website}`)
  // }
  async function getPageData() {
    try {
      loading.value = true
      const {
        data: { data: res },
      } = await getKeyDetail('policy', {
        id,
      })
      pageData.value = res
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }
  const copyLink = async () => {
    try {
      await navigator.clipboard.writeText(pageData.value.website)
      showSuccessToast('复制成功')
    } catch (err) {
      showFailToast('复制失败')
      console.log(err)
    }
  }
  onMounted(() => {
    getPageData()
  })
</script>

<style lang="scss" scoped>
  .banner {
    height: 153px;

    .banner_image {
      object-fit: cover;
      width: 100%;
      height: 153px;
    }
  }

  .content {
    padding: 12px 16px;

    .content_title {
      color: #1255e4;
      font-weight: 600;
      font-size: 18px;
      line-height: 26px;
    }

    .content_info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;
      padding-bottom: 16px;
      border-bottom: 1px solid rgb(0 0 0 / 6%);
      color: rgb(0 0 0 / 40%);
      font-size: 14px;
      line-height: 22px;
    }

    .content_file {
      margin-top: 16px;

      .label {
        color: rgb(0 0 0 / 60%);
        font-size: 14px;
        line-height: 22px;
      }

      .link {
        display: block;
        color: #1255e4;
        word-break: break-all;
      }
    }

    .content_text {
      margin-top: 24px;
      color: rgb(0 0 0 / 60%);
      font-size: 14px;
      line-height: 22px;

      .content_item {
        text-indent: 2em;
      }
    }
  }
</style>
