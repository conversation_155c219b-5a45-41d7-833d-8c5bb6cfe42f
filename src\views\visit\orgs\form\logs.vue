<template>
  <div class="page">
    <Skeleton
      v-if="loading"
      title
      :row="20" />
    <Space
      v-if="list.length > 0"
      direction="vertical"
      :size="16"
      fill>
      <CellGroup
        v-for="(item, index) in list"
        :key="index"
        inset>
        <Cell>
          <template #title>
            <h1>{{ item.updatedAt }}</h1>
          </template>
          <template #value>
            <span class="record_add_name">
              {{ item.updatedByName }}
            </span>
          </template>
        </Cell>
        <Field
          :model-value="item.fieldName"
          readonly
          label="更改字段" />
        <Field
          :model-value="item.before"
          readonly
          label="更改前" />
        <Field
          :model-value="item.after"
          readonly
          label="更改后" />
      </CellGroup>
    </Space>
    <div
      v-else
      class="pl-[16px] pr-[16px]">
      <Empty
        image-size="8rem"
        description="暂无操作记录" />
    </div>
  </div>
</template>

<script setup>
  import { getLogs } from '@/apis/visit'
  import { useRequest } from 'alova/client'
  import { Cell, CellGroup, Empty, Field, Skeleton, Space } from 'vant'
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'

  const route = useRoute()
  // 机构ID 存在代表编辑机构
  const orgId = route.params.id

  const { loading, data } = useRequest(getLogs(orgId), {
    initialData: [],
  })

  const list = computed(() => {
    const list = []

    for (let index = 0; index < data.value.length; index++) {
      const element = data.value[index]

      if (!element.vos) continue

      for (let index = 0; index < element.vos.length; index++) {
        const vosItem = element.vos[index]

        list.push({
          ...element,
          ...vosItem,
        })
      }
    }

    return list
  })
</script>

<style lang="scss" scoped>
  .page {
    height: 100%;
    padding: 16px 0;
    overflow-y: auto;
    background-color: #f7f8fa;

    h1 {
      color: #1255e4;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }

    .record_add_name {
      color: #969799;
      color: #969799;
      font-size: 14px;
      line-height: 20px;
    }
  }
</style>
