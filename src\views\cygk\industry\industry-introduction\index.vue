<!-- eslint-disable vue/no-v-html -->
<!-- 产业介绍 -->
<template>
  <div class="h-[100%] w-[100%]">
    <!-- <BackButton /> -->
    <div class="flex w-full items-center justify-end pt-[10px]">
      <div
        class="flex h-[24px] flex-1 items-center justify-center gap-[6px]"
        @click="showMenu = true">
        <span
          class="currentTiele text-[16px] font-bold leading-[24px] text-[#000]"
          >{{ isFirst[0] ? '产业链' : currentType }}</span
        >
        <i class="iconfont icon-caret-down-small text-[10px]"></i>
      </div>
      <div
        v-if="menuActionsMap.length"
        class="flex h-[24px] flex-1 items-center justify-center gap-[6px]"
        @click="showMapAction = true">
        <span
          class="currentTiele text-[16px] font-bold leading-[24px] text-[#000]"
          >{{ isFirst[1] ? '产业图谱' : currentActionMap }}</span
        >
        <i class="iconfont icon-caret-down-small text-[10px]"></i>
      </div>
    </div>
    <HumanoidRobotIntroduction v-if="!menuActionsMap.length" />
    <div v-else>
      <KeepAlive>
        <Component :is="currentComponent"></Component>
      </KeepAlive>
    </div>
  </div>
  <!-- 产业列表 -->
  <ActionSheet
    v-model:show="showMenu"
    :actions="menuActions"
    cancel-text="取消"
    close-on-click-action
    @select="onSelectType"></ActionSheet>
  <!-- 产业副列表 -->
  <ActionSheet
    v-model:show="showMapAction"
    :actions="menuActionsMap"
    cancel-text="取消"
    close-on-click-action
    @select="onMapActionSelectType"></ActionSheet>
  <ImagePreview
    v-model:show="show"
    :images="titleImages"
    :close-on-click-image="false"
    :double-scale="true">
  </ImagePreview>
</template>

<script setup>
  import HotBig from '@/assets/images/cygk/hot_big.png'
  import { ActionSheet, ImagePreview } from 'vant'
  import { computed, provide, reactive, ref } from 'vue'
  import { INDUSTRY_INTRO_MAP } from '../../options.js'
  import ApplicationScenarioDiagram from './components/application-scenario-diagram.vue'
  import BusinessPlanningMap from './components/business-planning-map.vue'
  import HumanoidRobotIntroduction from './components/humanoid-robot-introduction.vue'
  import IndustrialPlanningMap from './components/industrial-Planning-Map.vue'
  import IndustryChainLength from './components/industry-chain-length.vue'
  import IndustrySpace from './components/industry-space.vue'
  import InvestmentMap from './components/investment-map.vue'
  import KeyEnterpriseHeat from './components/key-enterprise-heat.vue'
  import keyEnterpriseLocations from './components/key-enterprise-locations.vue'
  import LocationMapInnovative from './components/location-map-innovative.vue'
  import LogisticsPlanningMap from './components/logistics-planning-map.vue'
  const currentType = ref('光芯屏端网')
  provide('currentType', currentType)
  const show = ref(false)
  const showMenu = ref(false)
  const isFirst = ref([true, true])

  const menuActions = Object.keys(INDUSTRY_INTRO_MAP).map((m) => ({ name: m }))
  console.log('INDUSTRY_INTRO_MAP', INDUSTRY_INTRO_MAP)

  const showMapAction = ref(false)
  const menuActionsMap = computed(() => {
    return INDUSTRY_INTRO_MAP[currentType.value].map((m) => ({ name: m }))
  })
  const titleImages = reactive([HotBig])

  const currentActionMap = ref('产业规划图')

  const onSelectType = (options) => {
    currentType.value = options.name
    currentActionMap.value = '产业规划图'
    isFirst.value[0] = false
  }
  const onMapActionSelectType = (options) => {
    currentActionMap.value = options.name
    isFirst.value[1] = false
  }

  const currentComponent = computed(() => {
    const result = {
      // 产业介绍: ProductIntroduction,
      商贸规划图: BusinessPlanningMap,
      物流规划图: LogisticsPlanningMap,
      产业规划图: IndustrialPlanningMap,
      产业空间布局: IndustrySpace,
      重点企业点位图: keyEnterpriseLocations,
      重点企业热力图: KeyEnterpriseHeat,
      创新资源点位图: LocationMapInnovative,
      应用场景示意图: ApplicationScenarioDiagram,
      产业链链长制: IndustryChainLength,
      招商图谱: InvestmentMap,
    }

    return result[currentActionMap.value] || null
  })
</script>
