<!-- 产业概况 -->
<template>
  <div class="flex h-full w-[100%] flex-col bg-[#fff]">
    <!-- <div class="flex w-full items-center justify-around pt-[10px]"> </div> -->
    <Tabs
      :active="currentTab"
      @click-tab="onClickTab">
      <!-- <Tab
        :title-style="{
          width: '200px',
        }"
        :title="currentTab"
        name="产业总体">
        <template #title>
          <span class="w-[200px]">{{ currentTab }}</span>
          <i class="iconfont icon-caret-down-small ml-[10px] text-[10px]"></i>
        </template>
      </Tab> -->
      <Tab
        :title-style="{
          width: '200px',
        }"
        title="产业总体布局"
        name="产业总体布局"></Tab>
      <Tab
        to="/cygk/industry-introduction"
        title="重点产业"
        name="产业介绍"></Tab>
      <Tab
        :title-style="{
          width: '200px',
        }"
        title="各区重点产业布局"
        name="各区重点产业布局"></Tab>
      <!-- <Tab
        title="区域分析"
        name="区域分析"></Tab> -->
    </Tabs>

    <div class="flex-1 overflow-y-auto">
      <div
        v-if="currentTab !== '区域分析'"
        class="bg-[#fff] px-[16px]">
        <div
          v-show="currentTab === '产业总体布局'"
          class="relative mt-[10px]">
          <img
            :src="bottomMap"
            alt=""
            @click="show = true" />
          <img
            class="absolute left-0 top-0"
            :src="currentAreaMap"
            alt="" />
        </div>
        <div
          v-show="currentTab === '各区重点产业布局'"
          class="mt-[10px]">
          <img
            :src="mapH"
            alt=""
            @click="show = true" />
        </div>
      </div>
      <div
        v-show="currentTab === '产业总体布局'"
        class="bottom">
        <div class="white_box">
          <div class="button_list">
            <Button
              class="btn"
              :class="{
                active: currentArea === '中心城区',
              }"
              color="#F3F3F3"
              size="small"
              @click="onClickBtn('中心城区')">
              中心城区
            </Button>
            <Button
              class="btn"
              :class="{
                active: currentArea === '开发区',
              }"
              color="#F3F3F3"
              size="small"
              @click="onClickBtn('开发区')">
              开发区(功能区)
            </Button>
            <Button
              class="btn"
              :class="{
                active: currentArea === '新城区',
              }"
              color="#F3F3F3"
              size="small"
              @click="onClickBtn('新城区')">
              新城区
            </Button>
          </div>
          <h2 class="mt-[10px] text-[18px] font-bold">{{ currentArea }}</h2>
          <div v-show="currentArea === '中心城区'">
            <div class="region_introduce">
              <div class="title">中心城区：</div>
              <div class="introduce">
                江岸区、江汉区、硚口区、汉阳区、武昌区、青山区、洪山区
              </div>
            </div>
            <div class="region_introduce">
              <div class="title">产业描述：</div>
              <div class="introduce">
                <p>
                  推动中心城区转型提质，大力发展
                  <span class="point_description blue">
                    数字创意、现代金融、软件和信息服务、商贸物流
                  </span>
                  等
                  <span class="point_description black">现代服务业</span>
                  ，实现
                  <span class="point_description black">产业集约集聚发展</span>
                  。
                </p>
              </div>
            </div>
          </div>
          <div v-show="currentArea === '开发区'">
            <div class="region_introduce">
              <div class="title">开发区（功能区）：</div>
              <div class="introduce">
                东湖新技术开发区、武汉经济技术开发区（汉南区）、武汉临空港经济技术开发区（东西湖区）、东湖生态旅游风景区、长江新区
              </div>
            </div>
            <div class="region_introduce">
              <div class="title">产业描述：</div>
              <div class="introduce">
                <p>
                  发挥开发区（功能区）引领作用，重点布局
                  <span class="point_description blue">
                    光电子信息、新能源和智能网联汽车、大健康
                  </span>
                  等
                  <span class="point_description black">科创型产业</span>
                  ，实现
                  <span class="point_description black">集群化突破</span>
                  。
                </p>
              </div>
            </div>
          </div>
          <div v-show="currentArea === '新城区'">
            <div class="region_introduce">
              <div class="title">新城区：</div>
              <div class="introduce">江夏区、蔡甸区、黄陂区、新洲区</div>
            </div>
            <div class="region_introduce">
              <div class="title">产业描述：</div>
              <div class="introduce">
                <p>
                  推进新城区差异化发展，重点布局
                  <span class="point_description blue">
                    汽车零部件、智能装备、生命健康、航空航天
                  </span>
                  等
                  <span class="point_description black">特色型产业</span>
                  ，因地制宜打造
                  <span class="point_description black">新型工业化基地</span>
                  。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <div
      v-for="(item, index) in images"
      :key="index"
      class="mt-[16px]"
      @click="onClickToKeyAreas(item)">
      <img
        :src="item.url"
        alt="" />
    </div> -->
      <!-- 重点分布 -->
      <!-- <div class="bottom">
      <div
        v-for="areaItem in areaData"
        v-show="currentTab === '各区重点产业布局'"
        :key="areaItem.name"
        class="industry_item"
        @click="onClickToKeyAreas(areaItem)">
        <p class="industry_item_title">{{ areaItem.name }}</p>
        <ul>
          <li
            v-for="indItem in areaItem.industries"
            :key="indItem"
            >{{ indItem }}</li
          >
        </ul>
      </div>
    </div> -->
      <div class="bottom">
        <div
          v-for="areaItem in areaData"
          v-show="currentTab === '各区重点产业布局'"
          :key="areaItem.name"
          class="industry_item"
          @click="onClickToKeyAreas(areaItem)">
          {{ areaItem.name }}
        </div>
      </div>

      <!-- <Tabs v-model:active="activeTab">
      <Tab
        title="产业图谱"
        name="产业图谱">
        <div class="w-full bg-[#fff] px-[16px]">
          <div v-if="!loading">
            <Title class="mt-[12px]">产业图谱</Title>

            <img
              v-if="industryInfo?.images && industryInfo?.images.length > 0"
              :src="industryInfo?.images[0].url"
              class="mt-[12px] h-[180px] w-full"
              alt=""
              @click="photoShow = true" />
          </div>
          <div
            v-else
            class="flex justify-center">
            <Loading></Loading> </div></div
      ></Tab>
      <Tab
        title="产业地图"
        name="产业地图">
        <div class="content">
          <div class="bg-[#fff] px-[16px]">
            <Title>产业地图</Title>
            <img
              src="@/assets/images/cygk/hot_big.png"
              class="mt-[10px] h-[280px] w-full"
              alt=""
              @click="show = true" />
          </div>
        </div>
      </Tab>
    </Tabs>
    <div class="px-[16px] pb-[10px]">
      <Title class="mt-[12px]">产业介绍</Title>
      <p class="mt-[12px] indent-[2em] text-[rgb(0,0,0,0.4)]">{{
        industryInfo.positioning?.replaceAll('\\n', '')
      }}</p>
      <p class="mt-[12px] indent-[2em] text-[rgb(0,0,0,0.4)]">{{
        industryInfo.situation?.replaceAll('\\n', '')
      }}</p>
      <p class="mt-[12px] indent-[2em] text-[rgb(0,0,0,0.4)]">{{
        industryInfo.synopsis?.replaceAll('\\n', '')
      }}</p>
    </div> -->
    </div>
  </div>

  <ActionSheet
    v-model:show="showMenu"
    :actions="menuActions"
    cancel-text="取消"
    close-on-click-action
    @select="onSelectType"></ActionSheet>
  <ImagePreview
    v-model:show="show"
    :images="titleImages"
    :close-on-click-image="false"
    :double-scale="true"></ImagePreview>
  <ImagePreview
    v-model:show="photoShow"
    :images="photoShowImages"
    :close-on-click-image="false"
    :double-scale="true"></ImagePreview>
</template>

<script setup>
  import { ActionSheet, Button, ImagePreview, Tab, Tabs } from 'vant'
  import { computed, onMounted, reactive, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { INDUSTRY_LAYOUT } from '../options.js'
  import bottomMap from './images/bottom-map.png'
  import mapArea1 from './images/map-01-area-01.png'
  import mapArea2 from './images/map-01-area-02.png'
  import mapArea3 from './images/map-01-area-03.png'
  import mapH from './images/map-02.png'

  onMounted(() => {
    const localCurrentTab = sessionStorage.getItem('currentTab')
    if (localCurrentTab) {
      if (localCurrentTab === '产业介绍') {
        currentTab.value = '产业总体布局'
        sessionStorage.setItem('currentTab', '产业总体布局')
      } else {
        currentTab.value = localCurrentTab
      }
    }
  })
  const router = useRouter()
  // const currentType = ref('')
  const show = ref(false)
  const showMenu = ref(false)
  // const loading = ref(false)
  // const activeTab = ref('产业图谱')
  const areaData = ref([
    {
      name: '江岸区',
      industries: ['金融保险', '商贸物流', '创意设计', '文化旅游', '生态环保'],
    },
    {
      name: '江汉区',
      industries: ['现代金融', '商贸物流', '商务服务', '通信信息'],
    },
    {
      name: '硚口区',
      industries: ['现代商贸', '健康服务', '科技服务'],
    },
    {
      name: '汉阳区',
      industries: ['大健康', '工程设计与建造', '汽车后市场', '会展'],
    },
    {
      name: '武昌区',
      industries: ['金融服务', '工程设计', '商业商务', '文化旅游'],
    },

    {
      name: '青山区',
      industries: ['钢铁加工', '石化化工', '智能建造'],
    },
    {
      name: '洪山区',
      industries: ['科技服务', '文化创意', '商务服务'],
    },
    {
      name: '蔡甸区',
      industries: [
        '汽车及零部件',
        '电子信息',
        '智能家居及智能制造',
        '文化旅游',
      ],
    },
    {
      name: '江夏区',
      industries: ['汽车及零部件', '大健康', '光电子信息'],
    },
    {
      name: '黄陂区',
      industries: ['新能源', '信息技术', '文化旅游', '珠宝时尚', '装备制造'],
    },
    {
      name: '新洲区',
      industries: ['航运物流', '智能建造', '智能制造', '航空航天'],
    },
    {
      name: '东湖新技术开发区',
      industries: [
        '光芯屏端网',
        '生物医药',
        '人工智能',
        '空天信息',
        '量子科技',
      ],
    },
    {
      name: '武汉经济技术开发区(汉南区)',
      industries: [
        '新能源和智能网联汽车',
        '智慧家居',
        '软件及信息技术',
        '氢能',
        '低空经济',
      ],
    },

    {
      name: '武汉临空港经济技术开发区(东西湖区)',
      industries: [
        '电子信息',
        '食品大健康',
        '智能制造',
        '现代商贸物流',
        '网络安全与大数据',
      ],
    },

    {
      name: '东湖生态旅游风景区',
      industries: ['现代服务业(文化旅游业)', '住宿餐饮业'],
    },

    {
      name: '长江新区',
      industries: [
        '枢纽经济',
        '低碳产业',
        '健康产业',
        '智造产业',
        '现代服务业',
        '现代农业',
      ],
    },
  ])
  const industryInfo = ref({})
  const menuActions = INDUSTRY_LAYOUT.map((m) => ({ name: m.value }))

  const titleImages = reactive([mapH])
  const photoShow = ref(false)
  const photoShowImages = computed(() => {
    if (industryInfo.value?.images && industryInfo.value.images.length > 0) {
      return industryInfo.value.images.map((item) => item.url)
    }
    return []
  })
  const currentArea = ref('中心城区')
  const onClickBtn = (name) => {
    currentArea.value = name
  }
  // const imageFiles = import.meta.glob(
  //   '@/assets/images/cygk/production_images/*.png',
  // )
  // const getImageUrl = (name) => {
  //   return new URL(
  //     `/src/assets/images/cygk/production_images/${name}.png`,
  //     import.meta.url,
  //   ).href
  // }

  // const images = computed(() => {
  //   return Object.keys(imageFiles).map((path) => {
  //     const moduleName = path
  //       .split('/')
  //       .pop()
  //       .replace(/\.\w+$/, '')
  //     return {
  //       url: getImageUrl(moduleName),
  //       name: moduleName,
  //     }
  //   })
  // })
  const onClickToKeyAreas = (detail) => {
    router.push({
      path: '/cygk/key-areas',
      query: {
        key: detail.name,
      },
    })
  }
  const currentTab = ref('产业总体布局')
  const onClickTab = ({ name }) => {
    // if (name === '区域分析') {
    //   // 跳转其他页面
    //   // router.push('/cygk/qyfx')
    //   // const src =
    //   // 'https://isv.cnfic.com.cn/common-login/?appKey=v3-whtcj&appSecret=YL38-KB12-U21M&clientId=user1&redirectUrl=https%3A%2F%2Fisv.cnfic.com.cn%2Fwhtc-h5%2F%23%2Fpages%2Fregion%2Fregion'
    //   // location.href = src
    //   router.push('/cygk/qyfx')
    // }
    // if (name === '产业总体') {
    //   showMenu.value = true
    // }
    sessionStorage.setItem('currentTab', name)
    currentTab.value = name
  }
  const onSelectType = ({ name }) => {
    currentTab.value = name
    sessionStorage.setItem('currentTab', name)
  }
  const currentAreaMap = computed(() => {
    return {
      中心城区: mapArea1,
      开发区: mapArea2,
      新城区: mapArea3,
    }[currentArea.value]
  })
  // const getIndustryDropDown = async () => {
  //   try {
  //     const res = await apis.getIndustryDropDown('park')
  //     if (res.data.data) {
  //       menuActions.value = res.data.data.map((name) => ({ name }))
  //       // menuActions.value = [{ name: '武汉市产业地图' }, ...menuActions.value]
  //       currentType.value = menuActions.value[0].name
  //       // onSearch()
  //     }
  //   } catch (error) {
  //     console.log(error)
  //   }
  // }
  // const onSearch = async () => {
  //   try {
  //     const res = await apis.getKeyDetail('overview', {
  //       industrychain: currentType.value,
  //     })
  //     const { data } = res.data
  //     industryInfo.value = data
  //     const images = JSON.parse(data?.images || '[]')
  //     if (images.length > 0) {
  //       industryInfo.value.images = images.map((item) => ({
  //         ...item,
  //         url: `${import.meta.env.VITE_IMAGES_BASE_URL}${item.value}`,
  //       }))
  //     }
  //   } catch (error) {
  //     console.log(error)
  //   }
  // }
  // onMounted(() => {
  //   getIndustryDropDown()
  // })
</script>

<style lang="scss" scoped>
  .content {
    width: 100%;
    height: 100%;

    .img {
      width: 375px;
      height: 210px;
      background-image: url('@/assets/images/cygk/xxcy_banner.png');
      background-position: center;
      background-size: cover;
      background-repeat: no-repeat;
    }

    .card {
      width: 343px;
      height: 124px;
      margin: 0 auto;
      margin-top: -30px;
      padding: 16px;
      border-radius: 12px;
      background-color: #fff;
    }
  }

  .bottom {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 16px;
    gap: 12px;
    background-color: #f7f8fa;

    .white_box {
      padding: 16px;
      border-radius: 12px;
      background-color: #fff;

      .button_list {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .btn {
          color: #000 !important;
          font-weight: bold;
          font-size: 14px;
        }

        .active {
          border: 2px solid #1255e4 !important;
          background-color: #fff !important;
          color: #1255e4 !important;
        }
      }

      .region_introduce {
        display: flex;
        margin-top: 8px;
        gap: 10px;
        font-size: 14px;

        .title {
          min-width: 80px;
          color: rgb(0 0 0 / 60%);
        }

        .introduce {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          color: rgb(0 0 0 / 40%);

          .point_description {
            font-weight: bold;
            font-size: 14px;
          }

          .blue {
            color: #0256ff;
          }

          .black {
            color: #1a1a1a;
          }
        }
      }
    }
  }

  // .content {
  //   width: 100%;
  //   height: 100%;
  //   padding-bottom: 20px;

  //   .img {
  //     width: 375px;
  //     height: 210px;
  //     background-image: url('@/assets/images/cygk/xxcy_banner.png');
  //     background-position: center;
  //     background-size: cover;
  //     background-repeat: no-repeat;
  //   }
  // }

  .currentTitle {
    color: #333;
  }

  .industry_item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 165px;
    height: 68px;

    // margin-bottom: 16px;
    padding: 16px;
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 0 10px 0 rgb(0 0 0 / 5%);
    font-size: 14px;

    &_title {
      position: relative;
      margin-bottom: 8px;
      padding-left: 8px;
      color: #3d3d3d;
      font-weight: 600;
      font-size: 16px;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 4px;
        height: 16px;
        transform: translateY(-50%);
        border-radius: 16px;
        background: #1255e4;
        content: '';
      }
    }

    ul {
      padding-left: 20px;

      li {
        position: relative;
        margin-bottom: 5px;
        color: #000000e6;
        font-weight: 500;
        font-size: 14px;
        line-height: 22px;

        &::before {
          position: absolute;
          top: 50%;
          left: -12px;
          width: 4px;
          height: 4px;
          transform: translateY(-50%);
          border-radius: 50%;
          background: #000000e6;
          content: '';
        }
      }
    }
  }
</style>
