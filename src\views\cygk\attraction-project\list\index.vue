<template>
  <div
    ref="listRef"
    class="project_page">
    <!-- banner -->
    <div class="banner">
      <img
        src="@/assets/images/cygk/project_list_banner.png"
        alt=""
        class="banner_image" />
    </div>
    <div class="content">
      <div class="title">
        <div class="title_text"> 项目 </div>
        <div class="sum">
          <span>共计</span>
          <span style="color: #1255e4">{{ paginate[total] }}</span>
          <span>条</span>
        </div>
      </div>

      <div class="list">
        <div
          v-for="(item, index) in projectList"
          :key="index"
          class="card"
          @click="toDetail(item.id)">
          <div class="card_title">
            <div class="card_title_text">{{ item.name }}</div>
            <div class="card_title_tag">{{ item.district }}</div>
          </div>
          <div class="card_content">
            <TextEllipsis
              rows="3"
              :content="item.conditions" />
          </div>
          <div class="card_money">
            <img
              class="card_money_icon"
              src="@/assets/images/cygk/project_money_icon.png"
              alt="" />
            <div class="card_money_text">{{ item.amount }}</div>
          </div>
        </div>
        <div
          v-show="paginate[total] === 0 && !loading"
          style="text-align: center"
          class="card">
          暂无数据
        </div>

        <Loading
          v-show="loading"
          class="card loading">
          加载中...
        </Loading>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Loading, TextEllipsis } from 'vant'
import { onBeforeUnmount, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import { getPageList } from '@/apis'

const router = useRouter()
const route = useRoute()
const listRef = ref(null)
const { key } = route.query

const total = Symbol()
const paginate = ref({
  pageNum: 1,
  pageSize: 10,
  [total]: 0,
})

const loading = ref(false)

const projectList = ref([])

async function getPageData() {
  try {
    loading.value = true
    const param = {
      page: paginate.value,
      params: {
        industrychain: key,
      },
    }
    const {
      data: { data: res },
    } = await getPageList('projects', param)
    projectList.value.push(...res.list)
    paginate.value[total] = res.total
    paginate.value.pageNum++
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

function onDownLoad(e) {
  if (loading.value || projectList.value.length >= paginate.value[total]) return
  let scrollTop = e.target.scrollTop
  let scrollHeight = e.target.scrollHeight
  let offsetHeight = Math.ceil(e.target.getBoundingClientRect().height)
  let currentHeight = scrollTop + offsetHeight
  if (currentHeight >= scrollHeight) {
    getPageData()
  }
}

onMounted(() => {
  getPageData()
  listRef.value.addEventListener('scroll', onDownLoad)
})

onBeforeUnmount(() => {
  listRef.value.removeEventListener('scroll', onDownLoad)
})

function toDetail(id) {
  router.push(`/cygk/project-detail?id=${id}`)
}
</script>

<style lang="scss" scoped>
.project_page {
  background: #f7f8fa;
  height: 100%;
  overflow-y: auto;

  .banner {
    height: 153px;

    .banner_image {
      width: 100%;
      height: 153px;
      object-fit: cover;
    }
  }

  .content {
    padding: 0 16px;
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .title_text {
        font-weight: 600;
        font-size: 16px;
        color: rgba(50, 50, 51, 0.9);
        line-height: 24px;
        display: flex;
        align-items: center;

        &::before {
          display: block;
          content: ' ';
          width: 4px;
          height: 16px;
          background: #1255e4;
          border-radius: 16px;
          margin-right: 4px;
        }
      }

      .sum {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.4);
        line-height: 20px;
      }
    }

    .list {
      margin-top: 18px;

      .card {
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.05);
        border-radius: 12px 12px 12px 12px;
        padding: 16px;
        margin-bottom: 16px;

        .card_title {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .card_title_text {
            font-weight: 600;
            font-size: 18px;
            color: rgba(0, 0, 0, 0.9);
            line-height: 26px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .card_title_tag {
            font-size: 12px;
            color: #1989fa;
            line-height: 16px;
            background: rgba(25, 137, 250, 0.1);
            border-radius: 2px 2px 2px 2px;
            padding: 1px 4px;
            flex-shrink: 0;
          }
        }

        .card_content {
          margin-top: 8px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          line-height: 22px;
        }

        .card_money {
          flex-grow: 0;
          margin-top: 8px;
          display: inline-flex;
          align-items: center;
          padding: 2px 8px;
          background: rgba(25, 137, 250, 0.1);
          border-radius: 26px 26px 26px 26px;

          .card_money_icon {
            width: 16px;
            height: 16px;
          }

          .card_money_text {
            font-size: 14px;
            color: #1989fa;
            line-height: 22px;
            margin-left: 4px;
          }
        }

        &.loading {
          text-align: center;
        }
      }
    }
  }
}
</style>
