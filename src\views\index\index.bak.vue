<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="index px-[16px] pb-[16px] pt-[183px]">
    <div
      v-for="(item, index) in items"
      :key="index"
      class="mb-[16px] flex h-[90px] items-center justify-between rounded-[12px] bg-white px-[16px]"
      @click="item.onClick">
      <div
        class="flex h-[48px] w-[48px] items-center justify-center rounded-[50%] bg-gradient-to-br text-white"
        :class="item.iconBg">
        <i
          class="iconfont text-[25px]"
          :class="item.icon" />
      </div>
      <p class="mx-[12px] flex-1 text-[18px] font-semibold">
        {{ item.title }}
      </p>
      <i class="iconfont icon-RightCircle text-[#000]/[.26]" />
    </div>
  </div>
</template>

<script setup>
  import { useRoute, useRouter } from 'vue-router'

  const router = useRouter()
  const route = useRoute()

  const items = [
    {
      icon: 'icon-industry',
      iconBg: 'from-[#FEC965] to-[#F6AB51]',
      title: '产业概况',
      onClick: () => {
        router.push('/cygk/industry')
      },
    },
    {
      icon: 'icon-enterprise',
      iconBg: 'from-[#71DBFD] to-[#41AFFF]',
      title: '重点企业',
      onClick: () => {
        window.open(
          `https://whjzzs.gzjp.cn/uniapp/#/zhaoshang/industryChainAnalysis/industrialList?comp=ChainEnterprise&username=${route.query.username}&password=${route.query.password}`,
          // `https://whjzzs.gzjp.cn/uniapp/#/zhaoshang/targetEnterprise/newChainEnterprise?username=${route.query.username}&password=${route.query.password}`,
        )
      },
    },
    {
      icon: 'icon-policy',
      iconBg: 'from-[#8EBAFF] to-[#5177F6]',
      title: '产业政策',
      onClick: () => {
        router.push('/cygk/policy-list')
      },
    },
    {
      icon: 'icon-Park',
      iconBg: 'from-[#FFA77B] to-[#F77F59]',
      title: '产业载体',
      onClick: () => {
        router.push('/cygk/introduce')
      },
    },
    {
      icon: 'icon-mastermind',
      iconBg: 'from-[#8FA0FF] to-[#634EEA]',
      title: '招引目标',
      onClick: () => {
        window.open(
          `https://whjzzs.gzjp.cn/uniapp/#/zhaoshang/industryChainAnalysis/industrialList?comp=Enterprise&username=${route.query.username}&password=${route.query.password}`,
          // `https://whjzzs.gzjp.cn/uniapp/#/zhaoshang/targetEnterprise/newEnterprise?username=${route.query.username}&password=${route.query.password}`,
        )
      },
    },
  ]
</script>

<style lang="scss" scoped>
  .index {
    min-height: 100%;
    background-color: #f7f8fa;
    background-image: url('@/assets/images/index/banner.png');
    background-size: 100%;
    background-repeat: no-repeat;
  }
</style>
