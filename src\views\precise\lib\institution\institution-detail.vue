<template>
  <div class="w-full h-full bg-[#f7f8fa] p-[16px]">
    <div class="card font-[600] text-[16px]">{{ info.name }} </div>
    <div class="card">
      <div class="card_item">
        <div class="label">重点领域：</div>
        <div class="value">{{ info.importantInvestmentArea }}</div>
      </div>
      <div class="card_item">
        <div class="label">所在城市：</div>
        <div class="value">{{ info.city }}</div>
      </div>
      <div class="card_item">
        <div class="label">联系方式：</div>
        <div class="value">{{ info.contactPhone }}</div>
      </div>
    </div>
    <div class="card">
      <div class="label">简介：</div>
      <div class="value introduction mt-[12px]">
        {{ info.introduction || '暂无简介' }}
      </div>
    </div>
  </div>
</template>

<script setup>
  import { institutionDetail } from '@/apis/precise'
  import { onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const info = ref({})

  async function initDetail() {
    try {
      const {
        data: { data: res },
      } = await institutionDetail(route.query.id)

      info.value = res
    } catch (error) {
      console.log(error)
    }
  }

  onMounted(() => {
    initDetail()
  })
</script>

<style lang="scss" scoped>
  .card {
    padding: 16px;
    background-color: #fff;

    & + .card {
      margin-top: 16px;
    }

    .card_item {
      display: flex;
    }

    .card_item + .card_item {
      margin-top: 6px;
    }

    .label {
      width: 100px;
    }

    .value {
      color: rgb(0 0 0 / 40%);

      &.introduction {
        white-space: pre-wrap;
      }
    }
  }
</style>
