import { defineMock } from '@alova/mock'

// 模拟数据 - 假设有100条待办事项
const mockTodos = Array.from({ length: 100 }, (_, i) => ({
  id: i + 1,
  title: `待办事项 ${i + 1}`,
  time: new Date(Date.now() - i * 3600000).toLocaleTimeString(),
  completed: Math.random() > 0.5,
}))

const visitMock = defineMock(
  {
    '/getOrgsPages/list': ({ query }) => {
      const page = parseInt(query.page) || 1
      const pageSize = parseInt(query.pageSize) || 10

      // 计算分页数据
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const data = mockTodos.slice(start, end)

      return {
        code: 0,
        data: {
          page,
          pageSize,
          total: mockTodos.length,
          list: data,
          hasNext: end < mockTodos.length,
        },
        msg: '成功',
      }
    },
    '/visitEnterpriseDatabase/userInfo': () => {
      return {
        code: 0,
        data: {
          uid: '1',
          userName: '测试',
          areaName: '洪山区',
          areaCode: '420100',
          roles: [
            {
              // 对象数组中某一对象的roleName是zwbzq区，zwbzqsj市
              code: 'zwbzq',
            },
            {
              // 对象数组中某一对象的roleName是zwbzq区，zwbzqsj市
              code: 'zwbzqsj',
            },
          ],
        },
        msg: '成功',
      }
    },
  },
  false,
)

export { visitMock }
