<template>
  <div
    v-if="loading"
    class="w-full h-full flex items-center justify-center">
    加载中...
  </div>
  <MdPreview
    v-else
    id="preview-only"
    class="p-[16px]"
    :model-value="text" />
</template>

<script setup>
  import { getSchemeDetail } from '@/apis/precise'
  import { MdPreview } from 'md-editor-v3'
  import 'md-editor-v3/lib/preview.css'
  import { onMounted, ref } from 'vue'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  const text = ref('')
  const loading = ref(true)

  async function getDetail() {
    loading.value = true

    try {
      const { data } = await getSchemeDetail({
        task_id: route.query.id,
      })

      text.value = data
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    getDetail()
  })
</script>
