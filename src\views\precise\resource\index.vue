<template>
  <div class="resource">
    <div class="reactive">
      <img
        width="100%"
        height="120px"
        src="@/assets/images/precise/zszy.png" />
      <!-- <img
        src="@/assets/images/precise/fenqu.png"
        width="96"
        height="28"
        class="top-[104px] left-[24px] absolute"
        alt=""
        @click="jumpTo('/precise/statistics')" /> -->
    </div>
    <div class="content">
      <div
        v-for="(item, index) in buttons"
        :key="index"
        class="content_button"
        @click="jumpTo(item.path)">
        <div class="flex items-center">
          <div
            class="flex h-[36px] w-[36px] flex-shrink-0 items-center justify-center rounded-[8px] bg-white">
            <i
              class="iconfont text-[20px]"
              :style="item.style"
              :class="[item.icon]"></i>
          </div>
          <span class="ml-[8px] flex-shrink-0">{{ item.title }}</span>
        </div>
        <div
          v-show="item.hasNumber"
          class="number">
          {{ item.number || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { useResourceStore } from '@/stores/resource'
  import { getTextGradient } from '@/utils'
  import { useCount } from '@/utils/precise'
  import { computed, onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const resourceStore = useResourceStore()

  const countObj = ref({})

  const countAll = [
    'expert',
    'policy',
    'park',
    'building',
    'scene',
    'investment',
    'innovate',
    'research',
    'colleges',
  ]

  onMounted(() => {
    Promise.all(
      countAll.map((item) => {
        useCount(item, {
          onSuccess({ data: { data: res } }) {
            countObj.value[item] = res
          },
        })
      }),
    )
  })

  const buttons = computed(() => [
    // 产业概况 暂时注释
    // {
    //   icon: 'icon-industry',
    //   title: '产业概况',
    //   style: getTextGradient(['#69D9F4', '#54B5F6'], '135deg'),
    //   path: '/cygk/industry',
    // },
    {
      icon: 'icon-Park',
      title: '产业园区',
      style: getTextGradient(['#FFCA65', '#F6AB52'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'park',
          key: '产业园区',
        },
      },
      hasNumber: true,
      number: countObj.value?.park,
    },
    {
      icon: 'icon-enterprise',
      title: '商务楼宇',
      style: getTextGradient(['#78F1D9', '#2BF4C3'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'building',
          key: '商务楼宇',
        },
      },
      hasNumber: true,
      number: countObj.value?.building,
    },
    {
      icon: 'icon-policy',
      title: '产业政策',
      style: getTextGradient(['#74A8FE', '#547AF7'], '135deg'),
      path: '/cygk/policy-list',
      hasNumber: true,
      number: countObj.value?.policy,
    },

    {
      icon: 'icon-resource',
      title: '应用场景',
      style: getTextGradient(['#D8B0FF', '#A953FF'], '135deg'),
      path: {
        path: '/cygk/introduce-info',
        query: {
          type: 'scene',
          key: '应用场景',
        },
      },
      hasNumber: true,
      number: countObj.value?.scene,
    },
    // {
    //   icon: 'icon-cxzy',
    //   title: '创新资源',
    //   style: getTextGradient(['#8396FF', '#634EEA'], '135deg'),
    //   path: '/precise/innovate',
    //   hasNumber: true,
    //   number:
    //     (+countObj.value?.innovate || 0) +
    //     (+countObj.value?.research || 0) +
    //     (+countObj.value?.colleges || 0),
    // },
    {
      icon: 'icon-jgllk',
      title: '机构联络库',
      style: getTextGradient(['#69D9F4', '#54B5F6'], '135deg'),
      path: '/precise/institution',
      hasNumber: true,
      number: countObj.value?.investment,
    },
    {
      icon: 'icon-ypzjk',
      title: '研判专家库',
      style: getTextGradient(['#FFCA65', '#F6AB52'], '135deg'),
      path: '/precise/except',
      hasNumber: true,
      number: countObj.value?.expert,
    },
  ])

  function jumpTo(path) {
    router.push(typeof path === 'string' ? { path } : path)
  }

  onMounted(() => {
    resourceStore.clean()
  })
</script>

<style lang="scss" scoped>
  .resource {
    width: 100%;
    height: 100%;
    background: #f7f8fa;

    .content {
      display: grid;
      grid-template-columns: 1fr;
      padding: 16px;
      gap: 12px;
      font-size: 14px;

      .content_button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 23px 12px;
        border: 1px solid #fff;
        border-radius: 10px;
        background: linear-gradient(135deg, #eef8ff 0%, #fff 100%);

        .number {
          color: rgb(0 0 0 / 60%);
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
</style>
